# Klaviyo OAuth Deployment Configuration Documentation

This document details the deployment configuration and environment setup for the Klaviyo OAuth integration across all services.

## Table of Contents

- [Overview](#overview)
- [Environment Variables](#environment-variables)
- [Service Configuration](#service-configuration)
- [Security Configuration](#security-configuration)
- [Monitoring and Logging](#monitoring-and-logging)
- [Deployment Pipeline](#deployment-pipeline)
- [Production Considerations](#production-considerations)

## Overview

The Klaviyo OAuth integration requires proper configuration across multiple services and environments:

- **Development**: Local development setup
- **Staging**: Pre-production testing environment
- **Production**: Live production environment

Each environment has specific configuration requirements for security, performance, and monitoring.

## Environment Variables

### Required Variables

#### Backend Service (om-backend)

```bash
# Klaviyo OAuth Configuration
KLAVIYO_CLIENT_ID=your_client_id_here
KLAVIYO_CLIENT_SECRET=your_client_secret_here
KLAVIYO_API_VERSION=2025-04-15

# Application URLs
om_backend_url=https://backend.yourdomain.com
om_new_admin_url=https://admin.yourdomain.com

# Database Configuration
mongo_url=mongodb://localhost:30000/optimonk2
dds_url=mongodb://localhost:30000/dds

# Redis Configuration
redis_host_user=localhost:6379

# Security Keys
om_shared_key=your_shared_key_here
editor_key=your_editor_key_here
magic_link_key=your_magic_link_key_here
```

#### Frontend Service (om-frontend)

```bash
# API Endpoints
BACKEND_URL=https://backend.yourdomain.com
ADMIN_URL=https://admin.yourdomain.com

# Authentication
BACKEND_AUTH_USERNAME=your_backend_username
BACKEND_AUTH_PASSWORD=your_backend_password

# Redis Configuration
REDIS_HOST=localhost
REDIS_EXPIRE=30

# Environment
ENVIRONMENT=production
```

#### Admin Interface (om-admin)

```bash
# API Configuration
VUE_APP_API_URL=https://backend.yourdomain.com
VUE_APP_ADMIN_URL=https://admin.yourdomain.com

# OAuth Configuration
VUE_APP_KLAVIYO_CLIENT_ID=your_client_id_here

# Environment
NODE_ENV=production
VUE_APP_ENV=production
```

### Optional Variables

```bash
# Klaviyo API Configuration
KLAVIYO_RATE_LIMIT_REQUESTS=150
KLAVIYO_RATE_LIMIT_WINDOW=60
KLAVIYO_REQUEST_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
NEW_RELIC_LICENSE_KEY=your_newrelic_key_here

# Feature Flags
KLAVIYO_OAUTH_ENABLED=true
KLAVIYO_BULK_IMPORT_ENABLED=true
KLAVIYO_INSTANT_CONSENT_ENABLED=true
```

## Service Configuration

### Backend Service Configuration

#### Docker Configuration

```dockerfile
# Dockerfile for om-backend
FROM node:16-slim

WORKDIR /app

# Copy package files
COPY package.json yarn.lock ./
COPY .yarn .yarn

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Set environment
ENV NODE_ENV=production

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["yarn", "start"]
```

#### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: om-backend
  labels:
    app: om-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: om-backend
  template:
    metadata:
      labels:
        app: om-backend
    spec:
      containers:
      - name: om-backend
        image: your-registry/om-backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: KLAVIYO_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: klaviyo-oauth-secrets
              key: client-id
        - name: KLAVIYO_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: klaviyo-oauth-secrets
              key: client-secret
        - name: mongo_url
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: mongodb-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Service Configuration

```yaml
apiVersion: v1
kind: Service
metadata:
  name: om-backend-service
spec:
  selector:
    app: om-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
```

### Frontend Service Configuration

#### Nginx Configuration

```nginx
# nginx.conf for om-frontend
server {
    listen 80;
    server_name frontend.yourdomain.com;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Static files
    location /static/ {
        alias /app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://om-backend-service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Main application
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### Admin Interface Configuration

#### Build Configuration

```javascript
// vue.config.js
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/admin/' : '/',
  
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          klaviyo: {
            test: /[\\/]src[\\/]components[\\/]IntegrationModals[\\/]klaviyoOAuth[\\/]/,
            name: 'klaviyo-oauth',
            chunks: 'all',
          }
        }
      }
    }
  },
  
  chainWebpack: config => {
    // Production optimizations
    if (process.env.NODE_ENV === 'production') {
      config.plugin('html').tap(args => {
        args[0].minify = {
          removeComments: true,
          collapseWhitespace: true,
          removeAttributeQuotes: true
        };
        return args;
      });
    }
  }
};
```

## Security Configuration

### Secrets Management

#### Kubernetes Secrets

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: klaviyo-oauth-secrets
type: Opaque
data:
  client-id: <base64-encoded-client-id>
  client-secret: <base64-encoded-client-secret>
---
apiVersion: v1
kind: Secret
metadata:
  name: database-secrets
type: Opaque
data:
  mongodb-url: <base64-encoded-mongodb-url>
  redis-url: <base64-encoded-redis-url>
```

#### Environment-Specific Secrets

```bash
# Development
kubectl create secret generic klaviyo-oauth-secrets \
  --from-literal=client-id=dev_client_id \
  --from-literal=client-secret=dev_client_secret \
  --namespace=development

# Staging
kubectl create secret generic klaviyo-oauth-secrets \
  --from-literal=client-id=staging_client_id \
  --from-literal=client-secret=staging_client_secret \
  --namespace=staging

# Production
kubectl create secret generic klaviyo-oauth-secrets \
  --from-literal=client-id=prod_client_id \
  --from-literal=client-secret=prod_client_secret \
  --namespace=production
```

### Network Security

#### Network Policies

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: om-backend-network-policy
spec:
  podSelector:
    matchLabels:
      app: om-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: om-frontend
    - podSelector:
        matchLabels:
          app: om-admin
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS to Klaviyo API
    - protocol: TCP
      port: 27017  # MongoDB
    - protocol: TCP
      port: 6379   # Redis
```

### SSL/TLS Configuration

#### Certificate Management

```yaml
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: klaviyo-oauth-tls
spec:
  secretName: klaviyo-oauth-tls-secret
  issuer:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - backend.yourdomain.com
  - admin.yourdomain.com
  - frontend.yourdomain.com
```

## Monitoring and Logging

### Application Monitoring

#### Health Checks

```javascript
// Backend health check endpoint
app.get('/health', async (req, res) => {
  try {
    // Check database connection
    await mongoose.connection.db.admin().ping();
    
    // Check Redis connection
    await redis.ping();
    
    // Check Klaviyo API connectivity
    const klaviyoHealth = await checkKlaviyoHealth();
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'up',
        redis: 'up',
        klaviyo: klaviyoHealth ? 'up' : 'down'
      }
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

#### Metrics Collection

```javascript
// Prometheus metrics
const promClient = require('prom-client');

const klaviyoRequestCounter = new promClient.Counter({
  name: 'klaviyo_oauth_requests_total',
  help: 'Total number of Klaviyo OAuth requests',
  labelNames: ['method', 'endpoint', 'status']
});

const klaviyoRequestDuration = new promClient.Histogram({
  name: 'klaviyo_oauth_request_duration_seconds',
  help: 'Duration of Klaviyo OAuth requests',
  labelNames: ['method', 'endpoint']
});

// Middleware to collect metrics
app.use('/integrationOAuth/klaviyoOAuth', (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    
    klaviyoRequestCounter
      .labels(req.method, req.path, res.statusCode)
      .inc();
      
    klaviyoRequestDuration
      .labels(req.method, req.path)
      .observe(duration);
  });
  
  next();
});
```

### Logging Configuration

#### Structured Logging

```javascript
// Winston logger configuration
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'klaviyo-oauth',
    version: process.env.APP_VERSION
  },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({
      filename: 'logs/klaviyo-oauth.log',
      maxsize: 10485760, // 10MB
      maxFiles: 5
    })
  ]
});

// Log OAuth events
logger.info('OAuth authorization initiated', {
  userId: req.user.id,
  integrationId: req.query.integrationId,
  timestamp: new Date().toISOString()
});
```

#### Log Aggregation

```yaml
# Fluentd configuration for log collection
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/*klaviyo*.log
      pos_file /var/log/fluentd-klaviyo.log.pos
      tag kubernetes.klaviyo
      format json
    </source>
    
    <filter kubernetes.klaviyo>
      @type parser
      key_name log
      format json
      reserve_data true
    </filter>
    
    <match kubernetes.klaviyo>
      @type elasticsearch
      host elasticsearch.logging.svc.cluster.local
      port 9200
      index_name klaviyo-oauth
      type_name _doc
    </match>
```

## Deployment Pipeline

### CI/CD Configuration

#### GitLab CI Pipeline

```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_REGISTRY: your-registry.com
  KUBECTL_VERSION: "1.21.0"

# Test stage
test:klaviyo-oauth:
  stage: test
  image: node:16
  script:
    - yarn install
    - yarn test:klaviyo-oauth
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml

# Build stage
build:backend:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  script:
    - docker build -t $DOCKER_REGISTRY/om-backend:$CI_COMMIT_SHA packages/om-backend/
    - docker push $DOCKER_REGISTRY/om-backend:$CI_COMMIT_SHA
  only:
    changes:
      - packages/om-backend/**/*

# Deploy stage
deploy:production:
  stage: deploy
  image: bitnami/kubectl:$KUBECTL_VERSION
  script:
    - kubectl set image deployment/om-backend om-backend=$DOCKER_REGISTRY/om-backend:$CI_COMMIT_SHA
    - kubectl rollout status deployment/om-backend
  environment:
    name: production
    url: https://backend.yourdomain.com
  when: manual
  only:
    - main
```

### Deployment Scripts

#### Kubernetes Deployment Script

```bash
#!/bin/bash
# deploy-klaviyo-oauth.sh

set -e

ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}

echo "Deploying Klaviyo OAuth integration to $ENVIRONMENT..."

# Update image tags
kubectl patch deployment om-backend \
  -p '{"spec":{"template":{"spec":{"containers":[{"name":"om-backend","image":"your-registry/om-backend:'$VERSION'"}]}}}}' \
  -n $ENVIRONMENT

kubectl patch deployment om-frontend \
  -p '{"spec":{"template":{"spec":{"containers":[{"name":"om-frontend","image":"your-registry/om-frontend:'$VERSION'"}]}}}}' \
  -n $ENVIRONMENT

kubectl patch deployment om-admin \
  -p '{"spec":{"template":{"spec":{"containers":[{"name":"om-admin","image":"your-registry/om-admin:'$VERSION'"}]}}}}' \
  -n $ENVIRONMENT

# Wait for rollout
kubectl rollout status deployment/om-backend -n $ENVIRONMENT
kubectl rollout status deployment/om-frontend -n $ENVIRONMENT
kubectl rollout status deployment/om-admin -n $ENVIRONMENT

echo "Deployment completed successfully!"
```

## Production Considerations

### Performance Optimization

#### Redis Configuration

```redis
# redis.conf for production
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
tcp-keepalive 300
timeout 0
```

#### Database Optimization

```javascript
// MongoDB connection optimization
const mongoOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferMaxEntries: 0,
  bufferCommands: false
};

mongoose.connect(process.env.mongo_url, mongoOptions);
```

### Scaling Configuration

#### Horizontal Pod Autoscaler

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: om-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: om-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Backup and Recovery

#### Database Backup

```bash
#!/bin/bash
# backup-klaviyo-oauth.sh

BACKUP_DIR="/backups/klaviyo-oauth"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup MongoDB
mongodump --uri="$MONGODB_URI" --out="$BACKUP_DIR/mongodb_$DATE"

# Backup Redis
redis-cli --rdb "$BACKUP_DIR/redis_$DATE.rdb"

# Compress backups
tar -czf "$BACKUP_DIR/klaviyo_oauth_backup_$DATE.tar.gz" \
  "$BACKUP_DIR/mongodb_$DATE" \
  "$BACKUP_DIR/redis_$DATE.rdb"

# Clean up old backups (keep last 7 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: klaviyo_oauth_backup_$DATE.tar.gz"
```

### Disaster Recovery

#### Recovery Procedures

```bash
#!/bin/bash
# restore-klaviyo-oauth.sh

BACKUP_FILE=$1
RESTORE_DIR="/tmp/restore"

if [ -z "$BACKUP_FILE" ]; then
  echo "Usage: $0 <backup_file>"
  exit 1
fi

# Extract backup
mkdir -p $RESTORE_DIR
tar -xzf "$BACKUP_FILE" -C $RESTORE_DIR

# Restore MongoDB
mongorestore --uri="$MONGODB_URI" --drop $RESTORE_DIR/mongodb_*/

# Restore Redis
redis-cli FLUSHALL
redis-cli --rdb $RESTORE_DIR/redis_*.rdb

echo "Restore completed from $BACKUP_FILE"
```
