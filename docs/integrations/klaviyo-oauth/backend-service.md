# Klaviyo OAuth Backend Service Documentation

This document details the backend implementation of the Klaviyo OAuth integration in the `om-backend` package.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Components](#components)
- [API Endpoints](#api-endpoints)
- [Service Classes](#service-classes)
- [Error Handling](#error-handling)
- [Configuration](#configuration)
- [Development](#development)

## Overview

The backend service handles:
- OAuth 2.0 authentication flow with PKCE
- Token management and refresh
- API communication with Klaviyo
- Data persistence and caching
- Error handling and logging

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    om-backend Package                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ OAuth Controller│    │ Integration     │                │
│  │                 │    │ Controller      │                │
│  │ • /auth         │◄───┤ • Route setup   │                │
│  │ • /callback     │    │ • Middleware    │                │
│  │ • /install      │    └─────────────────┘                │
│  └─────────────────┘                                        │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Service Adapter │    │ API Utilities   │                │
│  │                 │    │                 │                │
│  │ • Token mgmt    │◄───┤ • Error handling│                │
│  │ • API calls     │    │ • Response parse│                │
│  │ • Data format   │    │ • Token refresh │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Constants       │    │ Error Classes   │                │
│  │                 │    │                 │                │
│  │ • API endpoints │    │ • KlaviyoError  │                │
│  │ • OAuth config  │    │ • Log formatting│                │
│  │ • Redis keys    │    │ • Error types   │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Components

### 1. OAuth Controller

**File**: `controllers/oauth/klaviyoOAuth.js`

Handles HTTP endpoints for OAuth flow.

<augment_code_snippet path="packages/om-backend/controllers/oauth/klaviyoOAuth.js" mode="EXCERPT">
````javascript
const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const querystring = require('querystring');
const { model: AccountModel } = require('../../resources/account/account.model');
const { tokenHeader } = require('../../util/jwt');
const { saveToDB, decodeData } = require('../../util/oauth');
const redis = require('../../services/ioRedisAdapter');
````
</augment_code_snippet>

#### Key Functions:

- **`generateCodes()`**: Creates PKCE code verifier and challenge
- **`getAuthURL()`**: Constructs Klaviyo authorization URL
- **`getAuth()`**: Exchanges authorization code for tokens

### 2. Service Adapter

**File**: `services/integrations/klaviyoOAuth.js`

Main service class for Klaviyo API operations.

<augment_code_snippet path="packages/om-backend/services/integrations/klaviyoOAuth.js" mode="EXCERPT">
````javascript
class KlaviyoOAuthAdapter {
  constructor({ access_token, refresh_token, expires_at }) {
    this.accessToken = access_token;
    this.refreshToken = refresh_token;
    this.expiresAt = new Date(expires_at);

    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        Accept: 'application/json',
        revision: API_VERSION,
      },
    });
````
</augment_code_snippet>

#### Key Methods:

- **`isTokenGood()`**: Checks token validity and refreshes if needed
- **`getNewToken()`**: Refreshes access token using refresh token
- **`getLists()`**: Retrieves email lists from Klaviyo
- **`getSegments()`**: Retrieves audience segments
- **`ping()`**: Tests API connectivity

### 3. API Utilities

**File**: `services/integrations/klaviyo/apiUtils.js`

Shared utilities for consistent API handling.

<augment_code_snippet path="packages/om-backend/services/integrations/klaviyo/apiUtils.js" mode="EXCERPT">
````javascript
/**
 * Execute an API request with consistent error handling
 * @param {Function} requestFn - Function that returns a promise for the API request
 * @param {Object} options - Options for error handling
 * @param {string} options.context - Context for error logging
 * @param {Function} options.logger - Logger function
 * @param {boolean} [options.returnFalseOnError=false] - Whether to return false on error instead of throwing
 */
async function executeApiRequest(requestFn, { context, logger, returnFalseOnError = false }) {
````
</augment_code_snippet>

#### Key Functions:

- **`executeApiRequest()`**: Wraps API calls with error handling
- **`safeParseResponse()`**: Safely parses API responses
- **`handleTokenRefresh()`**: Manages token refresh operations

### 4. Error Handling

**File**: `services/integrations/klaviyo/KlaviyoError.js`

Custom error class for Klaviyo-specific errors.

<augment_code_snippet path="packages/om-backend/services/integrations/klaviyo/KlaviyoError.js" mode="EXCERPT">
````javascript
class KlaviyoError extends Error {
  constructor(message, options = {}) {
    super(message);
    this.name = 'KlaviyoError';

    // Store original error if provided
    if (options.originalError) {
      this.originalError = options.originalError;
      this.stack = options.originalError.stack;
    }
````
</augment_code_snippet>

#### Features:

- **Structured Logging**: Consistent error format for logs
- **Context Awareness**: Includes operation context
- **Axios Integration**: Converts Axios errors to Klaviyo errors
- **Error Categorization**: Different error types for different scenarios

### 5. Constants

**File**: `services/integrations/klaviyo/constants.js`

Centralized configuration and constants.

<augment_code_snippet path="packages/om-backend/services/integrations/klaviyo/constants.js" mode="EXCERPT">
````javascript
// API configuration
const API_VERSION = process.env.KLAVIYO_API_VERSION || '2025-04-15';
const BASE_URL = 'https://a.klaviyo.com';
const API_BASE_URL = `${BASE_URL}/api`;
const TOKEN_ENDPOINT = `${BASE_URL}/oauth/token`;
const AUTH_ENDPOINT = 'https://www.klaviyo.com/oauth/authorize';

// OAuth credentials
const CLIENT_ID = process.env.KLAVIYO_CLIENT_ID || 'fc5b62d5-e7f8-47a9-8803-b114a4dbfd16';
const CLIENT_SECRET = process.env.KLAVIYO_CLIENT_SECRET || 'wXmjS2loK5GMj-uLUm4zD9vz6Dd-SsL10Ovlxxi1b-bM3BV7uVAe9lPTKSLQjBZlBY-ZbP9skAo_IWdHYWpghg';
````
</augment_code_snippet>

## API Endpoints

### OAuth Flow Endpoints

#### GET `/integrationOAuth/klaviyoOAuth/auth`

Initiates OAuth authorization flow.

**Query Parameters**:
- `state` (string): CSRF protection parameter
- `integrationId` (string): Integration identifier

**Process**:
1. Generate PKCE code verifier and challenge
2. Store verifier and state in Redis (15-minute TTL)
3. Construct authorization URL with parameters
4. Redirect user to Klaviyo authorization page

**Response**: HTTP 302 redirect to Klaviyo

#### GET `/integrationOAuth/klaviyoOAuth/callback`

Handles OAuth callback from Klaviyo.

**Query Parameters**:
- `code` (string): Authorization code from Klaviyo
- `state` (string): State parameter for CSRF validation
- `error` (string, optional): Error code if authorization failed

**Process**:
1. Validate state parameter against Redis
2. Retrieve code verifier from Redis
3. Exchange authorization code for tokens
4. Save integration data to database
5. Redirect to admin interface

**Response**: HTTP 302 redirect to admin with success/error status

#### GET `/integrationOAuth/klaviyoOAuth/install`

Handles installation flow for Klaviyo app marketplace.

**Process**:
1. Check for installation cookie
2. Generate PKCE codes if installation needed
3. Return authorization URL for app installation

**Response**:
```json
{
  "success": true,
  "authUrl": "https://www.klaviyo.com/oauth/authorize?..."
}
```

## Service Classes

### KlaviyoOAuthAdapter

Main service class for API operations.

#### Constructor

```javascript
constructor({ access_token, refresh_token, expires_at })
```

**Parameters**:
- `access_token` (string): OAuth access token
- `refresh_token` (string): OAuth refresh token
- `expires_at` (Date): Token expiration timestamp

#### Methods

##### `async isTokenGood()`

Checks if current token is valid and refreshes if needed.

**Returns**: `Promise<boolean>` - Token validity status

**Logic**:
- Checks if token expires within 10 minutes
- Automatically refreshes if needed
- Returns false if refresh fails

##### `async getNewToken()`

Refreshes access token using refresh token.

**Returns**: `Promise<boolean>` - Refresh success status

**Process**:
1. Make POST request to token endpoint
2. Update token data in database
3. Update instance properties
4. Update Axios headers

##### `async getLists()`

Retrieves all email lists from Klaviyo.

**Returns**: `Promise<Array<Object>>` - Array of list objects

**Features**:
- Handles pagination automatically
- Sorts lists by name
- Formats response data consistently

##### `async getSegments()`

Retrieves all audience segments from Klaviyo.

**Returns**: `Promise<Array<Object>>` - Array of segment objects

**Features**:
- Handles pagination automatically
- Filters active segments only
- Formats response data consistently

##### `async ping()`

Tests API connectivity and token validity.

**Returns**: `Promise<boolean>` - Connection status

**Usage**: Health checks and integration validation

## Error Handling

### Error Types

1. **Authentication Errors**: Invalid tokens, expired credentials
2. **API Errors**: Klaviyo API failures, rate limits
3. **Network Errors**: Connection timeouts, DNS failures
4. **Validation Errors**: Invalid parameters, missing data

### Error Logging

All errors are logged with structured format:

```javascript
{
  name: 'KlaviyoError',
  message: 'Error description',
  context: 'operation-context',
  code: 'ERROR_CODE',
  status: 400,
  responseData: {...}
}
```

### Error Recovery

- **Token Refresh**: Automatic retry with new token
- **Rate Limiting**: Exponential backoff for rate limit errors
- **Network Errors**: Configurable retry logic
- **Graceful Degradation**: Fallback to cached data when possible

## Configuration

### Environment Variables

```bash
# Required
KLAVIYO_CLIENT_ID=your_client_id
KLAVIYO_CLIENT_SECRET=your_client_secret

# Optional
KLAVIYO_API_VERSION=2025-04-15

# Application URLs
om_backend_url=https://your-backend-url
om_new_admin_url=https://your-admin-url
```

### Redis Configuration

Redis is used for temporary storage during OAuth flow:

- **Connection**: Uses `ioRedisAdapter` service
- **Key Patterns**: 
  - `klaviyoOAuthCode-{userId}`: PKCE code verifier
  - `klaviyoOAuthState-{userId}`: OAuth state parameter
- **TTL**: 900 seconds (15 minutes)

### Database Schema

Integration data is stored in the `accounts` collection:

```javascript
{
  databaseId: "user_id",
  settings: {
    integrations: [{
      _id: ObjectId,
      type: "klaviyoOAuth",
      data: {
        name: "Integration Name",
        access_token: "token",
        refresh_token: "refresh_token",
        expires_at: Date,
        scope: "permissions"
      }
    }]
  }
}
```

## Development

### Adding New API Methods

1. **Add method to KlaviyoOAuthAdapter**:
```javascript
async newMethod() {
  return executeApiRequest(
    async () => {
      const isSuccess = await this.isTokenGood();
      if (!isSuccess) {
        throw new KlaviyoError('Token refresh failed');
      }
      
      const response = await this.api.get('/new-endpoint');
      return safeParseResponse(response, this._formatData.bind(this));
    },
    {
      context: 'klaviyo-oauth-new-method',
      logger: log,
    }
  );
}
```

2. **Add endpoint constants if needed**:
```javascript
const NEW_ENDPOINT = `${API_BASE_URL}/new-endpoint`;
```

3. **Add error handling**:
```javascript
// Custom error for specific scenarios
static newMethodError(message, originalError = null) {
  return new KlaviyoError(message, {
    originalError,
    context: 'klaviyo-new-method',
    code: 'KLAVIYO_NEW_METHOD_ERROR',
  });
}
```

### Testing

#### Unit Tests

```javascript
describe('KlaviyoOAuthAdapter', () => {
  test('should refresh token when expired', async () => {
    const adapter = new KlaviyoOAuthAdapter({
      access_token: 'old_token',
      refresh_token: 'refresh_token',
      expires_at: new Date(Date.now() - 1000) // Expired
    });
    
    const result = await adapter.isTokenGood();
    expect(result).toBe(true);
  });
});
```

#### Integration Tests

```javascript
describe('OAuth Flow', () => {
  test('should complete full OAuth flow', async () => {
    // Test authorization URL generation
    const authUrl = getAuthURL('test-state', 'test-challenge');
    expect(authUrl).toContain('klaviyo.com/oauth/authorize');
    
    // Test token exchange
    const tokens = await getAuth('auth_code', '/callback', 'user_id');
    expect(tokens.access_token).toBeDefined();
  });
});
```

### Debugging

Enable debug logging:

```javascript
const log = require('../../logger').child({ 
  integration: 'klaviyo-oauth',
  level: 'debug'
});
```

Monitor Redis operations:

```bash
redis-cli monitor | grep klaviyoOAuth
```

Check database operations:

```javascript
// Log all database queries
mongoose.set('debug', true);
```
