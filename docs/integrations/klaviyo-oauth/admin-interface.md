# Klaviyo OAuth Admin Interface Documentation

This document details the admin interface implementation of the Klaviyo OAuth integration in the `om-admin` package.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Components](#components)
- [Integration Modal](#integration-modal)
- [Settings Components](#settings-components)
- [Validation](#validation)
- [User Experience](#user-experience)
- [Development](#development)

## Overview

The admin interface provides:
- Multi-step integration setup wizard
- OAuth authentication flow
- List selection and management
- Field mapping configuration
- Integration settings management
- Validation and error handling
- User-friendly setup experience

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    om-admin Package                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Integration     │    │ Settings        │                │
│  │ Modal           │    │ Component       │                │
│  │                 │    │                 │                │
│  │ • Step wizard   │◄───┤ • List selection│                │
│  │ • OAuth flow    │    │ • Field binding │                │
│  │ • Validation    │    │ • Validation    │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Step Components │    │ Shared Mixins   │                │
│  │                 │    │                 │                │
│  │ • Step 1: Auth  │◄───┤ • Integration   │                │
│  │ • Step 2: Lists │    │   Base          │                │
│  │ • Step 3: Fields│    │ • Validation    │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ UI Components   │    │ GraphQL Queries │                │
│  │                 │    │                 │                │
│  │ • Stepper       │    │ • Get Lists     │                │
│  │ • Modal Footer  │    │ • Save Settings │                │
│  │ • Form Controls │    │ • Validation    │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Components

### 1. Integration Modal

**File**: `src/components/IntegrationModals/klaviyoOAuth/index.vue`

Main modal component that orchestrates the setup wizard.

<augment_code_snippet path="packages/om-admin/src/components/IntegrationModals/klaviyoOAuth/index.vue" mode="EXCERPT">
````vue
<template lang="pug">
.klaviyo-oauth
  .d-flex.step-holder
    stepper(:activeStep="activeContent" @stepClick="validateClickedStep" :steps="alignedSteps")
  .content
    transition(name="fade" mode="out-in")
      template(v-if="loading")
      template(v-else)
        step1(
          v-if="this.activeContent === 'setup'"
          :settings.sync="settings"
          :validations="$v.settings"
          :validByAlert.sync="validByAlert"
        )
        step2(
          v-if="this.activeContent === 'listId'"
          :settings.sync="settings"
          :validations="$v.settings"
          :validByAlert.sync="validByAlert"
          @isModified="step2Modified = $event"
        )
        step3(
          v-if="this.activeContent === 'fieldMapping'"
          ref="bindings"
          :state="state"
          :settings.sync="settings"
          :validByAlert.sync="validByAlert"
          @isModified="isBindingModified = $event"
        )
````
</augment_code_snippet>

#### Key Features:

- **Step Navigation**: Progressive wizard interface
- **State Management**: Centralized settings and validation
- **Error Handling**: OAuth error detection and display
- **Responsive Design**: Mobile-friendly layout

#### Data Structure:

```javascript
data() {
  return {
    activeContent: 'setup',
    integrationType: 'klaviyoOAuth',
    step2Modified: false,
    isBindingModified: false,
    validByAlert: true,
    loading: false
  };
}
```

#### Validation Rules:

```javascript
validations() {
  return {
    settings: {
      publicApiKey: {
        required: requiredIf(function () {
          return this.isNew;
        }),
      },
      name: { required },
      convertedSettings: {
        listId: {
          required: requiredIf(function () {
            return !this.isFirstStep && !this.validateListSelection();
          }),
          validByAlert: () => this.validByAlert,
        },
      },
    },
  };
}
```

### 2. Step 1: Authentication

**File**: `src/components/IntegrationModals/klaviyoOAuth/step1.vue`

Handles OAuth authentication and initial setup.

<augment_code_snippet path="packages/om-admin/src/components/IntegrationModals/klaviyoOAuth/step1.vue" mode="EXCERPT">
````vue
<script>
export default {
  components: {
    UilCheckCircle,
    UilTimesCircle,
  },
  props: {
    settings: {
      type: Object,
      required: true,
    },
    validations: {
      type: Object,
      required: true,
    },
    isNew: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    hasOauthError() {
      return !!this.$route.query?.oauthError;
    },
    getBaseIntegrationData() {
      return {
        type: 'klaviyoOAuth',
        data: {
          name: this.settings.name || 'Klaviyo OAuth Integration {counter}',
        },
      };
    },
    hasAccessToken() {
      return !!this.settings.access_token;
    },
  },
  methods: {
    authenticate() {
      this.$bus.$emit('saveCampaignSettings', { redirect: null });
      window.location.href = getOauthRedirectUrl(
        this.getBaseIntegrationData,
        this.$route.params.id,
      );
    },
  },
};
````
</augment_code_snippet>

#### Features:

- **OAuth Status Display**: Shows authentication state
- **Error Handling**: Displays OAuth errors from URL parameters
- **Authentication Button**: Initiates OAuth flow
- **Integration Naming**: Default name with counter

#### User Interface:

- **Success State**: Green checkmark when authenticated
- **Error State**: Red X with error message
- **Pending State**: Authentication button
- **Loading State**: Spinner during OAuth flow

### 3. Step 2: List Selection

**File**: `src/components/IntegrationModals/klaviyoOAuth/step2.vue`

Handles email list selection and configuration.

#### Features:

- **List Retrieval**: Fetches available lists from Klaviyo
- **List Display**: Shows list names and subscriber counts
- **Selection Validation**: Ensures a list is selected
- **Loading States**: Shows loading during API calls
- **Error Handling**: Displays API errors

#### Data Flow:

1. Component mounts and fetches lists
2. User selects a list from dropdown
3. Selection is validated
4. Settings are updated with list ID

### 4. Step 3: Field Mapping

**File**: `src/components/IntegrationModals/klaviyoOAuth/step3.vue`

Handles field mapping between OptimOnk and Klaviyo.

#### Features:

- **Field Binding**: Maps OptimOnk fields to Klaviyo fields
- **Validation**: Ensures required fields are mapped
- **Custom Fields**: Support for custom field mapping
- **Preview**: Shows mapping preview
- **Validation Feedback**: Real-time validation messages

### 5. Settings Component

**File**: `src/components/IntegrationSettings/KlaviyoOAuth.vue`

Manages integration settings after initial setup.

<augment_code_snippet path="packages/om-admin/src/components/IntegrationSettings/KlaviyoOAuth.vue" mode="EXCERPT">
````vue
<template lang="pug">
div
  .mb-4
    .row.align-items-center.mb-2
      .col-sm-4.col-form-label
        label.mb-0(for="listName") {{ $t('listName') }}:
      .col-sm-8
        select#listName.form-control(
          :class="{ 'is-invalid': $v.settings.listId.$error }"
          v-model="settings.listId"
        )
          option(:value="null") {{ $t('selectAList') }}
          template(v-for="list in lists")
            option(:value="list.id") {{ list.name }}
  integration-binding(ref="binding" :type="globalIntegration.type" :bindings.sync="bindings")
</template>
````
</augment_code_snippet>

#### Features:

- **List Management**: Change selected list
- **Field Binding**: Update field mappings
- **Validation**: Real-time validation
- **GraphQL Integration**: Uses GraphQL for data fetching

## Integration Modal

### Step Flow

The integration setup follows a three-step process:

1. **Authentication** (`setup`)
   - OAuth authentication
   - Integration naming
   - Error handling

2. **List Selection** (`listId`)
   - Fetch available lists
   - Select target list
   - Validate selection

3. **Field Mapping** (`fieldMapping`)
   - Map OptimOnk fields to Klaviyo fields
   - Configure required fields
   - Validate mappings

### Navigation Logic

```javascript
methods: {
  validateClickedStep(step) {
    // Validate current step before allowing navigation
    if (this.canNavigateToStep(step)) {
      this.activeContent = step;
    }
  },
  
  canNavigateToStep(step) {
    const stepOrder = ['setup', 'listId', 'fieldMapping'];
    const currentIndex = stepOrder.indexOf(this.activeContent);
    const targetIndex = stepOrder.indexOf(step);
    
    // Can only navigate to completed steps or next step
    return targetIndex <= currentIndex + 1;
  }
}
```

### State Management

The modal manages state through:

- **Settings Object**: Central configuration storage
- **Validation State**: Real-time validation feedback
- **Step State**: Current step and completion status
- **Loading State**: API call progress

### Error Handling

#### OAuth Errors

```javascript
computed: {
  hasOauthError() {
    return !!this.$route.query?.oauthError;
  },
  
  oauthErrorMessage() {
    const errorCode = this.$route.query?.oauthError;
    return this.getErrorMessage(errorCode);
  }
}
```

#### API Errors

```javascript
methods: {
  async fetchLists() {
    try {
      this.loading = true;
      const { data } = await this.$apollo.query({
        query: GET_INTEGRATION_LISTS,
        variables: {
          integrationType: 'klaviyoOAuth',
          integrationId: this.integrationId
        }
      });
      this.lists = data.lists;
    } catch (error) {
      this.showError('Failed to fetch lists');
    } finally {
      this.loading = false;
    }
  }
}
```

## Settings Components

### List Selection

The list selection component provides:

- **Dropdown Interface**: User-friendly list selection
- **Search Functionality**: Filter lists by name
- **Validation**: Required field validation
- **Loading States**: Progress indicators

### Field Binding

The field binding component offers:

- **Drag & Drop**: Intuitive field mapping
- **Validation**: Required field checking
- **Custom Fields**: Support for custom field types
- **Preview**: Mapping preview and validation

## Validation

### Client-Side Validation

Uses Vuelidate for form validation:

```javascript
import { required, requiredIf } from 'vuelidate/lib/validators';

validations: {
  settings: {
    name: { required },
    listId: { 
      required: requiredIf(function() {
        return this.currentStep !== 'setup';
      })
    }
  }
}
```

### Server-Side Validation

GraphQL mutations include server-side validation:

```javascript
mutation SaveIntegration($input: IntegrationInput!) {
  saveIntegration(input: $input) {
    success
    errors {
      field
      message
    }
    integration {
      id
      name
      settings
    }
  }
}
```

### Real-Time Validation

Validation occurs on:

- **Field Change**: Immediate feedback
- **Step Navigation**: Prevent invalid navigation
- **Form Submission**: Final validation before save

## User Experience

### Progressive Disclosure

Information is revealed progressively:

1. **Step 1**: Basic authentication
2. **Step 2**: List configuration
3. **Step 3**: Advanced field mapping

### Loading States

Clear loading indicators for:

- **OAuth Flow**: Authentication progress
- **API Calls**: List fetching, validation
- **Form Submission**: Save progress

### Error Messages

User-friendly error messages:

- **OAuth Errors**: Clear authentication issues
- **Validation Errors**: Field-specific feedback
- **API Errors**: Network and server issues

### Success Feedback

Positive feedback for:

- **Successful Authentication**: Green checkmark
- **Valid Configuration**: Step completion indicators
- **Successful Save**: Success notification

## Development

### Adding New Steps

1. **Create Step Component**:

```vue
<template>
  <div class="step-new">
    <!-- Step content -->
  </div>
</template>

<script>
export default {
  props: {
    settings: Object,
    validations: Object
  },
  // Component logic
};
</script>
```

2. **Update Main Modal**:

```javascript
// Add to steps array
steps: [
  { key: 'setup', title: 'Authentication' },
  { key: 'listId', title: 'List Selection' },
  { key: 'newStep', title: 'New Step' },
  { key: 'fieldMapping', title: 'Field Mapping' }
]

// Add to template
stepNew(
  v-if="this.activeContent === 'newStep'"
  :settings.sync="settings"
  :validations="$v.settings"
)
```

3. **Update Validation**:

```javascript
validations() {
  return {
    settings: {
      // Existing validations
      newField: { required }
    }
  };
}
```

### Custom Validation Rules

```javascript
// Custom validator
const validListId = (value) => {
  return value && value.length > 0;
};

// Usage in validations
validations: {
  settings: {
    listId: { 
      required,
      validListId
    }
  }
}
```

### Internationalization

Add translations for new features:

```javascript
// en.json
{
  "klaviyo": {
    "oauth": {
      "steps": {
        "authentication": "Authentication",
        "listSelection": "List Selection",
        "fieldMapping": "Field Mapping"
      },
      "errors": {
        "authFailed": "Authentication failed",
        "noLists": "No lists available"
      }
    }
  }
}
```

### Testing

#### Component Tests

```javascript
import { shallowMount } from '@vue/test-utils';
import KlaviyoOAuthModal from '@/components/IntegrationModals/klaviyoOAuth/index.vue';

describe('KlaviyoOAuthModal', () => {
  test('should render authentication step by default', () => {
    const wrapper = shallowMount(KlaviyoOAuthModal);
    expect(wrapper.vm.activeContent).toBe('setup');
  });

  test('should validate step navigation', () => {
    const wrapper = shallowMount(KlaviyoOAuthModal);
    const canNavigate = wrapper.vm.canNavigateToStep('fieldMapping');
    expect(canNavigate).toBe(false);
  });
});
```

#### Integration Tests

```javascript
describe('Integration Setup Flow', () => {
  test('should complete full setup process', async () => {
    // Mount component
    const wrapper = mount(KlaviyoOAuthModal);
    
    // Step 1: Authentication
    await wrapper.find('.authenticate-button').trigger('click');
    // Mock OAuth success
    
    // Step 2: List Selection
    await wrapper.find('select').setValue('list-id');
    
    // Step 3: Field Mapping
    await wrapper.find('.save-button').trigger('click');
    
    expect(wrapper.emitted('saved')).toBeTruthy();
  });
});
```

### Debugging

#### Vue DevTools

Use Vue DevTools to inspect:

- Component state
- Props and data
- Events and mutations
- Vuex store state

#### Console Logging

Add debug logging:

```javascript
methods: {
  debugStep(step) {
    console.log('Step Navigation:', {
      from: this.activeContent,
      to: step,
      settings: this.settings,
      validation: this.$v.$invalid
    });
  }
}
```

#### Error Tracking

Monitor errors in production:

```javascript
errorCaptured(err, instance, info) {
  console.error('Klaviyo OAuth Error:', {
    error: err,
    component: instance.$options.name,
    info: info
  });
  
  // Send to error tracking service
  this.$sentry.captureException(err);
}
```
