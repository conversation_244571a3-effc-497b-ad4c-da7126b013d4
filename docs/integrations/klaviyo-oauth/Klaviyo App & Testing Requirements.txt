OAuth App Overview

Customer Workflow

Enumerate the steps required for a customer to set up and utilize the integration.
1. Login/register into OptiMonk
2. After the onboarding process create a new campaign
3. After exiting from the editor the customer needs to add a new Klaviyo integration
4. Follow the OAuth flow

Integration Details
Provide a list of use cases and corresponding Klaviyo API Endpoints.
Use Case
	Endpoint(s)
	1 - New Klaviyo added, showing the lists - https://a.klaviyo.com/api/lists
	2 - When a visitor signs up in a campaign - https://a.klaviyo.com/api/profile-subscription-bulk-create-jobs

Architectural Diagram
Include a visual to explain how data moves between Klaviyo and your systems.

Product Integration Demo
Use case 1: https://jam.dev/c/3d52b6c2-1e46-44fb-acaf-143e11a09a30


Testing Details
For Klaviyo to approve the application, we need to test the OAuth flow. Please provide access to a test account that can use Klaviyo OAuth. This test account should be able to demo the end-to-end flow of data in or out of Klaviyo. 


For security purposes, do not share any account credentials with us in this document or via email. You can <NAME_EMAIL> email to your test account, or submit a password reset to that account to provide access.


Password reset link: https://app.optimonk.com/forgotten-password?lang=en

-----------------------------------------
 — Klaviyo App Review Checklist —
The checklist below outlines what the Klaviyo Ecosystem team will review when you submit your application.To help ensure a smooth publishing process, please use this as a reference of items to complete. Keep in mind that even if you meet all the criteria, the team may still offer feedback on how to improve your workflow. Please do not fill out this checklist; Klaviyo will provide updates and feedback as needed.


Technical Review
Integrating with Klaviyo
   * Install URL directs user as expected
      * Works while logged into Partner App
      * Works while not logged into Partner App
   * Installation via Partner App is seamless
Uninstalling (via Partner App or Klaviyo)
   * Revoking access via Partner App successfully removes app in Klaviyo
   * Removing integration via Klaviyo successfully updates Partner App
* Settings URL directs to an Integration/Settings page
* Deny permission workflow uses clear language and notifies client how to proceed
* Metrics are supplied and updated as applicable
* App performs as expected and described


General Documentation and Form Submissions
* Installation instructions supplied via Manage App page
* Settings / Install / Support URLs via Manage App page
   * If directing to support email; supply client-facing documentation as well 
* Integration Registration 
* Security Questionnaire 
* Partner Page - publicly facing integration documentation


‘To Do’ Checklist
* Recorded demo of installation and use cases
* Klaviyo brand guidelines are met and references to Klaviyo are accurate
* The integration has 5 active user installs






________________


Details for Checklist Items
Please note that these items are the same as the above, but we’ve added some additional explanations here for sake of clarity. 


Technical Review
* Integrating with Klaviyo
   * Install URL directs user as expected
      * This URL is hit when a client clicks ‘Install’ in Klaviyo on your integration directory page
      * The URL should redirect directly into the Authorization workflow
      * Needs to work while logged into the partner app or while not logged in, via redirecting through login workflow
   * Installation via Partner App is seamless
      * User can clearly navigate to an integrations area and install the integration
* Uninstalling (via Partner App or Klaviyo)
   * Revoke access via Partner App; successfully removes app in Klaviyo
      * Team has implemented steps noted here - such that the integration does not appear within Klaviyo
   * Remove integration via Klaviyo; Partner App updates successfully
      * Team has reviewed how to handle uninstalls from Klaviyo
* Settings URL directs to an Integration/Settings page
   * A Klaviyo specific integration settings page is the ideal, but a general integration settings page would be acceptable
* Deny permission workflow uses clear language and notifies client how to proceed
   * Include language that notifies the user that they’ve denied the permissions needed for the integration, and if they want the integration, they’ll need to go through the workflow again
* Metrics are supplied and updated as applicable
   * Use branded events to ensure clients are seeing your value
* App performs as expected and described
   * All use cases noted should be able to be completed by tester


General Documentation and Form Submissions
* Installation instructions (this document) supplied via Manage App page
* Preferences / Install / Support URLs via Manage App page
   * If directing to support email; supply client-facing documentation as well 
* Integration Registration 
* Security Questionnaire 
* Partner Page - publicly facing integration documentation


‘To Do’ Checklist
* Recorded demo of installation and use cases
   * Helps Ecosystem and Security teams during App Review process 
* Klaviyo brand guidelines are met and references to Klaviyo are accurate
   * Using the guide, ensure proper logos are used
* 5 active user installs
