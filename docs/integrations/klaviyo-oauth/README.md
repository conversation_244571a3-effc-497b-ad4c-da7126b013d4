# Klaviyo OAuth Integration Documentation

This document provides comprehensive documentation for the Klaviyo OAuth integration implemented across all services in the OptimOnk monorepo.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Services](#services)
- [Configuration](#configuration)
- [API Reference](#api-reference)
- [Development](#development)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)

## Documentation Structure

This documentation is organized into several detailed guides:

- **[Backend Service](./backend-service.md)**: Complete backend implementation details
- **[Frontend Service](./frontend-service.md)**: Frontend integration handlers and providers
- **[Admin Interface](./admin-interface.md)**: Admin UI components and user experience
- **[Shared Libraries](./shared-libraries.md)**: Common configurations and utilities
- **[API Reference](./api-reference.md)**: Complete API documentation
- **[Deployment Configuration](./deployment-configuration.md)**: Production deployment guide

## Overview

The Klaviyo OAuth integration enables secure authentication and data synchronization with Klaviyo's email marketing platform using OAuth 2.0 with PKCE (Proof Key for Code Exchange). This integration supports:

- **OAuth 2.0 Authentication**: Secure token-based authentication
- **Profile Management**: Create and update customer profiles
- **List Management**: Retrieve and manage email lists
- **Subscription Handling**: Subscribe users to email lists
- **Token Refresh**: Automatic token renewal
- **Error Handling**: Comprehensive error management

## Architecture

The integration follows a distributed architecture across multiple services:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   om-admin      │    │   om-backend    │    │   om-frontend   │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Integration │ │    │ │ OAuth       │ │    │ │ Conversion  │ │
│ │ Modals      │ │    │ │ Controller  │ │    │ │ Handlers    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Settings    │ │    │ │ Service     │ │    │ │ Auth        │ │
│ │ Components  │ │    │ │ Adapter     │ │    │ │ Providers   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Klaviyo API   │
                    │                 │
                    │ ┌─────────────┐ │
                    │ │ OAuth       │ │
                    │ │ Endpoints   │ │
                    │ └─────────────┘ │
                    │ ┌─────────────┐ │
                    │ │ Profile &   │ │
                    │ │ List APIs   │ │
                    │ └─────────────┘ │
                    └─────────────────┘
```

## Services

### 1. Backend Service (om-backend)

**Location**: `packages/om-backend/`

#### OAuth Controller
- **File**: `controllers/oauth/klaviyoOAuth.js`
- **Purpose**: Handles OAuth flow endpoints
- **Key Features**:
  - Authorization URL generation with PKCE
  - Token exchange and refresh
  - Redis-based state management
  - Error handling and logging

#### Service Adapter
- **File**: `services/integrations/klaviyoOAuth.js`
- **Purpose**: Main integration service class
- **Key Features**:
  - Token management and refresh
  - API communication
  - List and segment retrieval
  - Profile operations

#### API Utilities
- **File**: `services/integrations/klaviyo/apiUtils.js`
- **Purpose**: Shared utilities for API operations
- **Key Features**:
  - Consistent error handling
  - Response parsing
  - Token refresh handling

#### Error Handling
- **File**: `services/integrations/klaviyo/KlaviyoError.js`
- **Purpose**: Custom error class for Klaviyo operations
- **Key Features**:
  - Structured error logging
  - Axios error conversion
  - Context-aware error messages

#### Constants
- **File**: `services/integrations/klaviyo/constants.js`
- **Purpose**: Centralized configuration
- **Key Features**:
  - API endpoints and versions
  - OAuth credentials
  - Scope definitions
  - Redis key patterns

### 2. Frontend Service (om-frontend)

**Location**: `packages/om-frontend/`

#### Integration Handler
- **File**: `src/handlers/conversions/integrations/KlaviyoOAuth.ts`
- **Purpose**: Main conversion handler for OAuth integration
- **Key Features**:
  - Token management
  - Subscription processing
  - Multi-list support
  - Error handling

#### Auth Providers
- **File**: `src/handlers/conversions/integrations/klaviyo/KlaviyoOAuthProvider.ts`
- **Purpose**: OAuth-specific authentication provider
- **Key Features**:
  - Bearer token authentication
  - Header management

#### Conversion Handlers
- **Files**: 
  - `src/handlers/conversions/integrations/klaviyo/KlaviyoBulkSubscribe.ts`
  - `src/handlers/conversions/integrations/klaviyo/KlaviyoInstantConsent.ts`
- **Purpose**: Handle different subscription methods
- **Key Features**:
  - Profile upsert operations
  - List subscription
  - Consent management

### 3. Admin Interface (om-admin)

**Location**: `packages/om-admin/`

#### Integration Modal
- **File**: `src/components/IntegrationModals/klaviyoOAuth/index.vue`
- **Purpose**: Multi-step integration setup wizard
- **Key Features**:
  - OAuth authentication flow
  - List selection
  - Field mapping configuration
  - Validation and error handling

#### Step Components
- **Files**:
  - `src/components/IntegrationModals/klaviyoOAuth/step1.vue` - Authentication
  - `src/components/IntegrationModals/klaviyoOAuth/step2.vue` - List selection
  - `src/components/IntegrationModals/klaviyoOAuth/step3.vue` - Field mapping

#### Settings Component
- **File**: `src/components/IntegrationSettings/KlaviyoOAuth.vue`
- **Purpose**: Integration configuration interface
- **Key Features**:
  - List selection
  - Field binding management
  - Validation

### 4. Shared Libraries

#### Integration Configuration
- **File**: `libraries/integrations/src/configs/klaviyoOAuth.js`
- **Purpose**: Shared field definitions
- **Key Features**:
  - Reserved field mappings
  - Location field definitions

## Configuration

### Environment Variables

The integration uses the following environment variables:

```bash
# Klaviyo OAuth Configuration
KLAVIYO_CLIENT_ID=your_client_id
KLAVIYO_CLIENT_SECRET=your_client_secret
KLAVIYO_API_VERSION=2025-04-15

# Application URLs
om_backend_url=https://your-backend-url
om_new_admin_url=https://your-admin-url
```

### OAuth Scopes

Default scopes requested during authentication:

- `subscriptions:write` - Create and manage subscriptions
- `profiles:read` - Read profile information
- `profiles:write` - Create and update profiles
- `lists:read` - Read list information
- `lists:write` - Manage lists
- `segments:read` - Read segment information

### Redis Configuration

The integration uses Redis for temporary storage:

- **Code Verifier**: `klaviyoOAuthCode-{userId}` (TTL: 900 seconds)
- **State Parameter**: `klaviyoOAuthState-{userId}` (TTL: 900 seconds)

## API Reference

### OAuth Endpoints

#### GET /integrationOAuth/klaviyoOAuth/auth
Initiates OAuth authorization flow.

**Parameters**:
- `state` - CSRF protection parameter
- `integrationId` - Integration identifier

**Response**: Redirects to Klaviyo authorization URL

#### GET /integrationOAuth/klaviyoOAuth/callback
Handles OAuth callback from Klaviyo.

**Parameters**:
- `code` - Authorization code
- `state` - State parameter for validation

**Response**: Redirects to admin interface with success/error status

#### GET /integrationOAuth/klaviyoOAuth/install
Handles installation flow for Klaviyo app.

**Response**: JSON with authentication URL or success status

### Integration Service Methods

#### `ping()`
Tests connection and token validity.

**Returns**: `boolean` - Connection status

#### `getLists()`
Retrieves all available lists.

**Returns**: `Array<Object>` - List of email lists

#### `getSegments()`
Retrieves all available segments.

**Returns**: `Array<Object>` - List of segments

#### `getSegmentsAndList()`
Retrieves both lists and segments.

**Returns**: `Array<Object>` - Combined list of lists and segments

## Development

### Setting Up Development Environment

1. **Install Dependencies**:
   ```bash
   yarn install
   ```

2. **Configure Environment**:
   Copy `.env.example` to `.env` and configure Klaviyo credentials.

3. **Start Services**:
   ```bash
   # Backend
   cd packages/om-backend && yarn dev
   
   # Frontend
   cd packages/om-frontend && yarn dev
   
   # Admin
   cd packages/om-admin && yarn serve
   ```

### Adding New Features

1. **Backend Changes**:
   - Update service adapter in `services/integrations/klaviyoOAuth.js`
   - Add new API utilities if needed
   - Update constants for new endpoints

2. **Frontend Changes**:
   - Modify integration handler in `KlaviyoOAuth.ts`
   - Update auth providers if authentication changes
   - Add new conversion handlers for new features

3. **Admin Changes**:
   - Update modal components for new configuration options
   - Add validation rules
   - Update field mappings

### Code Style Guidelines

- Use TypeScript for frontend code
- Follow existing error handling patterns
- Add comprehensive logging
- Include JSDoc comments for public methods
- Use consistent naming conventions

## Testing

### Unit Tests

Run unit tests for each service:

```bash
# Backend tests
cd packages/om-backend && yarn test

# Frontend tests
cd packages/om-frontend && yarn test

# Admin tests
cd packages/om-admin && yarn test:unit
```

### Integration Tests

Test the complete OAuth flow:

1. **Authentication Flow**:
   - Test authorization URL generation
   - Test callback handling
   - Test token refresh

2. **API Operations**:
   - Test list retrieval
   - Test profile operations
   - Test subscription handling

3. **Error Scenarios**:
   - Test invalid tokens
   - Test API errors
   - Test network failures

### Manual Testing

1. **Setup Integration**:
   - Navigate to admin interface
   - Add new Klaviyo OAuth integration
   - Complete OAuth flow

2. **Test Functionality**:
   - Verify list retrieval
   - Test field mapping
   - Test subscription flow

## Troubleshooting

### Common Issues

#### 1. OAuth Authentication Fails

**Symptoms**: Redirect to error page after OAuth callback

**Solutions**:
- Verify `KLAVIYO_CLIENT_ID` and `KLAVIYO_CLIENT_SECRET`
- Check redirect URI configuration in Klaviyo app
- Verify Redis connectivity for state storage

#### 2. Token Refresh Fails

**Symptoms**: API calls return 401 Unauthorized

**Solutions**:
- Check token expiration handling
- Verify refresh token is stored correctly
- Check network connectivity to Klaviyo API

#### 3. List Retrieval Fails

**Symptoms**: Empty list in admin interface

**Solutions**:
- Verify OAuth scopes include `lists:read`
- Check API version compatibility
- Verify account has lists configured

#### 4. Profile Subscription Fails

**Symptoms**: Subscribers not added to Klaviyo lists

**Solutions**:
- Verify OAuth scopes include required permissions
- Check field mapping configuration
- Verify list ID is correct

### Debugging

#### Enable Debug Logging

Add debug logging to troubleshoot issues:

```javascript
// Backend
const log = require('../../logger').child({ 
  integration: 'klaviyo-oauth',
  debug: true 
});

// Frontend
console.debug('Klaviyo OAuth Debug:', data);
```

#### Monitor API Calls

Use browser developer tools or server logs to monitor:
- OAuth flow requests
- API calls to Klaviyo
- Token refresh operations
- Error responses

### Support

For additional support:
- Check Klaviyo API documentation: https://developers.klaviyo.com
- Review error logs in application monitoring
- Contact development team for integration-specific issues
