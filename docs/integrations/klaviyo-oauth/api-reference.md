# Klaviyo OAuth API Reference

This document provides a comprehensive API reference for the Klaviyo OAuth integration endpoints and methods.

## Table of Contents

- [Authentication](#authentication)
- [OAuth Endpoints](#oauth-endpoints)
- [Integration Service API](#integration-service-api)
- [GraphQL API](#graphql-api)
- [Error Responses](#error-responses)
- [Rate Limiting](#rate-limiting)
- [Examples](#examples)

## Authentication

All API endpoints require proper authentication:

- **OAuth Endpoints**: Use JWT tokens for user authentication
- **Service API**: Use OAuth access tokens for Klaviyo API access
- **GraphQL API**: Use session-based authentication

### JWT Token Format

```http
Authorization: Bearer <jwt_token>
```

### OAuth Access Token Format

```http
Authorization: Bearer <klaviyo_access_token>
```

## OAuth Endpoints

### Initiate OAuth Flow

Starts the OAuth authorization process with Klaviyo.

```http
GET /integrationOAuth/klaviyoOAuth/auth
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `state` | string | Yes | CSRF protection parameter |
| `integrationId` | string | Yes | Integration identifier |

#### Response

```http
HTTP/1.1 302 Found
Location: https://www.klaviyo.com/oauth/authorize?response_type=code&client_id=...
```

#### Example

```bash
curl -X GET \
  "https://backend.yourdomain.com/integrationOAuth/klaviyoOAuth/auth?state=csrf_token&integrationId=12345" \
  -H "Authorization: Bearer <jwt_token>"
```

### OAuth Callback

Handles the OAuth callback from Klaviyo after user authorization.

```http
GET /integrationOAuth/klaviyoOAuth/callback
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `code` | string | Yes | Authorization code from Klaviyo |
| `state` | string | Yes | State parameter for CSRF validation |
| `error` | string | No | Error code if authorization failed |

#### Success Response

```http
HTTP/1.1 302 Found
Location: https://admin.yourdomain.com/integrations?success=true&integration=klaviyoOAuth
```

#### Error Response

```http
HTTP/1.1 302 Found
Location: https://admin.yourdomain.com/integrations?error=oauth_failed&message=Invalid+authorization+code
```

#### Example

```bash
# This endpoint is called by Klaviyo, not directly by clients
# Example callback URL:
https://backend.yourdomain.com/integrationOAuth/klaviyoOAuth/callback?code=auth_code_here&state=csrf_token
```

### Installation Endpoint

Handles Klaviyo app marketplace installation flow.

```http
GET /integrationOAuth/klaviyoOAuth/install
```

#### Response

```json
{
  "success": true,
  "authUrl": "https://www.klaviyo.com/oauth/authorize?..."
}
```

#### Example

```bash
curl -X GET \
  "https://backend.yourdomain.com/integrationOAuth/klaviyoOAuth/install" \
  -H "Authorization: Bearer <jwt_token>"
```

## Integration Service API

### Test Connection

Tests the integration connection and token validity.

```http
POST /api/integrations/klaviyoOAuth/ping
```

#### Request Body

```json
{
  "integrationId": "60f1b2c3d4e5f6789abcdef0"
}
```

#### Success Response

```json
{
  "success": true,
  "message": "Connection successful",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Error Response

```json
{
  "success": false,
  "error": "Token expired",
  "code": "KLAVIYO_TOKEN_EXPIRED"
}
```

### Get Lists

Retrieves all available email lists from Klaviyo.

```http
POST /api/integrations/klaviyoOAuth/lists
```

#### Request Body

```json
{
  "integrationId": "60f1b2c3d4e5f6789abcdef0"
}
```

#### Success Response

```json
{
  "success": true,
  "lists": [
    {
      "id": "XyZ123",
      "name": "Newsletter Subscribers",
      "created": "2024-01-01T00:00:00Z",
      "updated": "2024-01-15T10:30:00Z"
    },
    {
      "id": "AbC456",
      "name": "VIP Customers",
      "created": "2024-01-05T00:00:00Z",
      "updated": "2024-01-10T15:45:00Z"
    }
  ]
}
```

### Get Segments

Retrieves all available audience segments from Klaviyo.

```http
POST /api/integrations/klaviyoOAuth/segments
```

#### Request Body

```json
{
  "integrationId": "60f1b2c3d4e5f6789abcdef0"
}
```

#### Success Response

```json
{
  "success": true,
  "segments": [
    {
      "id": "Seg789",
      "name": "High Value Customers",
      "definition": "Total spent > $500",
      "created": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Subscribe Profile

Subscribes a profile to a Klaviyo list.

```http
POST /api/integrations/klaviyoOAuth/subscribe
```

#### Request Body

```json
{
  "integrationId": "60f1b2c3d4e5f6789abcdef0",
  "profile": {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+1234567890"
  },
  "listId": "XyZ123",
  "options": {
    "doubleOptin": false,
    "sendWelcomeEmail": true
  }
}
```

#### Success Response

```json
{
  "success": true,
  "profileId": "Prof123",
  "subscriptionId": "Sub456",
  "message": "Profile subscribed successfully"
}
```

## GraphQL API

### Get Integration Lists

Retrieves lists for a specific integration.

```graphql
query GetIntegrationLists($integrationType: String!, $integrationId: ID!) {
  lists(integrationType: $integrationType, integrationId: $integrationId) {
    id
    name
    created
    updated
  }
}
```

#### Variables

```json
{
  "integrationType": "klaviyoOAuth",
  "integrationId": "60f1b2c3d4e5f6789abcdef0"
}
```

#### Response

```json
{
  "data": {
    "lists": [
      {
        "id": "XyZ123",
        "name": "Newsletter Subscribers",
        "created": "2024-01-01T00:00:00Z",
        "updated": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### Save Integration

Creates or updates a Klaviyo OAuth integration.

```graphql
mutation SaveIntegration($input: IntegrationInput!) {
  saveIntegration(input: $input) {
    success
    errors {
      field
      message
    }
    integration {
      id
      name
      type
      settings
    }
  }
}
```

#### Variables

```json
{
  "input": {
    "type": "klaviyoOAuth",
    "name": "My Klaviyo Integration",
    "settings": {
      "listId": "XyZ123",
      "bindings": [
        {
          "fieldId": "email",
          "externalId": "email",
          "type": "string",
          "required": true
        }
      ]
    }
  }
}
```

#### Response

```json
{
  "data": {
    "saveIntegration": {
      "success": true,
      "errors": [],
      "integration": {
        "id": "60f1b2c3d4e5f6789abcdef0",
        "name": "My Klaviyo Integration",
        "type": "klaviyoOAuth",
        "settings": {
          "listId": "XyZ123",
          "bindings": [...]
        }
      }
    }
  }
}
```

## Error Responses

### Standard Error Format

All API endpoints return errors in a consistent format:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "specific_field",
    "value": "invalid_value"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `KLAVIYO_AUTH_INVALID_CLIENT` | Invalid OAuth client credentials | 401 |
| `KLAVIYO_AUTH_EXPIRED_TOKEN` | Access token has expired | 401 |
| `KLAVIYO_AUTH_INVALID_GRANT` | Invalid authorization grant | 400 |
| `KLAVIYO_API_RATE_LIMITED` | API rate limit exceeded | 429 |
| `KLAVIYO_API_NOT_FOUND` | Resource not found | 404 |
| `KLAVIYO_API_SERVER_ERROR` | Klaviyo server error | 500 |
| `KLAVIYO_INTEGRATION_INVALID_CONFIG` | Invalid integration configuration | 400 |
| `KLAVIYO_NETWORK_TIMEOUT` | Network request timeout | 408 |

### Error Examples

#### Authentication Error

```json
{
  "success": false,
  "error": "Access token has expired",
  "code": "KLAVIYO_AUTH_EXPIRED_TOKEN",
  "details": {
    "expires_at": "2024-01-15T09:30:00Z",
    "current_time": "2024-01-15T10:30:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Validation Error

```json
{
  "success": false,
  "error": "Invalid email format",
  "code": "KLAVIYO_VALIDATION_ERROR",
  "details": {
    "field": "email",
    "value": "invalid-email",
    "expected_format": "<EMAIL>"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Rate Limit Error

```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "code": "KLAVIYO_API_RATE_LIMITED",
  "details": {
    "limit": 150,
    "window": 60,
    "retry_after": 30
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Rate Limiting

### Klaviyo API Limits

- **Default Rate Limit**: 150 requests per second
- **Burst Limit**: 3,600 requests per hour
- **Token Refresh**: 10 requests per minute

### Rate Limit Headers

Responses include rate limit information:

```http
X-RateLimit-Limit: 150
X-RateLimit-Remaining: 149
X-RateLimit-Reset: 1642248600
Retry-After: 30
```

### Handling Rate Limits

When rate limited, implement exponential backoff:

```javascript
async function retryWithBackoff(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (error.code === 'KLAVIYO_API_RATE_LIMITED' && i < maxRetries - 1) {
        const delay = Math.pow(2, i) * 1000; // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw error;
    }
  }
}
```

## Examples

### Complete OAuth Flow

```javascript
// 1. Initiate OAuth
const authResponse = await fetch('/integrationOAuth/klaviyoOAuth/auth?state=csrf_token&integrationId=12345', {
  headers: {
    'Authorization': 'Bearer ' + jwtToken
  }
});

// 2. User is redirected to Klaviyo for authorization
// 3. Klaviyo redirects back to callback endpoint
// 4. Integration is saved and user is redirected to admin

// 5. Test the integration
const pingResponse = await fetch('/api/integrations/klaviyoOAuth/ping', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + jwtToken
  },
  body: JSON.stringify({
    integrationId: '60f1b2c3d4e5f6789abcdef0'
  })
});

const pingResult = await pingResponse.json();
console.log('Connection test:', pingResult.success);
```

### Subscribe a User

```javascript
// Subscribe a user to a Klaviyo list
const subscribeResponse = await fetch('/api/integrations/klaviyoOAuth/subscribe', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + jwtToken
  },
  body: JSON.stringify({
    integrationId: '60f1b2c3d4e5f6789abcdef0',
    profile: {
      email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Doe',
      phone_number: '+1234567890'
    },
    listId: 'XyZ123',
    options: {
      doubleOptin: false,
      sendWelcomeEmail: true
    }
  })
});

const subscribeResult = await subscribeResponse.json();
if (subscribeResult.success) {
  console.log('User subscribed:', subscribeResult.profileId);
} else {
  console.error('Subscription failed:', subscribeResult.error);
}
```

### GraphQL Integration Management

```javascript
// Get lists using GraphQL
const listsQuery = `
  query GetIntegrationLists($integrationType: String!, $integrationId: ID!) {
    lists(integrationType: $integrationType, integrationId: $integrationId) {
      id
      name
      created
      updated
    }
  }
`;

const listsResponse = await fetch('/graphql', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + jwtToken
  },
  body: JSON.stringify({
    query: listsQuery,
    variables: {
      integrationType: 'klaviyoOAuth',
      integrationId: '60f1b2c3d4e5f6789abcdef0'
    }
  })
});

const listsResult = await listsResponse.json();
console.log('Available lists:', listsResult.data.lists);
```

### Error Handling

```javascript
async function handleKlaviyoRequest(requestFn) {
  try {
    const result = await requestFn();
    return result;
  } catch (error) {
    switch (error.code) {
      case 'KLAVIYO_AUTH_EXPIRED_TOKEN':
        // Trigger token refresh
        await refreshToken();
        return await requestFn(); // Retry
        
      case 'KLAVIYO_API_RATE_LIMITED':
        // Wait and retry
        const retryAfter = error.details.retry_after || 60;
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        return await requestFn(); // Retry
        
      case 'KLAVIYO_API_NOT_FOUND':
        // Handle missing resource
        console.warn('Resource not found:', error.details);
        return null;
        
      default:
        // Log and re-throw unexpected errors
        console.error('Klaviyo API error:', error);
        throw error;
    }
  }
}
```
