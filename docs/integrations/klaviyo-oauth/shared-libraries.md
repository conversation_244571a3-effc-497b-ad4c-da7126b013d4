# Klaviyo OAuth Shared Libraries Documentation

This document details the shared libraries and configurations used across all services for the Klaviyo OAuth integration.

## Table of Contents

- [Overview](#overview)
- [Integration Configuration](#integration-configuration)
- [Field Mappings](#field-mappings)
- [Shared Utilities](#shared-utilities)
- [Constants](#constants)
- [Type Definitions](#type-definitions)
- [Development](#development)

## Overview

Shared libraries provide:
- Common configuration across services
- Field mapping definitions
- Reusable utilities and helpers
- Type definitions and interfaces
- Validation rules and constants

## Integration Configuration

### Configuration File

**File**: `libraries/integrations/src/configs/klaviyoOAuth.js`

Central configuration for Klaviyo OAuth integration.

<augment_code_snippet path="libraries/integrations/src/configs/klaviyoOAuth.js" mode="EXCERPT">
````javascript
const reservedFields = [
  'address1',
  'address2',
  'city',
  'country',
  'latitude',
  'longitude',
  'region',
  'zip',
  'email',
  'title',
  'phone_number',
  'organization',
  'first_name',
  'last_name',
  'timezone',
];

const locationFields = ['address1', 'address2', 'city', 'country', 'region', 'zip', 'timezone'];

module.exports = { reservedFields, locationFields };
````
</augment_code_snippet>

#### Purpose:

- **Field Definitions**: Standard Klaviyo field names
- **Validation Rules**: Field type and format validation
- **Mapping Guidelines**: Consistent field mapping across services

## Field Mappings

### Reserved Fields

Klaviyo has predefined fields that require special handling:

#### Profile Fields

```javascript
const profileFields = {
  // Identity fields
  email: {
    type: 'string',
    required: true,
    format: 'email',
    description: 'Primary email address'
  },
  phone_number: {
    type: 'string',
    format: 'phone',
    description: 'Phone number in E.164 format'
  },
  
  // Personal information
  first_name: {
    type: 'string',
    maxLength: 255,
    description: 'First name'
  },
  last_name: {
    type: 'string',
    maxLength: 255,
    description: 'Last name'
  },
  title: {
    type: 'string',
    maxLength: 255,
    description: 'Job title or prefix'
  },
  organization: {
    type: 'string',
    maxLength: 255,
    description: 'Company or organization'
  },
  
  // Location fields
  address1: {
    type: 'string',
    maxLength: 255,
    description: 'Street address line 1'
  },
  address2: {
    type: 'string',
    maxLength: 255,
    description: 'Street address line 2'
  },
  city: {
    type: 'string',
    maxLength: 255,
    description: 'City name'
  },
  region: {
    type: 'string',
    maxLength: 255,
    description: 'State or region'
  },
  country: {
    type: 'string',
    maxLength: 255,
    description: 'Country name'
  },
  zip: {
    type: 'string',
    maxLength: 255,
    description: 'Postal code'
  },
  timezone: {
    type: 'string',
    format: 'timezone',
    description: 'IANA timezone identifier'
  },
  
  // Geographic coordinates
  latitude: {
    type: 'number',
    minimum: -90,
    maximum: 90,
    description: 'Latitude coordinate'
  },
  longitude: {
    type: 'number',
    minimum: -180,
    maximum: 180,
    description: 'Longitude coordinate'
  }
};
```

### Location Fields

Special handling for location-related fields:

```javascript
const locationFields = [
  'address1',
  'address2', 
  'city',
  'country',
  'region',
  'zip',
  'timezone'
];

// Location field grouping
const locationFieldGroups = {
  address: ['address1', 'address2'],
  locality: ['city', 'region', 'zip'],
  country: ['country'],
  timezone: ['timezone']
};
```

### Custom Fields

Guidelines for custom field mapping:

```javascript
const customFieldRules = {
  // Naming conventions
  naming: {
    pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
    maxLength: 255,
    reservedPrefixes: ['klaviyo_', 'kl_', '$']
  },
  
  // Data types
  supportedTypes: [
    'string',
    'number',
    'boolean',
    'date',
    'datetime'
  ],
  
  // Validation rules
  validation: {
    string: { maxLength: 10000 },
    number: { 
      minimum: Number.MIN_SAFE_INTEGER,
      maximum: Number.MAX_SAFE_INTEGER
    },
    date: { format: 'YYYY-MM-DD' },
    datetime: { format: 'ISO8601' }
  }
};
```

## Shared Utilities

### Field Validation

```javascript
/**
 * Validates field name against Klaviyo rules
 * @param {string} fieldName - Field name to validate
 * @returns {Object} Validation result
 */
function validateFieldName(fieldName) {
  const errors = [];
  
  // Check reserved fields
  if (reservedFields.includes(fieldName)) {
    return {
      valid: true,
      type: 'reserved',
      field: fieldName
    };
  }
  
  // Check naming pattern
  if (!customFieldRules.naming.pattern.test(fieldName)) {
    errors.push('Field name must start with letter and contain only letters, numbers, and underscores');
  }
  
  // Check length
  if (fieldName.length > customFieldRules.naming.maxLength) {
    errors.push(`Field name must be ${customFieldRules.naming.maxLength} characters or less`);
  }
  
  // Check reserved prefixes
  const hasReservedPrefix = customFieldRules.naming.reservedPrefixes.some(
    prefix => fieldName.startsWith(prefix)
  );
  if (hasReservedPrefix) {
    errors.push('Field name cannot start with reserved prefix');
  }
  
  return {
    valid: errors.length === 0,
    type: 'custom',
    field: fieldName,
    errors
  };
}
```

### Data Type Conversion

```javascript
/**
 * Converts value to appropriate type for Klaviyo
 * @param {any} value - Value to convert
 * @param {string} type - Target type
 * @returns {any} Converted value
 */
function convertToKlaviyoType(value, type) {
  if (value === null || value === undefined) {
    return null;
  }
  
  switch (type) {
    case 'string':
      return String(value).substring(0, customFieldRules.validation.string.maxLength);
      
    case 'number':
      const num = Number(value);
      if (isNaN(num)) return null;
      return Math.max(
        customFieldRules.validation.number.minimum,
        Math.min(customFieldRules.validation.number.maximum, num)
      );
      
    case 'boolean':
      return Boolean(value);
      
    case 'date':
      const date = new Date(value);
      if (isNaN(date.getTime())) return null;
      return date.toISOString().split('T')[0];
      
    case 'datetime':
      const datetime = new Date(value);
      if (isNaN(datetime.getTime())) return null;
      return datetime.toISOString();
      
    default:
      return value;
  }
}
```

### Field Mapping Utilities

```javascript
/**
 * Maps OptimOnk fields to Klaviyo fields
 * @param {Object} data - Source data
 * @param {Array} bindings - Field bindings configuration
 * @returns {Object} Mapped data
 */
function mapFieldsToKlaviyo(data, bindings) {
  const mapped = {};
  
  bindings.forEach(binding => {
    const { fieldId, externalId, type = 'string' } = binding;
    const value = data[fieldId];
    
    if (value !== undefined && value !== null && value !== '') {
      mapped[externalId] = convertToKlaviyoType(value, type);
    }
  });
  
  return mapped;
}

/**
 * Validates field bindings configuration
 * @param {Array} bindings - Field bindings to validate
 * @returns {Object} Validation result
 */
function validateFieldBindings(bindings) {
  const errors = [];
  const usedFields = new Set();
  
  bindings.forEach((binding, index) => {
    const { fieldId, externalId, required } = binding;
    
    // Check for duplicate external fields
    if (usedFields.has(externalId)) {
      errors.push({
        index,
        field: externalId,
        error: 'Duplicate field mapping'
      });
    }
    usedFields.add(externalId);
    
    // Validate external field name
    const fieldValidation = validateFieldName(externalId);
    if (!fieldValidation.valid) {
      errors.push({
        index,
        field: externalId,
        error: fieldValidation.errors.join(', ')
      });
    }
    
    // Check required fields
    if (required && !fieldId) {
      errors.push({
        index,
        field: externalId,
        error: 'Required field must be mapped'
      });
    }
  });
  
  return {
    valid: errors.length === 0,
    errors
  };
}
```

## Constants

### API Configuration

```javascript
const API_CONSTANTS = {
  // Endpoints
  BASE_URL: 'https://a.klaviyo.com',
  API_BASE_URL: 'https://a.klaviyo.com/api',
  AUTH_URL: 'https://www.klaviyo.com/oauth/authorize',
  TOKEN_URL: 'https://a.klaviyo.com/oauth/token',
  
  // API Version
  DEFAULT_API_VERSION: '2025-04-15',
  SUPPORTED_VERSIONS: [
    '2023-12-15',
    '2024-02-15',
    '2024-05-15',
    '2024-07-15',
    '2025-01-15',
    '2025-04-15'
  ],
  
  // Rate Limits
  RATE_LIMITS: {
    default: 150, // requests per second
    burst: 3600   // requests per hour
  },
  
  // Timeouts
  TIMEOUTS: {
    request: 30000,    // 30 seconds
    token_refresh: 10000 // 10 seconds
  }
};
```

### OAuth Configuration

```javascript
const OAUTH_CONSTANTS = {
  // Grant types
  GRANT_TYPES: {
    AUTHORIZATION_CODE: 'authorization_code',
    REFRESH_TOKEN: 'refresh_token'
  },
  
  // Response types
  RESPONSE_TYPES: {
    CODE: 'code'
  },
  
  // PKCE
  PKCE: {
    CODE_CHALLENGE_METHOD: 'S256',
    CODE_VERIFIER_LENGTH: 128,
    STATE_LENGTH: 32
  },
  
  // Scopes
  SCOPES: {
    PROFILES_READ: 'profiles:read',
    PROFILES_WRITE: 'profiles:write',
    LISTS_READ: 'lists:read',
    LISTS_WRITE: 'lists:write',
    SEGMENTS_READ: 'segments:read',
    SUBSCRIPTIONS_WRITE: 'subscriptions:write'
  },
  
  // Default scope combinations
  DEFAULT_SCOPES: [
    'subscriptions:write',
    'profiles:read',
    'profiles:write',
    'lists:read',
    'lists:write',
    'segments:read'
  ]
};
```

### Error Codes

```javascript
const ERROR_CODES = {
  // Authentication errors
  AUTH_INVALID_CLIENT: 'KLAVIYO_AUTH_INVALID_CLIENT',
  AUTH_INVALID_GRANT: 'KLAVIYO_AUTH_INVALID_GRANT',
  AUTH_INVALID_SCOPE: 'KLAVIYO_AUTH_INVALID_SCOPE',
  AUTH_EXPIRED_TOKEN: 'KLAVIYO_AUTH_EXPIRED_TOKEN',
  
  // API errors
  API_RATE_LIMITED: 'KLAVIYO_API_RATE_LIMITED',
  API_INVALID_REQUEST: 'KLAVIYO_API_INVALID_REQUEST',
  API_NOT_FOUND: 'KLAVIYO_API_NOT_FOUND',
  API_SERVER_ERROR: 'KLAVIYO_API_SERVER_ERROR',
  
  // Integration errors
  INTEGRATION_INVALID_CONFIG: 'KLAVIYO_INTEGRATION_INVALID_CONFIG',
  INTEGRATION_FIELD_MAPPING: 'KLAVIYO_INTEGRATION_FIELD_MAPPING',
  INTEGRATION_LIST_NOT_FOUND: 'KLAVIYO_INTEGRATION_LIST_NOT_FOUND',
  
  // Network errors
  NETWORK_TIMEOUT: 'KLAVIYO_NETWORK_TIMEOUT',
  NETWORK_CONNECTION: 'KLAVIYO_NETWORK_CONNECTION'
};
```

## Type Definitions

### TypeScript Interfaces

```typescript
// Integration settings
interface KlaviyoOAuthSettings {
  name: string;
  access_token: string;
  refresh_token: string;
  expires_at: Date;
  scope: string;
  listId?: string;
  publicApiKey?: string;
  bindings: FieldBinding[];
  options: IntegrationOptions;
}

// Field binding
interface FieldBinding {
  fieldId: string;
  externalId: string;
  type: FieldType;
  required: boolean;
  defaultValue?: any;
}

// Field types
type FieldType = 'string' | 'number' | 'boolean' | 'date' | 'datetime';

// Integration options
interface IntegrationOptions {
  optin: boolean;
  doubleOptin: boolean;
  sendWelcomeEmail: boolean;
  updateExisting: boolean;
}

// API response types
interface KlaviyoList {
  id: string;
  name: string;
  created: string;
  updated: string;
}

interface KlaviyoProfile {
  id: string;
  email?: string;
  phone_number?: string;
  first_name?: string;
  last_name?: string;
  [key: string]: any;
}

// Error types
interface KlaviyoError {
  code: string;
  message: string;
  context?: string;
  originalError?: Error;
  response?: {
    status: number;
    data: any;
  };
}
```

### Validation Schemas

```javascript
// JSON Schema for settings validation
const settingsSchema = {
  type: 'object',
  required: ['name', 'access_token', 'refresh_token'],
  properties: {
    name: {
      type: 'string',
      minLength: 1,
      maxLength: 255
    },
    access_token: {
      type: 'string',
      minLength: 1
    },
    refresh_token: {
      type: 'string',
      minLength: 1
    },
    expires_at: {
      type: 'string',
      format: 'date-time'
    },
    listId: {
      type: 'string',
      pattern: '^[a-zA-Z0-9]+$'
    },
    bindings: {
      type: 'array',
      items: {
        type: 'object',
        required: ['fieldId', 'externalId'],
        properties: {
          fieldId: { type: 'string' },
          externalId: { type: 'string' },
          type: {
            type: 'string',
            enum: ['string', 'number', 'boolean', 'date', 'datetime']
          },
          required: { type: 'boolean' }
        }
      }
    }
  }
};
```

## Development

### Adding New Field Types

1. **Update Type Definitions**:

```javascript
// Add to supported types
const supportedTypes = [
  'string',
  'number',
  'boolean',
  'date',
  'datetime',
  'array',    // New type
  'object'    // New type
];
```

2. **Add Conversion Logic**:

```javascript
function convertToKlaviyoType(value, type) {
  switch (type) {
    // Existing cases...
    
    case 'array':
      return Array.isArray(value) ? value : [value];
      
    case 'object':
      return typeof value === 'object' ? value : { value };
      
    default:
      return value;
  }
}
```

3. **Update Validation**:

```javascript
const validation = {
  // Existing validations...
  
  array: { 
    maxItems: 100,
    itemType: 'string'
  },
  object: {
    maxProperties: 50,
    maxDepth: 3
  }
};
```

### Creating Custom Validators

```javascript
/**
 * Custom validator for email fields
 */
function validateEmail(value) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return {
    valid: emailRegex.test(value),
    error: 'Invalid email format'
  };
}

/**
 * Custom validator for phone numbers
 */
function validatePhoneNumber(value) {
  // E.164 format validation
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return {
    valid: phoneRegex.test(value),
    error: 'Phone number must be in E.164 format (+1234567890)'
  };
}

/**
 * Register custom validators
 */
const customValidators = {
  email: validateEmail,
  phone: validatePhoneNumber
};
```

### Testing Utilities

```javascript
/**
 * Test data generators
 */
const testDataGenerators = {
  generateProfile() {
    return {
      email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Doe',
      phone_number: '+1234567890'
    };
  },
  
  generateBindings() {
    return [
      {
        fieldId: 'email',
        externalId: 'email',
        type: 'string',
        required: true
      },
      {
        fieldId: 'firstName',
        externalId: 'first_name',
        type: 'string',
        required: false
      }
    ];
  },
  
  generateSettings() {
    return {
      name: 'Test Integration',
      access_token: 'test_token',
      refresh_token: 'refresh_token',
      expires_at: new Date(Date.now() + 3600000),
      listId: 'test_list_id',
      bindings: this.generateBindings()
    };
  }
};
```

### Documentation Helpers

```javascript
/**
 * Generate field documentation
 */
function generateFieldDocs(fields) {
  return fields.map(field => ({
    name: field.name,
    type: field.type,
    required: field.required,
    description: field.description,
    example: field.example
  }));
}

/**
 * Generate API documentation
 */
function generateApiDocs(endpoints) {
  return endpoints.map(endpoint => ({
    method: endpoint.method,
    path: endpoint.path,
    description: endpoint.description,
    parameters: endpoint.parameters,
    responses: endpoint.responses
  }));
}
```
