# Klaviyo OAuth Frontend Service Documentation

This document details the frontend implementation of the Klaviyo OAuth integration in the `om-frontend` package.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Components](#components)
- [Integration Handlers](#integration-handlers)
- [Auth Providers](#auth-providers)
- [Conversion Processing](#conversion-processing)
- [Configuration](#configuration)
- [Development](#development)

## Overview

The frontend service handles:
- Conversion processing and subscriber management
- OAuth token management and refresh
- API communication with Klaviyo
- Profile creation and subscription handling
- Multi-list support and field mapping
- Error handling and retry logic

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                   om-frontend Package                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ KlaviyoOAuth    │    │ Abstract OAuth  │                │
│  │ Handler         │    │ Integration     │                │
│  │                 │◄───┤                 │                │
│  │ • Token mgmt    │    │ • Base methods  │                │
│  │ • Conversion    │    │ • Token refresh │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Auth Providers  │    │ Conversion      │                │
│  │                 │    │ Handlers        │                │
│  │ • OAuth Provider│◄───┤                 │                │
│  │ • API Key Prov. │    │ • Bulk Subscribe│                │
│  │ • Header mgmt   │    │ • Instant Consent│               │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Abstract Base   │    │ Binding Helper  │                │
│  │                 │    │                 │                │
│  │ • Common logic  │    │ • Field mapping │                │
│  │ • Error handling│    │ • Data binding  │                │
│  │ • Validation    │    │ • Type conversion│               │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Components

### 1. Main Integration Handler

**File**: `src/handlers/conversions/integrations/KlaviyoOAuth.ts`

Primary handler for OAuth-based Klaviyo integration.

<augment_code_snippet path="packages/om-frontend/src/handlers/conversions/integrations/KlaviyoOAuth.ts" mode="EXCERPT">
````typescript
export default class KlaviyoOAuth extends AbstractOAuthIntegration implements IIntegrationAdapter {
  private accessToken: string = '';
  private PHONE_NUMBER = 'phone_number';

  getType() {
    return 'klaviyoOAuth';
  }

  protected async getAccessToken() {
    let token;
    const isTokenExpired = await this.isTokenExpired();
    if (isTokenExpired) {
      try {
        token = await this.refreshToken();
      } catch (e) {
        const errorMessage = e.response && e.response.data ? e.response.data : e.message;
        this.logWarn('tokenRefreshError', {
          received: errorMessage,
        });
      }
    } else {
      token = this.settings.getGlobal('access_token');
    }

    return token;
  }
````
</augment_code_snippet>

#### Key Features:

- **Token Management**: Automatic token refresh and validation
- **Multi-list Support**: Dynamic list selection based on subscriber data
- **Error Handling**: Comprehensive error logging and recovery
- **Feature Flags**: Support for different API modes

### 2. Abstract OAuth Integration

**File**: `src/handlers/conversions/integrations/AbstractOAuthIntegration.ts`

Base class for OAuth-based integrations.

<augment_code_snippet path="packages/om-frontend/src/handlers/conversions/integrations/AbstractOAuthIntegration.ts" mode="EXCERPT">
````typescript
export default abstract class AbstractOAuthIntegration extends AbstractIntegration {
  protected async getAccessToken() {
    let token;
    const isTokenExpired = await this.isTokenExpired();
    if (isTokenExpired) {
      try {
        token = await this.refreshToken();
      } catch (e) {
        const errorMessage = e.response && e.response.data ? e.response.data : e.message;
        this.logWarn(
          `tokenRefreshError ${this.getType().toLowerCase()}, sentBack: ${errorMessage}`,
        );
      }
    } else {
      token = this.settings.getGlobal('accessToken');
    }
    return token;
  }
````
</augment_code_snippet>

#### Shared Functionality:

- **Token Lifecycle**: Expiration checking and refresh logic
- **Database Operations**: Token persistence and updates
- **Error Handling**: Standardized error processing

## Integration Handlers

### KlaviyoOAuth Handler

Main handler class that orchestrates the conversion process.

#### Key Methods:

##### `async handle(campaignId, subscriber)`

Main entry point for processing conversions.

**Process**:
1. Extract phone number from field bindings
2. Set multi-list ID based on subscriber data
3. Validate subscriber has email or phone
4. Get valid OAuth access token
5. Choose appropriate conversion handler (Instant Consent vs Bulk Subscribe)
6. Process subscription

**Parameters**:
- `campaignId` (ObjectId): Campaign identifier
- `subscriber` (ISubscriber): Subscriber data

**Returns**: `Promise<HandlingResult>` - Processing result

##### `async getAccessToken()`

Retrieves valid access token, refreshing if necessary.

**Returns**: `Promise<string>` - Valid access token

**Logic**:
- Check token expiration
- Refresh if expired
- Handle refresh errors gracefully
- Return cached token if valid

##### `async refreshToken()`

Refreshes OAuth access token using refresh token.

**Returns**: `Promise<string>` - New access token

**Process**:
1. Make POST request to Klaviyo token endpoint
2. Calculate new expiration time
3. Update database with new tokens
4. Return new access token

##### `protected getPhoneNumberFromBindings(subscriber)`

Extracts phone number from field bindings.

**Parameters**:
- `subscriber` (ISubscriber): Subscriber data

**Returns**: `string` - Phone number or empty string

## Auth Providers

### KlaviyoOAuthProvider

**File**: `src/handlers/conversions/integrations/klaviyo/KlaviyoOAuthProvider.ts`

OAuth-specific authentication provider.

<augment_code_snippet path="packages/om-frontend/src/handlers/conversions/integrations/klaviyo/KlaviyoOAuthProvider.ts" mode="EXCERPT">
````typescript
import { AbstractKlaviyoAuthProvider } from './AbstractKlaviyoAuthProvider';

export default class KlaviyoOAuthProvider extends AbstractKlaviyoAuthProvider {
  getAuthHeader(): string {
    return `Bearer ${this.settings.getGlobal('accessToken')}`;
  }
}
````
</augment_code_snippet>

#### Features:

- **Bearer Token Authentication**: Uses OAuth access token
- **Header Management**: Consistent authorization headers
- **Settings Integration**: Retrieves token from integration settings

### AbstractKlaviyoAuthProvider

**File**: `src/handlers/conversions/integrations/klaviyo/AbstractKlaviyoAuthProvider.ts`

Base class for Klaviyo authentication providers.

<augment_code_snippet path="packages/om-frontend/src/handlers/conversions/integrations/klaviyo/AbstractKlaviyoAuthProvider.ts" mode="EXCERPT">
````typescript
export abstract class AbstractKlaviyoAuthProvider {
  protected settings: IntegrationSettings;
  constructor(settings: IntegrationSettings) {
    this.settings = settings;
  }

  abstract getAuthHeader(): string;
}
````
</augment_code_snippet>

#### Purpose:

- **Abstraction**: Common interface for different auth methods
- **Flexibility**: Support for OAuth and API key authentication
- **Consistency**: Standardized header generation

## Conversion Processing

### Bulk Subscribe Handler

**File**: `src/handlers/conversions/integrations/klaviyo/KlaviyoBulkSubscribe.ts`

Handles bulk profile import and subscription.

<augment_code_snippet path="packages/om-frontend/src/handlers/conversions/integrations/klaviyo/KlaviyoBulkSubscribe.ts" mode="EXCERPT">
````typescript
export default class KlaviyoBulkSubscribe extends AbstractKlaviyoIntegration {
  private profile: { id?: string; email?: string; phoneNumber?: string } = {};
  private subscriptions = {
    email: false,
    sms: false,
  };
  private lastConfig?: { data?: any };

  async upsertProfile(profile: any): Promise<string> {
    const axiosInstance = axios.create(this.getAxiosConfig());

    const {
      data: {
        data: { id },
      },
    } = await axiosInstance.request({
      method: 'post',
      url: '/profile-import',
      data: profile,
    });

    return id;
  }
````
</augment_code_snippet>

#### Process:

1. **Profile Upsert**: Create or update profile in Klaviyo
2. **Subscription Setup**: Configure email/SMS subscriptions
3. **List Assignment**: Add profile to specified lists
4. **Error Handling**: Manage API failures gracefully

### Instant Consent Handler

**File**: `src/handlers/conversions/integrations/klaviyo/KlaviyoInstantConsent.ts`

Handles real-time subscription with instant consent.

<augment_code_snippet path="packages/om-frontend/src/handlers/conversions/integrations/klaviyo/KlaviyoInstantConsent.ts" mode="EXCERPT">
````typescript
export default class KlaviyoInstantConsent extends AbstractKlaviyoIntegration {
  private lastConfig?: { data?: any };

  protected async prepareSubscription(subscriber: ISubscriber): Promise<void> {
    const config = this.getSubscribeConfig(subscriber);
    this.lastConfig = { data: config.data };
  }

  buildPayload(subscriber: ISubscriber) {
    const profile: any = {};
    const bindings = this.settings.getSpecific('bindings');

    if (Array.isArray(bindings)) {
      BindingHelper.setBindings({
        body: this.body,
        campaignData: this.campaignData,
        subscriber,
        bindings,
        properties: profile,
      });
    }
````
</augment_code_snippet>

#### Features:

- **Real-time Processing**: Immediate subscription handling
- **Consent Management**: Proper consent tracking
- **Field Binding**: Dynamic field mapping
- **Validation**: Input validation and error handling

### Abstract Klaviyo Integration

**File**: `src/handlers/conversions/integrations/klaviyo/AbstractKlaviyoIntegration.ts`

Base class for Klaviyo conversion handlers.

#### Shared Functionality:

- **Configuration Management**: Axios setup and headers
- **Error Handling**: Consistent error processing
- **Validation**: Common validation logic
- **Logging**: Structured logging for debugging

## Configuration

### Integration Settings

The integration uses the following settings structure:

```typescript
interface KlaviyoOAuthSettings {
  // OAuth tokens
  access_token: string;
  refresh_token: string;
  expires_at: Date;
  
  // Configuration
  name: string;
  listId: string;
  publicApiKey?: string;
  
  // Field mappings
  bindings: Array<{
    fieldId: string;
    externalId: string;
    required: boolean;
  }>;
  
  // Options
  optin: boolean;
  doubleOptin: boolean;
}
```

### Feature Flags

The integration supports feature flags for different modes:

- **`KLAVIYO_NEW_SYNCHRONOUS_API`**: Enables instant consent mode
- **Multi-list Support**: Dynamic list selection based on data

### API Configuration

```typescript
const API_CONFIG = {
  baseURL: 'https://a.klaviyo.com/',
  headers: {
    'Content-Type': 'application/json',
    'revision': '2025-04-15',
    'x-klaviyo-partner-key': 'PARTNER_KEY'
  }
};
```

## Development

### Adding New Conversion Handlers

1. **Create Handler Class**:

```typescript
export default class NewKlaviyoHandler extends AbstractKlaviyoIntegration {
  protected async prepareSubscription(subscriber: ISubscriber): Promise<void> {
    // Preparation logic
  }

  protected getSubscribeConfig(subscriber: ISubscriber) {
    return {
      method: 'post',
      url: '/new-endpoint',
      data: this.buildPayload(subscriber)
    };
  }

  buildPayload(subscriber: ISubscriber) {
    // Build API payload
  }
}
```

2. **Update Main Handler**:

```typescript
// In KlaviyoOAuth.handle()
if (someCondition) {
  const newHandler = new NewKlaviyoHandler(
    this.settings,
    this.databaseId,
    this.body,
    new KlaviyoOAuthProvider(this.settings)
  );
  return newHandler.handle(campaignId, subscriber);
}
```

### Token Management

#### Custom Token Refresh Logic

```typescript
protected async customRefreshToken(): Promise<string> {
  try {
    const response = await axios.post(
      'https://a.klaviyo.com/oauth/token',
      {
        grant_type: 'refresh_token',
        refresh_token: this.settings.getGlobal('refresh_token'),
      },
      {
        headers: {
          Authorization: this._getBasicAuthorizationHeader(),
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    // Update settings with new tokens
    await this.updateTokens(response.data);
    
    return response.data.access_token;
  } catch (error) {
    this.logError('Token refresh failed', error);
    throw error;
  }
}
```

#### Token Expiration Checking

```typescript
protected async isTokenExpired(): Promise<boolean> {
  const expiresAt = new Date(this.settings.getGlobal('expires_at'));
  const now = new Date();
  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
  
  return (expiresAt.getTime() - bufferTime) <= now.getTime();
}
```

### Error Handling

#### Custom Error Types

```typescript
class KlaviyoConversionError extends Error {
  constructor(
    message: string,
    public readonly subscriber: ISubscriber,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'KlaviyoConversionError';
  }
}
```

#### Error Recovery

```typescript
async handleWithRetry(
  campaignId: ObjectId, 
  subscriber: ISubscriber,
  maxRetries: number = 3
): Promise<HandlingResult> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await this.handle(campaignId, subscriber);
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}
```

### Testing

#### Unit Tests

```typescript
describe('KlaviyoOAuth', () => {
  let handler: KlaviyoOAuth;
  let mockSettings: IntegrationSettings;

  beforeEach(() => {
    mockSettings = new IntegrationSettings({
      access_token: 'test_token',
      refresh_token: 'refresh_token',
      expires_at: new Date(Date.now() + 3600000)
    });
    
    handler = new KlaviyoOAuth(mockSettings, 'test_db_id', {});
  });

  test('should get valid access token', async () => {
    const token = await handler.getAccessToken();
    expect(token).toBe('test_token');
  });

  test('should refresh expired token', async () => {
    // Set expired token
    mockSettings.setGlobal('expires_at', new Date(Date.now() - 1000));
    
    // Mock refresh response
    jest.spyOn(handler, 'refreshToken').mockResolvedValue('new_token');
    
    const token = await handler.getAccessToken();
    expect(token).toBe('new_token');
  });
});
```

#### Integration Tests

```typescript
describe('Klaviyo Integration Flow', () => {
  test('should complete full conversion flow', async () => {
    const subscriber: ISubscriber = {
      email: '<EMAIL>',
      customFields: {
        'first_name': 'John',
        'last_name': 'Doe'
      }
    };

    const result = await handler.handle(campaignId, subscriber);
    
    expect(result.success).toBe(true);
    expect(result.response.status).toBe(200);
  });
});
```

### Debugging

#### Enable Debug Logging

```typescript
// Add debug logging
console.debug('Klaviyo OAuth Debug:', {
  tokenExpired: await this.isTokenExpired(),
  hasRefreshToken: !!this.settings.getGlobal('refresh_token'),
  subscriber: subscriber.email
});
```

#### Monitor API Calls

```typescript
// Add request/response interceptors
this.api.interceptors.request.use(request => {
  console.log('Klaviyo API Request:', request);
  return request;
});

this.api.interceptors.response.use(
  response => {
    console.log('Klaviyo API Response:', response.status);
    return response;
  },
  error => {
    console.error('Klaviyo API Error:', error.response?.data);
    return Promise.reject(error);
  }
);
```
