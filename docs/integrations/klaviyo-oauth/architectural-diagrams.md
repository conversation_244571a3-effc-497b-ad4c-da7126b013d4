# Klaviyo OAuth Integration - Architectural Diagrams

This document provides visual architectural diagrams that explain how data moves between Klaviyo and OptimOnk systems, as required for Klaviyo app submission.

## Table of Contents

- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Data Flow Sequence](#data-flow-sequence)
- [Component Relationships](#component-relationships)
- [Use Cases](#use-cases)
- [Security Considerations](#security-considerations)

## Overview

The OptimOnk-Klaviyo OAuth integration enables seamless data synchronization between OptimOnk's conversion optimization platform and Klaviyo's email marketing platform. The integration follows OAuth 2.0 with PKCE for secure authentication and uses Klaviyo's REST API for data operations.

### Key Integration Points

1. **OAuth Authentication**: Secure token-based authentication using OAuth 2.0 with PKCE
2. **List Management**: Retrieval and display of Klaviyo email lists
3. **Profile Subscription**: Automatic subscription of website visitors to Klaviyo lists
4. **Data Synchronization**: Real-time transfer of conversion data from OptimOnk to Klaviyo

## System Architecture

The following diagram shows the high-level architecture and data flow between OptimOnk and Klaviyo systems:

### Main Architecture Diagram

This diagram illustrates the complete system architecture, showing how data moves between OptimOnk services and Klaviyo:

- **OAuth Flow**: Steps 1-10 show the initial authentication setup
- **Data Sync Flow**: Steps 11-18 show ongoing data synchronization
- **API Endpoints**: Shows the specific Klaviyo endpoints used

The architecture follows a microservices pattern with clear separation of concerns:

- **Frontend Services**: Handle user interfaces and visitor interactions
- **Backend Services**: Manage OAuth flow and API communications
- **Data Layer**: Store integration settings and cache OAuth state
- **External Integration**: Secure communication with Klaviyo APIs

### Key Components

#### OptimOnk Services

1. **Admin Interface (om-admin)**
   - Vue.js application for integration setup
   - Multi-step OAuth configuration wizard
   - List selection and field mapping interface

2. **Frontend Service (om-frontend)**
   - JavaScript SDK for campaign rendering
   - Conversion tracking and data collection
   - Real-time visitor interaction handling

3. **Backend Service (om-backend)**
   - Node.js/Express API server
   - OAuth 2.0 implementation with PKCE
   - Integration management and data processing

4. **Data Storage**
   - **MongoDB**: Persistent storage for user accounts, integration settings, and OAuth tokens
   - **Redis**: Temporary storage for OAuth state, PKCE codes, and session cache

#### Klaviyo Integration Points

1. **OAuth Endpoints**
   - `https://www.klaviyo.com/oauth/authorize` - User authorization
   - `https://a.klaviyo.com/oauth/token` - Token exchange and refresh

2. **API Endpoints**
   - `https://a.klaviyo.com/api/lists` - Email list retrieval
   - `https://a.klaviyo.com/api/profile-subscription-bulk-create-jobs` - Profile subscription

## Data Flow Sequence

The following sequence diagram shows the detailed data flow for both main use cases:

### Use Case 1: OAuth Setup & List Retrieval

**Steps 1-20**: Complete OAuth authentication and list setup process

1. **Customer initiates integration** in OptimOnk admin interface
2. **OAuth flow begins** with PKCE code generation and state storage
3. **User authorization** on Klaviyo platform
4. **Token exchange** and secure storage of credentials
5. **List retrieval** and display for customer selection
6. **Configuration save** with selected list and field mappings

### Use Case 2: Visitor Signup & Profile Subscription

**Steps 21-32**: Real-time conversion processing and data sync

1. **Visitor interaction** with OptimOnk campaign
2. **Conversion processing** with integration settings retrieval
3. **Token validation** and automatic refresh if needed
4. **Profile subscription** to selected Klaviyo list
5. **Result logging** and confirmation to visitor

### Error Handling & Recovery

The system includes comprehensive error handling:

- **Token expiration**: Automatic refresh using stored refresh tokens
- **API errors**: Exponential backoff and retry logic
- **Network failures**: Graceful degradation and error reporting
- **Invalid configurations**: Clear error messages and recovery guidance

## Component Relationships

The component diagram shows the detailed relationships between system components:

### Layer Architecture

1. **Frontend Layer**
   - Admin Interface: Integration setup and management
   - Campaign Frontend: Visitor interaction and conversion tracking

2. **API Layer**
   - Backend API: OAuth endpoints and integration management
   - GraphQL API: Data queries and settings management

3. **Service Layer**
   - OAuth Service: PKCE implementation and token management
   - Integration Service: API communication and data transformation
   - Conversion Service: Profile processing and subscription logic

4. **Data Layer**
   - MongoDB: Persistent data storage
   - Redis: Temporary state and caching

### Data Flow Patterns

1. **Setup Flow**: Admin UI → OAuth Service → Klaviyo OAuth → Integration Service → Klaviyo API
2. **Conversion Flow**: Campaign UI → Conversion Service → Klaviyo API
3. **Management Flow**: Admin UI → GraphQL API → Integration Service → MongoDB

## Use Cases

### Use Case 1: New Klaviyo Integration Added, Showing Lists

**Endpoint**: `https://a.klaviyo.com/api/lists`

**Flow**:
1. Customer adds new Klaviyo integration in OptimOnk admin
2. OAuth authentication flow completes successfully
3. System retrieves available email lists from Klaviyo
4. Lists are displayed for customer selection
5. Customer selects target list and configures field mappings
6. Integration settings are saved for future conversions

**Data Transferred**:
- **To Klaviyo**: OAuth credentials, API requests for list data
- **From Klaviyo**: Email list information (ID, name, creation date, subscriber count)

### Use Case 2: Visitor Signs Up in Campaign

**Endpoint**: `https://a.klaviyo.com/api/profile-subscription-bulk-create-jobs`

**Flow**:
1. Website visitor fills out OptimOnk campaign form
2. Frontend service captures conversion data
3. Backend retrieves integration settings and validates tokens
4. Profile data is formatted according to field mappings
5. Subscription request is sent to Klaviyo with profile data
6. Klaviyo confirms subscription and returns job status
7. Result is logged and confirmation shown to visitor

**Data Transferred**:
- **To Klaviyo**: Profile information (email, name, custom fields), list ID, subscription preferences
- **From Klaviyo**: Subscription confirmation, profile ID, job status

## Security Considerations

### OAuth 2.0 with PKCE

- **PKCE Implementation**: Prevents authorization code interception attacks
- **State Parameter**: CSRF protection during OAuth flow
- **Secure Storage**: Tokens encrypted and stored securely in MongoDB
- **Token Refresh**: Automatic renewal without user intervention

### Data Protection

- **Encryption in Transit**: All API communications use HTTPS/TLS
- **Encryption at Rest**: Sensitive data encrypted in database
- **Access Control**: Role-based access to integration settings
- **Audit Logging**: Complete audit trail of all data operations

### API Security

- **Rate Limiting**: Respect Klaviyo API rate limits with exponential backoff
- **Error Handling**: Secure error messages without sensitive data exposure
- **Token Management**: Secure token storage and automatic refresh
- **Scope Limitation**: Request only necessary OAuth scopes

## Technical Specifications

### API Versions

- **Klaviyo API Version**: 2025-04-15 (configurable)
- **OAuth Version**: OAuth 2.0 with PKCE (RFC 7636)
- **Supported Scopes**: 
  - `profiles:read` - Read profile information
  - `profiles:write` - Create and update profiles
  - `lists:read` - Read list information
  - `lists:write` - Manage list subscriptions
  - `subscriptions:write` - Create subscriptions
  - `segments:read` - Read segment information

### Data Formats

- **API Communication**: JSON over HTTPS
- **Field Mapping**: Configurable mapping between OptimOnk and Klaviyo fields
- **Profile Data**: Supports all Klaviyo profile fields including custom properties
- **Error Responses**: Structured error format with context and recovery guidance

### Performance Characteristics

- **Response Time**: < 2 seconds for typical operations
- **Throughput**: Supports high-volume conversion processing
- **Reliability**: 99.9% uptime with automatic failover
- **Scalability**: Horizontal scaling across multiple service instances

## Monitoring and Observability

### Health Checks

- **Service Health**: Regular health checks for all components
- **API Connectivity**: Continuous monitoring of Klaviyo API availability
- **Token Validity**: Proactive token refresh before expiration
- **Data Integrity**: Validation of data transfer completeness

### Metrics and Logging

- **Performance Metrics**: Response times, throughput, error rates
- **Business Metrics**: Conversion rates, subscription success rates
- **Security Metrics**: Authentication failures, token refresh rates
- **Operational Metrics**: System resource usage, scaling events

### Alerting

- **Critical Alerts**: Service outages, authentication failures
- **Warning Alerts**: High error rates, performance degradation
- **Informational Alerts**: Successful deployments, configuration changes

This architectural documentation provides a comprehensive view of how data flows between OptimOnk and Klaviyo systems, ensuring secure, reliable, and efficient integration for email marketing automation.
