import { merge as _merge } from 'lodash-es';
import base from './base';
import { fallbackFields, fallbackValue } from './utils';

export default (ctx) => {
  return _merge({}, base(ctx), {
    subElements: {
      option: {
        desktop: {
          background: fallbackFields(
            ctx,
            'element.desktop.background',
            'elementDefaults.OmSurvey.subElements.option.desktop.background',
            'elementDefaults.OmButton.desktop.background',
          ),
          border: fallbackFields(
            ctx,
            'element.desktop.border',
            'elementDefaults.OmSurvey.subElements.option.desktop.border',
            'elementDefaults.OmButton.desktop.border',
          ),
          shadow: fallbackFields(
            ctx,
            'element.desktop.shadow',
            'elementDefaults.OmSurvey.subElements.option.desktop.shadow',
            'elementDefaults.OmButton.desktop.shadow',
          ),
          textAlign: fallbackValue(
            ctx,
            'element.desktop.textAlign',
            'elementDefaults.OmSurvey.subElements.option.desktop.textAlign',
            'elementDefaults.OmButton.desktop.textAlign',
          ),
          color: fallbackValue(
            ctx,
            'element.desktop.color',
            'elementDefaults.OmSurvey.subElements.option.desktop.color',
            'elementDefaults.OmButton.desktop.color',
          ),
          fontFamily: fallbackValue(
            ctx,
            'element.desktop.fontFamily',
            'elementDefaults.OmSurvey.subElements.option.desktop.fontFamily',
            'elementDefaults.OmButton.desktop.fontFamily',
          ),
          fontSize: fallbackValue(
            ctx,
            'element.desktop.fontSize',
            'elementDefaults.OmSurvey.subElements.option.desktop.fontSize',
            'elementDefaults.OmButton.desktop.fontSize',
          ),
          textWeight: fallbackValue(
            ctx,
            'element.desktop.textWeight',
            'elementDefaults.OmSurvey.subElements.option.desktop.textWeight',
            'elementDefaults.OmButton.desktop.textWeight',
          ),
          fontItalic: fallbackValue(
            ctx,
            'element.desktop.fontItalic',
            'elementDefaults.OmSurvey.subElements.option.desktop.fontItalic',
            'elementDefaults.OmButton.desktop.fontItalic',
          ),
          textDecoration: fallbackValue(
            ctx,
            'element.desktop.textDecoration',
            'elementDefaults.OmSurvey.subElements.option.desktop.textDecoration',
            'elementDefaults.OmButton.desktop.textDecoration',
          ),
          height: fallbackValue(
            ctx,
            'element.desktop.height',
            'elementDefaults.OmSurvey.subElements.option.desktop.height',
            'elementDefaults.OmButton.desktop.height',
          ),
        },
      },
    },
  });
};
