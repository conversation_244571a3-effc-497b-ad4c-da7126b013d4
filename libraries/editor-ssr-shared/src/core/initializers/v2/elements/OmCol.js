import { initWithDefaults } from '../utils';
import settings from '../settings';

export default (value) => {
  const element = value || {};
  const { desktop = {}, mobile = {} } = element;

  element.desktop = initWithDefaults(element.desktop, {
    hidden: false,
    background: settings.background(desktop.background, { type: 'transparent', crop: null }),
    shadow: settings.shadow(desktop.shadow),
    border: settings.border(desktop.border),
    padding: settings.padding(desktop.padding, { top: 10, left: 10, bottom: 10, right: 10 }),
    margin: settings.margin(desktop.margin),
    minHeight: null,
    verticalAlign: 'flex-start',
    contentAlignment: 'center',
    zIndex: null,
  });

  element.mobile = initWithDefaults(element.mobile, {
    hidden: false,
    background: settings.background(mobile.background, { type: null, crop: null }),
    padding: settings.nullPadding(mobile.padding),
    margin: settings.nullMargin(mobile.margin),
    minHeight: 40,
    verticalAlign: null,
    alignContent: null,
    reverseOrder: false,
    border: settings.border(mobile.border, {
      selectedBorder: null,
      width: null,
      radius: [null, null, null, null],
      radiusChained: true,
    }),
    shadow: settings.shadow(mobile.shadow, { type: null }),
  });

  element.data = initWithDefaults(element.data, {
    customClasses: '',
  });

  return element;
};
