#!/bin/bash

setup_vars() {
  # generate env name for dynamic stage
  if [ -z "$ENVIRONMENT_NAME" ]; then
    BRANCH_NAME=$(echo "$CI_COMMIT_REF_NAME" | tr '[:upper:]' '[:lower:]')
    ISSUE_NUMBER=$(echo "$BRANCH_NAME" | cut -d '-' -f 1,2)
    BRANCH_NAME=$(echo "$BRANCH_NAME" | cut -d '-' -f 3-)
    BRANCH_NAME_HASH=$(echo $BRANCH_NAME | shasum | head -c 5)
    ENVIRONMENT_NAME="$ISSUE_NUMBER-$BRANCH_NAME_HASH"
    MYSQL_DB_NAME=$(echo "$ENVIRONMENT_NAME" | tr '-' '_')
    ci_commit_ref_name=$(echo "$CI_COMMIT_REF_NAME" | tr '[:upper:]' '[:lower:]')
  fi
}

get_service_name() {
  SERVICE=$1
  echo "$SERVICE" | tr '[:upper:]' '[:lower:]' | tr '_' '-'
}

copy_env() {
  for SERVICE in $SERVICES
  do
    service=$(get_service_name "$SERVICE")
    cp -a ./server-config/${ENV_PATH}/om-${service}.env ./optimonk-config/stagings/${ENVIRONMENT_NAME}/om-${service}.env
  done
}

set_kustomize() {
  kustomize edit set namespace "staging-${ENVIRONMENT_NAME}"

  for SERVICE in $SERVICES
  do
    service=$(get_service_name "$SERVICE")
    if [ "$SERVICE" == "SALES" ]; then
      kustomize edit set image salesadmin-image="*:latest"
    else
      kustomize edit set image om-${service}="*:${CI_COMMIT_SHA}"
    fi
    hostname="${SERVICE}_HOST_NAME"
    eval "${hostname}"="${ENVIRONMENT_NAME}-om-${service}"
    if [ "$ENVIRONMENT_NAME" != "default" ] && [ "$SERVICE" == "ADMIN" ]; then
      eval "${hostname}"=$ci_commit_ref_name
    fi
    yq eval -i ".spec.hostnames[0]=\"${!hostname}.staging.optimonk.com\"" httproute-patch-${service}.yaml

    i=0
    max=9
    while [ $i -lt $max ]
    do
      yq eval -i ".configMapGenerator[$i].literals[0] = \"IMAGE_TAG=${CI_COMMIT_SHA}\"" kustomization.yaml
      i=$(($i + 1))
    done
  done

  yq eval -i ".spec.hostnames[0]=\"${ENVIRONMENT_NAME}-webmail.staging.optimonk.com\"" httproute-patch-mailhog.yaml
}

set_dynamic_env_variables() {
  if [ "$ENVIRONMENT_NAME" != "default" ]; then

    MONGO_DB_URI="mongodb+srv://${MONGO_ATLAS_STAGING_USER}:${MONGO_ATLAS_STAGING_PWD}@optimonk-staging-pri.6egz7.mongodb.net/${ENVIRONMENT_NAME}?retryWrites=true&w=majority"
    DDS_DB_URI="mongodb+srv://${MONGO_ATLAS_STAGING_USER}:${MONGO_ATLAS_STAGING_PWD}@optimonk-staging-pri.6egz7.mongodb.net/${ENVIRONMENT_NAME}-dds?retryWrites=true&w=majority"

    # frontend
    yq eval -i ".configMapGenerator[0].literals[1] = \"MONGO_ADDRESS=${MONGO_DB_URI}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[0].literals[2] = \"SSR_URL=https://${ENVIRONMENT_NAME}-om-renderer.staging.optimonk.com/ssr\"" kustomization.yaml
    yq eval -i ".configMapGenerator[0].literals[3] = \"DOMAIN_FRONTEND=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[0].literals[4] = \"DOMAIN_BACKEND=https://${ENVIRONMENT_NAME}-om-backend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[0].literals[5] = \"ASSETS_PATH=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com/\"" kustomization.yaml
    yq eval -i ".configMapGenerator[0].literals[6] = \"BACKOFFICE_DOMAIN=https://${ENVIRONMENT_NAME}-om-backoffice.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[0].literals[7] = \"EMBEDDED_CDN_CONTENT_URL=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com/public\"" kustomization.yaml
    yq eval -i ".configMapGenerator[0].literals[8] = \"EMBEDDED_CDN_DOMAIN=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com/public\"" kustomization.yaml
    # backend
    yq eval -i ".configMapGenerator[1].literals[1] = \"mongo_url=${MONGO_DB_URI}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[2] = \"dds_url=${DDS_DB_URI}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[3] = \"om_new_admin_url=https://${ci_commit_ref_name}.staging.optimonk.com/api\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[4] = \"MYSQL_DATABASE=${MYSQL_DB_NAME}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[5] = \"sales_domain_en=${ENVIRONMENT_NAME}-om-sales.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[6] = \"sales_domain_hu=${ENVIRONMENT_NAME}-om-sales.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[7] = \"om_frontend_url=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[8] = \"om_backend_url=https://${ENVIRONMENT_NAME}-om-backend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[9] = \"om_optimage_url=https://${ENVIRONMENT_NAME}-om-optimage.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[1].literals[10] = \"chrome_url=https://${ENVIRONMENT_NAME}-om-fakeclient.staging.optimonk.com\"" kustomization.yaml
    # backoffice
    yq eval -i ".configMapGenerator[2].literals[1] = \"MONGODB_URI=${MONGO_DB_URI}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[2].literals[2] = \"DDS_URI=${DDS_DB_URI}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[2].literals[3] = \"OM_API_URI=https://${ENVIRONMENT_NAME}-om-backoffice.staging.optimonk.com/api\"" kustomization.yaml
    yq eval -i ".configMapGenerator[2].literals[4] = \"BACKEND_PUBLIC_URL=https://${ENVIRONMENT_NAME}-om-backend.staging.optimonk.com/api\"" kustomization.yaml
    yq eval -i ".configMapGenerator[2].literals[5] = \"MYSQL_DATABASE=${MYSQL_DB_NAME}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[2].literals[6] = \"FRONTEND_URL=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[2].literals[7] = \"ADMIN_URL=https://${ci_commit_ref_name}.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[2].literals[8] = \"SALES_URL=https://${ENVIRONMENT_NAME}-om-sales.staging.optimonk.com\"" kustomization.yaml
    # admin
    yq eval -i ".configMapGenerator[3].literals[1] = \"VUE_APP_API=https://${ENVIRONMENT_NAME}-om-backend.staging.optimonk.com/api\"" kustomization.yaml
    yq eval -i ".configMapGenerator[3].literals[2] = \"VUE_APP_GRAPHQL_API=https://${ENVIRONMENT_NAME}-om-backend.staging.optimonk.com/graphql\"" kustomization.yaml
    yq eval -i ".configMapGenerator[3].literals[3] = \"VUE_APP_SALES_DOMAIN=https://${ENVIRONMENT_NAME}-om-sales.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[3].literals[4] = \"VUE_APP_API_DOCUMENTATION_URL=https://${ENVIRONMENT_NAME}-om-public-api.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[3].literals[5] = \"VUE_APP_SSR_CDN_URL=https://${ENVIRONMENT_NAME}-om-renderer.staging.optimonk.com/ssr\"" kustomization.yaml
    yq eval -i ".configMapGenerator[3].literals[6] = \"VUE_APP_FRONTEND_ONSITE_URL=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[3].literals[7] = \"VUE_APP_SSR_URL=https://${ENVIRONMENT_NAME}-om-renderer.staging.optimonk.com/ssr\"" kustomization.yaml
    yq eval -i ".configMapGenerator[3].literals[8] = \"VUE_APP_FRONTEND=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com\"" kustomization.yaml
    # public-api
    yq eval -i ".configMapGenerator[4].literals[1] = \"MONGODB_CONNECTION_STRING=${MONGO_DB_URI}\"" kustomization.yaml
    # fakeclient
    yq eval -i ".configMapGenerator[5].literals[1] = \"CLIENT_URL=https://${ci_commit_ref_name}.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[5].literals[2] = \"FRONT_URL=https://${ENVIRONMENT_NAME}-om-frontend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[5].literals[3] = \"ENVIRONMENT=${ENVIRONMENT_NAME}\"" kustomization.yaml
    # renderer
    yq eval -i ".configMapGenerator[6].literals[1] = \"mongo_url=${MONGO_DB_URI}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[6].literals[2] = \"environment=${ENVIRONMENT_NAME}\"" kustomization.yaml
    # optimage
    yq eval -i ".configMapGenerator[7].literals[1] = \"environment=${ENVIRONMENT_NAME}\"" kustomization.yaml
    # salesadmin
    yq eval -i ".configMapGenerator[8].literals[1] = \"DB_NAME=${MYSQL_DB_NAME}\"" kustomization.yaml
    yq eval -i ".configMapGenerator[8].literals[2] = \"GCP_HOST_NAME=${ENVIRONMENT_NAME}-om-sales.staging.optimonk\"" kustomization.yaml
    yq eval -i ".configMapGenerator[8].literals[3] = \"BACKEND_DOMAIN=${ENVIRONMENT_NAME}-om-backend.staging.optimonk.com\"" kustomization.yaml
    yq eval -i ".configMapGenerator[8].literals[4] = \"FRONTEND_DOMAIN=https://${ci_commit_ref_name}.staging.optimonk.com\"" kustomization.yaml
  fi
}

setup_git() {
  git config --global user.email "<EMAIL>"
  git config --global user.name "GitLab CI/CD"
}

push_deploy_config() {
  setup_git
  git add stagings/
  git commit --allow-empty -m "Deploy to staging env ${ENVIRONMENT_NAME} from Gitlab CI pipeline, JobName: ${CI_JOB_NAME} JobId: ${CI_JOB_ID}"
  set +e
  git push origin master
  if [ $? -ne 0 ]; then
    git pull --rebase
    git push origin master
  fi
}

pull_repos() {
  git clone https://${GITLAB_DEPLOY_USER}:${GITLAB_DEPLOY_TOKEN}@gitlab.com/optimonk/optimonk-config.git
  git clone https://${GITLAB_DEPLOY_USER}:${GITLAB_DEPLOY_TOKEN}@gitlab.com/optimonk/server-config.git
}

poll_new_version() {
  echo "Polling application"

  APPLICATION_UP_AND_RUNNING=false
  LOOPS=0
  while [ $LOOPS -le 30 ] && [ $APPLICATION_UP_AND_RUNNING == false ]
  do

    for SERVICE in $SERVICES
    do
      service=$(get_service_name "$SERVICE")
      response="${SERVICE}_RESPONSE"
      if [ $service == "public-api" ]; then
        eval "${response}"='$(curl -w " %{http_code}" -s http://${ENVIRONMENT_NAME}-om-${service}.staging.optimonk.com/v1/health)'
      elif [ $service == "optimage" ]; then
        eval "${response}"='$(curl -w " %{http_code}" -s http://${ENVIRONMENT_NAME}-om-${service}.staging.optimonk.com/healthz)'
      elif [ $service == "backoffice" ]; then
        eval "${response}"='$(curl -w " %{http_code}" -s http://${ENVIRONMENT_NAME}-om-${service}.staging.optimonk.com/api/health)'
      else
        if [ "$ENVIRONMENT_NAME" != "default" ] && [ $service == "admin" ]; then
          eval "${response}"='$(curl -w " %{http_code}" -s http://${ci_commit_ref_name}.staging.optimonk.com/health)'
        elif [ $service != "sales" ]; then
          eval "${response}"='$(curl -w " %{http_code}" -s http://${ENVIRONMENT_NAME}-om-${service}.staging.optimonk.com/health)'
        fi
      fi

      status="${SERVICE}_STATUS"
      image_tag="${SERVICE}_IMAGE_TAG"
      if [ $service == "admin" ]; then
        eval "${status}"=$(echo "${!response}" | cut -d ' ' -f 3) 2> /dev/null
        eval "${image_tag}"=$(echo "${!response}" | cut -d ' ' -f 1,2 | jq -r '.imageTag' 2> /dev/null) 2> /dev/null
      elif [ $service != "sales" ]; then
        eval "${status}"=$(echo "${!response}" | cut -d ' ' -f 2) 2> /dev/null
        eval "${image_tag}"=$(echo "${!response}" | cut -d ' ' -f 1 | jq -r '.imageTag' 2> /dev/null) 2> /dev/null
      fi

      if [ $service != "sales" ]; then
        echo ${status} ${!status}
        echo ${image_tag} ${!image_tag}
      fi
    done

      if  [[ $BACKEND_STATUS != 200 ]] || [[ $BACKEND_IMAGE_TAG != "${CI_COMMIT_SHA}" ]] ||
          [[ $PUBLIC_API_STATUS != 200 ]] || [[ $PUBLIC_API_IMAGE_TAG != "${CI_COMMIT_SHA}" ]] ||
          [[ $ADMIN_STATUS != 200 ]] || [[ $ADMIN_IMAGE_TAG != "${CI_COMMIT_SHA}" ]] ||
          [[ $OPTIMAGE_STATUS != 200 ]] || [[ $OPTIMAGE_IMAGE_TAG != "${CI_COMMIT_SHA}" ]] ||
          [[ $RENDERER_STATUS != 200 ]] || [[ $RENDERER_IMAGE_TAG != "${CI_COMMIT_SHA}" ]] ||
          [[ $BACKOFFICE_STATUS != 200 ]] || [[ $BACKOFFICE_IMAGE_TAG != "${CI_COMMIT_SHA}" ]] ||
          [[ $FRONTEND_STATUS != 200 ]] || [[ $FRONTEND_IMAGE_TAG != "${CI_COMMIT_SHA}" ]] ||
          [[ $FAKECLIENT_STATUS != 200 ]] || [[ $FAKECLIENT_IMAGE_TAG != "${CI_COMMIT_SHA}" ]];
      then
          echo "Application not deployed yet"
          sleep 30
      else
          APPLICATION_UP_AND_RUNNING=true
      fi
      LOOPS=$(($LOOPS + 1))
  done

  if [ $APPLICATION_UP_AND_RUNNING == false ]; then
      echo Deployment timeouted after 15 minutes
  fi
}

print_slack_summary() {

  local slack_msg_header
  local slack_msg_body

  slack_msg_header=":x: Deploy to ${ADMIN_URL} failed"
  if [[ "${APPLICATION_UP_AND_RUNNING}" == true ]]; then
      slack_msg_header=":white_check_mark: Deploy to ${ADMIN_URL} succeeded"
  fi

  slack_msg_body="Deployment of branch <https://gitlab.com/${CI_PROJECT_PATH}/-/tree/${CI_COMMIT_REF_NAME}> with job <https://gitlab.com/${CI_PROJECT_PATH}/-/jobs/${CI_JOB_ID}> \n "

  if [[ "${APPLICATION_UP_AND_RUNNING}" == true ]]; then
      slack_msg_body="${slack_msg_body}\n \
\n \
Admin URL: ${ADMIN_URL}\n \
Backoffice URL: ${BACKOFFICE_URL}\n \
Mailhog URL ${MAILHOG_URL}
Frontend URL: ${FRONTEND_URL}\n \
Backend URL: ${BACKEND_URL}\n \
Sales Admin URL: ${SALES_URL}\n \
\n \
      "
  fi

  cat <<-SLACK
  {
      "channel": "${SLACK_NOTIFICATION_CHANNEL}",
      "blocks": [
      {
          "type": "section",
          "text": {
                  "type": "mrkdwn",
                  "text": "${slack_msg_header}"
          }
      },
      {
          "type": "divider"
      },
      {
          "type": "section",
          "text": {
          "type": "mrkdwn",
          "text": "${slack_msg_body}"
          }
      }
      ]
  }
SLACK
}

send_slack_notification() {
  for SERVICE in $SERVICES
  do
    service=$(get_service_name "$SERVICE")
    url="${SERVICE}_URL"
    hostname="${SERVICE}_HOST_NAME"
    eval "${hostname}"="${ENVIRONMENT_NAME}-om-${service}"
    eval "${url}"="https://${!hostname}.staging.optimonk.com"
    if [ "$ENVIRONMENT_NAME" != "default" ]; then
      ADMIN_URL=https://$CI_COMMIT_REF_NAME.staging.optimonk.com
    fi
  done

  MAILHOG_URL="https://${ENVIRONMENT_NAME}-webmail.staging.optimonk.com"

  curl -X POST -s -o /dev/null \
  --data-urlencode "payload=$(print_slack_summary)"  \
  "${SLACK_WEBHOOK}"
}

log_urls() {
  echo -e "\nApplication is available at: $ADMIN_HOST_NAME.staging.optimonk.com\n\n"
  echo -e "Service URLs:\n"

for SERVICE in $SERVICES
do
  service=$(get_service_name "$SERVICE")
  service_uppercase="$(tr '[:lower:]' '[:upper:]' <<< ${service:0:1})${service:1}"
  url="${SERVICE}_URL"
  echo "${service_uppercase}: ${!url}"
done
}

comment_on_merge_requests() {
  echo -e "\nCommenting on open merge requests..."
  IFS=","
  for merge_request in $CI_OPEN_MERGE_REQUESTS
  do
    echo $merge_request
    merge_request_number=$(echo $merge_request | cut -d '!' -f 2)
    curl -X POST -H 'Content-Type: application/json' -H "PRIVATE-TOKEN: $GITLAB_DEPLOY_TOKEN"  https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests/$merge_request_number/notes -d \
  "{\"body\":\"Deployed to staging environment:\n\n \
  [Admin URL](https://$ADMIN_HOST_NAME.staging.optimonk.com)\n\n \
  [Frontend Service URL](https://$FRONTEND_HOST_NAME.staging.optimonk.com)\n\n \
  [Backend Service URL](https://$BACKEND_HOST_NAME.staging.optimonk.com)\n\n \
  [Backoffice Service URL](https://$BACKOFFICE_HOST_NAME.staging.optimonk.com)\n\n \
  [Fakeclient Service URL](https://$FAKECLIENT_HOST_NAME.staging.optimonk.com)\n\n \
  [Renderer Service URL](https://$RENDERER_HOST_NAME.staging.optimonk.com)\n\n \
  [Sales Admin URL](https://$SALES_HOST_NAME.staging.optimonk.com)\n\n \
  [Public API URL](https://$PUBLIC_API_URL.staging.optimonk.com)\n\n \
  [Optimage Service URL](https://$OPTIMAGE_HOST_NAME.staging.optimonk.com)\n\n \"}"
  done
}

check_env_count() {
  ENV_COUNT=$(ls -l ./optimonk-config/stagings | grep -v ${ENVIRONMENT_NAME} | grep -c ^d)

  if [ $ENV_COUNT -gt 9 ]; then
    local slack_msg_header
    local slack_msg_body

    slack_msg_header=":alert: Deployment of branch <https://${CI_PROJECT_PATH}/-/tree/${CI_COMMIT_REF_NAME}> failed"
    slack_msg_body="The maximum number(10) of simultaneous staging enviroments has been reached.\n
  If you want to deploy a new environment, please delete an older one first."

    SLACK_MESSAGE=$(cat <<-SLACK
    {
      "channel": "${SLACK_NOTIFICATION_CHANNEL}",
      "blocks": [
      {
          "type": "section",
          "text": {
                  "type": "mrkdwn",
                  "text": "${slack_msg_header}"
          }
      },
      {
          "type": "divider"
      },
      {
          "type": "section",
          "text": {
          "type": "mrkdwn",
          "text": "${slack_msg_body}"
          }
      }
      ]
    }
SLACK
)
    curl -X POST -s -o /dev/null \
    --data-urlencode "payload=$SLACK_MESSAGE"  \
    "${SLACK_WEBHOOK}"


    echo "The maximum number(10) of simultaneous staging enviroments has been reached"
    exit 1
  fi
}
