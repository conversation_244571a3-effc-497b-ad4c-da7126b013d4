#!/bin/sh

send_deploy_message() {
    if [ "$1" = "success" ]; then
        DEPLOY_HEADER_TEXT="Deploy finished ✅"
    else
        DEPLOY_HEADER_TEXT="Deploy failed ❌"
    fi

    FORMATED_COMMIT_TIMESTAMP=$(date -D "${CI_COMMIT_TIMESTAMP}" +"%B %d, %Y at %I:%M %p")

    SLACK_MESSAGE=$(cat <<-SLACK
{
  "text": "${DEPLOY_HEADER_TEXT}",
	"blocks": [
		{
			"type": "header",
			"text": {
				"type": "plain_text",
				"text": "${DEPLOY_HEADER_TEXT}",
				"emoji": true
			}
		},
		{
			"type": "section",
			"fields": [
				{
					"type": "mrkdwn",
					"text": "*Pipeline*:\n<${CI_PIPELINE_URL}|${CI_PIPELINE_ID}>"
				},
				{
					"type": "mrkdwn",
					"text": "*Job*:\n<${CI_JOB_URL}|${CI_JOB_NAME}>"
				}
			]
		},
		{
			"type": "divider"
		},
		{
			"type": "section",
			"text": {
				"type": "mrkdwn",
				"text": "*${CI_COMMIT_TITLE}*"
			}
		},
		{
			"type": "context",
			"elements": [
				{
					"type": "mrkdwn",
					"text": "Commit <${CI_PROJECT_URL}/-/commit/${CI_COMMIT_SHA}|*${CI_COMMIT_SHORT_SHA}*> by *${CI_COMMIT_AUTHOR}* at *${FORMATED_COMMIT_TIMESTAMP}*"
				}
			]
		}
	]
}
SLACK
)

  curl -X POST -s -o /dev/null \
        --data "$SLACK_MESSAGE"  \
        "${PRODUCT_RELEASES_HOOK}"
}

send_deploy_message $1
