import TYPES from './helper/types';
import { DOM } from './helper/DOM';
import { OUTLINE_SHOW_CLASS } from '../../constants';
import BaseAction from './BaseAction';
import { generateRandomString } from '../../helper';

export default class Appearance extends BaseAction {
  static createChange(selector) {
    return {
      id: generateRandomString(),
      selector,
      type: TYPES.APPEARANCE,
    };
  }

  static toggleVisibility({ element, selector }) {
    if (DOM.hasOpacity(element)) {
      DOM.removeOpacity(element);
      DOM.removeClass(element, OUTLINE_SHOW_CLASS);
      return null;
    }

    DOM.addOpacity(element);
    return Appearance.createChange(selector);
  }

  static applyChange({ change }) {
    const element = DOM.getElement(change.selector);
    if (!element) return change;

    DOM.hideElement(element);
    this.setUniqueAttributes(element, change);

    return change;
  }

  static deleteChange(change) {
    const element = document.querySelector(change.selector);
    if (!element) return;

    DOM.showElement(element);
    DOM.removeOpacity(element);
    this.resetElement(element);
  }

  static hideElements() {
    const elementsToHide = DOM.getHiddenElements();
    elementsToHide.forEach((element) => {
      DOM.hideElement(element);
      DOM.removeOpacity(element);
    });
  }

  static toggleChanges({ change, element = null }) {
    const domElement = document.querySelector(change?.selector) || element;
    if (!domElement) return;

    if (!DOM.isElementVisible(domElement)) {
      DOM.showElement(domElement);
      DOM.addOpacity(domElement);
      DOM.addClass(domElement, OUTLINE_SHOW_CLASS);
    } else {
      DOM.hideElement(domElement);
      DOM.removeOpacity(domElement);
      DOM.removeClass(domElement, OUTLINE_SHOW_CLASS);
    }
  }

  static getContent(change) {
    const element = this.getElement(change);
    if (!element) return { content: 'changesModal.missingElement', error: true };

    return { content: element.outerHTML.replace('style="display:none !important', '') };
  }
}
