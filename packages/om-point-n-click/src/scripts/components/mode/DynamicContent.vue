<template>
  <div class="dynamic-content">
    <transition name="slide">
      <template v-if="smartAbTestPreviewMode">
        <SmartAbTestPreview :parameters="smartAbTestParameters" @close="closeSmartAbTestPreview" />
      </template>
      <template v-else>
        <TopBar
          :disabled="disabled"
          :displayChanges="displayChanges"
          :currentlyEditing="currentlyEditing"
        >
          <template #back-area>
            <Back
              :class="{ 'om-disabled': disabled }"
              class="om-top-bar-back-button"
              data-track-property="top-bar:back-clicked"
              href="#"
              @click="back"
            />
            <div class="om-separator"></div>
            <Breadcrumb
              v-if="!isSmartAbTestMode"
              :paths="getPaths"
              :disabled="isBreadcrumbDisabled || isInsertMenuActive"
              @path-clicked="handleBreadcrumbPathClicked"
            />
          </template>
          <template #center-right-side>
            <div
              data-track-property="top-bar:display-changes-modal"
              class="om-button changes om-small"
              :class="{
                'om-disabled': !hasChanges || disabled,
              }"
              @click="showChanges"
            >
              {{ $t('changes', { noOfChanges: changesCount }) }}
              <div v-if="!allElementsAvailable" class="changes-warning">
                <div class="om-tooltip">
                  <Error />
                  <span class="om-tooltip-text" v-html="$t('tooltips.missingDynamicContent')">
                  </span>
                </div>
              </div>
            </div>
            <Switch
              v-if="hasChanges"
              :modelValue="displayChanges"
              trackIdPrefix="top-bar:display-changes-toggle"
              :text="$t('highlightChanges')"
              :tooltipText="$t('tooltips.changeToggle')"
              :disabled="disabled"
              @update:modelValue="displayChanges = !displayChanges"
          /></template>
          <template #history>
            <History :disabled="disabled" @undo="handleUndoClick" @redo="handleRedoClick" />
          </template>
          <template #action>
            <Marker v-if="!isNew" />
            <Switch
              v-if="isSuperAdmin && !isSmartAbTestMode"
              :modelValue="advancedSelect"
              trackIdPrefix="top-bar:advanced-select"
              :text="$t('advancedSelect')"
              @update:modelValue="advancedSelect = !advancedSelect"
            />
            <div
              class="om-tooltip om-tooltip-click-mode"
              :class="{ 'om-tooltip-visible': previewBtnClicked }"
            >
              <a
                data-track-property="top-bar:share-clicked"
                class="om-preview om-button om-secondary om-small om-m-0"
                :class="{
                  'om-disabled': !hasChanges,
                }"
                @click="previewBtnClicked = !previewBtnClicked"
              >
                {{ $t('preview') }}
              </a>
              <span
                v-if="previewBtnClicked"
                class="om-tooltip-text om-preview-tooltip"
                :class="{ 'om-tooltip-wider': $i18n.locale === 'hu' }"
              >
                <a
                  data-track-property="share:copy-clicked"
                  class="om-button om-secondary om-small"
                  @click="copyUrlToClipboard"
                >
                  {{ $t('copyURL') }}
                </a>
                <a
                  data-track-property="share:preview-clicked"
                  class="om-button om-secondary om-small"
                  @click="preview"
                >
                  {{ $t('openURL') }}
                </a>
              </span>
            </div>
            <a
              v-if="!isNew"
              class="om-button om-primary om-small om-m-0 om-save-button"
              :class="{ 'om-disabled': !hasChanges || isContextMenuActive }"
              :style="{ 'margin-left': isNew ? '12px' : '' }"
              data-track-property="top-bar:save-clicked"
              :disabled="!hasChanges"
              @click="saveCampaignOnly"
            >
              {{ $t('save') }}
            </a>
            <a
              :class="{
                'om-disabled': !hasChanges || isContextMenuActive,
              }"
              data-track-property="top-bar:save-and-exit-clicked"
              class="om-button om-primary om-small om-m-0"
              :disabled="!hasChanges"
              @click="saveAndExit"
            >
              {{ isNew ? $t('next') : $t('saveAndExit') }}
            </a>
          </template>
        </TopBar>
      </template>
    </transition>
    <transition name="fade">
      <ChangesModal
        @edit="changesModalEdit"
        @highlight="changesModalHighlight"
        @delete="changesModalDelete"
        @reposition="handleReposition"
        @preview="smartAbTestPreview"
      />
    </transition>
    <UnsavedChangesModal />
    <TooManyChangesModal />
    <DCProductTourModal />
    <transition name="slide-fade">
      <ContextMenu
        v-if="isContextMenuActive"
        :element="contextMenu.lastElement"
        :elementTextModified="elementTextModified"
        @context-menu-canceled="handleContextMenuClosed"
        @context-menu-done="handleContextMenuClosed"
        @context-menu-edit-text="handleContextMenuEdit"
        @context-menu-edit-HTML="handleContextMenuHTML"
        @context-menu-editing-canceled="handleEditingCanceled"
        @context-menu-editing-finished="handleEditingFinished"
        @context-menu-toggle-element="handleToggleElement"
        @context-menu-insert-text="handleInsertText"
        @context-menu-insert-html="handleInsertHTML"
        @context-menu-smart-ab-test="handleSmartABTest"
        @context-menu-smart-product-tag="handleSmartProductTagClicked"
        @context-menu-smart-personalization="handleSmartPersonalization"
        @context-menu-edit-style="handleEditStyle"
      />
    </transition>
    <transition name="slide-fade">
      <InsertMenu
        v-if="isInsertMenuActive"
        :change="insertMenu.change"
        :editMode="insertMenu.editMode"
        :paths="getPaths"
        @insert-menu-done="handleInsertMenuDone"
        @insert-menu-cancel="handleInsertMenuCancel"
        @insert-menu-element-change="setSelectedElement"
        @reposition="handleReposition"
      />
    </transition>
    <HTMLEditor
      @edit-HTML-action-cancel="handleHTMLModalCancel"
      @html-editor-done="handleHTMLDone"
    />
    <SmartABTestModal
      @smart-ab-test-close="handleSmartABTestClose"
      @smart-ab-test-preview="smartAbTestPreview"
    />
  </div>
  <transition name="fade">
    <SmartProductTagModal
      @smart-product-tag-modal-action="handleSmartProductTagModalAction"
      @smart-product-tag-modal-close="handleSmartProductTagModalClose"
    />
  </transition>
  <transition name="fade">
    <SmartPersonalizationModal />
  </transition>
</template>
<script>
  import { mapState, mapMutations, mapGetters, createNamespacedHelpers } from 'vuex';
  import { createApp } from 'vue';
  import unique from 'unique-selector';
  import { emitter } from '../../services/shared';
  import FloatingActionBar from '../FloatingActionBar.vue';
  import Breadcrumb from '../Breadcrumb/Breadcrumb.vue';
  import ChangesModal from '../Modals/Changes/Changes.vue';
  import DCProductTourModal from '../Modals/DCProductTour.vue';
  import UnsavedChangesModal from '../Modals/UnsavedChanges.vue';
  import TooManyChangesModal from '../Modals/TooManyChanges.vue';
  import HTMLEditor from '../Modals/HTMLEditor.vue';
  import SmartABTestModal from '../SmartABTest/SmartABTestModal.vue';
  import TopBar from '../TopBar.vue';
  import Back from '../svg/Back.vue';
  import ContextMenu from '../ContextMenu/index.vue';
  import InsertMenu from '../ContextMenu/insert.vue';
  import Error from '../svg/Error.vue';
  import Marker from '../Marker.vue';
  import Switch from '../Switch.vue';
  import apiService from '../../services/api';
  import OverlayService from '../../services/overlay';
  import {
    OUTLINE_HOVER_CLASS,
    OUTLINE_SHOW_CLASS,
    OM_CHANGES_KEY,
    ESC_KEYCODE,
    FLOATING_ACTION_BAR_WRAPPER_CLASS,
    FLOATING_ACTION_BAR_CONTENT_CLASS,
  } from '../../constants';
  import {
    findParentDiv,
    isEventAboveIgnoreLine,
    backToAdmin,
    hasNotExistingElement,
    getFullPath,
    scrollToElement,
    isEditableElement,
    isOnlyTextDiv,
    getEditableElement,
  } from '../../helper';
  import ActionService from '../../services/actions/ActionService';
  import Appearance from '../../services/actions/Appearance';
  import { DOM } from '../../services/actions/helper/DOM';
  import storage from '../../storage';
  import History from '../History.vue';
  import SmartProductTagModal from '../SmartProductTag/SmartProductTagModal.vue';
  import SmartPersonalizationModal from '../SmartPersonalization/SmartPersonalizationModal.vue';
  import SmartAbTestPreview from '../Preview/SmartAbTestPreview.vue';
  import EditText from '../../services/actions/EditText';
  import notifyMixin from '../../mixins/notify';
  import TYPES from '../../services/actions/helper/types';
  import InsertText from '../../services/actions/InsertText';
  import SmartProductTag from '../../services/actions/SmartProductTag';
  import EditStyle from '../../services/actions/EditStyle';

  const MAX_ACCEPTABLE_CHANGES = 20;
  const SELECTABLE_TAG_NAMES = ['H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'P', 'SPAN', 'A', 'DIV'];
  const {
    mapState: mapDCState,
    mapGetters: mapDCGetters,
    mapMutations: mapDCMutations,
    mapActions: mapDCActions,
  } = createNamespacedHelpers('dynamicContent');

  const { mapGetters: mapRepositionGetters, mapMutations: mapRepositionMutations } =
    createNamespacedHelpers('reposition');

  const { mapState: mapHistoryState, mapMutations: mapHistoryMutations } =
    createNamespacedHelpers('history');

  const { mapActions: mapSPPOActions, mapGetters: mapSPPOGetters } =
    createNamespacedHelpers('sppo');

  const { mapActions: mapSPActions } = createNamespacedHelpers('sp');

  export default {
    components: {
      TopBar,
      Error,
      Marker,
      Switch,
      Back,
      ChangesModal,
      DCProductTourModal,
      UnsavedChangesModal,
      TooManyChangesModal,
      ContextMenu,
      Breadcrumb,
      HTMLEditor,
      InsertMenu,
      History,
      SmartABTestModal,
      SmartAbTestPreview,
      SmartProductTagModal,
      SmartPersonalizationModal,
    },
    mixins: [notifyMixin],
    props: {
      initializedServices: {
        type: Object,
      },
    },
    data: () => ({
      selectedHtmlElement: null,
      selectedPage: '',
      currentlyEditing: false,
      originalText: '',
      originalHTML: '',
      displayChanges: false,
      displayChangesMemory: false,
      advancedSelect: false,
      contextMenu: {
        active: false,
        lastElement: null,
      },
      insertMenu: {
        active: false,
        change: null,
        editMode: false,
      },
      elementTextModified: false,
      paths: [],
      smartAbTestPreviewMode: false,
      smartAbTestParameters: null,
      previewBtnClicked: false,
      copied: false,
      lastHighlightedChange: null,
      floatingActionBarInstances: {},
    }),
    computed: {
      ...mapState(['isNew', 'selectedMode', 'selector', 'isSuperAdmin', 'campaignData']),
      ...mapDCState(['changes', 'unsavedChangesCount']),
      ...mapGetters(['isSmartAbTestMode']),
      ...mapHistoryState(['unsavedChangesCount']),
      ...mapSPPOGetters(['smartProductTags']),
      ...mapDCGetters([
        'hasChanges',
        'changesCount',
        'hasActiveModal',
        'variantId',
        'changesByType',
      ]),
      ...mapRepositionGetters(['inRepositionState', 'repositionChange']),
      allElementsAvailable() {
        return !this.hasNotExistingElement(this.getSelectors());
      },
      disabled() {
        return this.isContextMenuActive || this.isInsertMenuActive || this.currentlyEditing;
      },
      isContextMenuActive() {
        return this.contextMenu.active;
      },
      isInsertMenuActive() {
        return this.insertMenu.active;
      },
      isBreadcrumbDisabled() {
        return this.modal.$hasActiveModal();
      },
      getPaths() {
        return this.paths.length > 0 ? this.paths : [{ tagname: 'body', selector: 'body' }];
      },
    },
    watch: {
      selectedHtmlElement(newV, oldV) {
        if (oldV && oldV.hasAttribute('contentEditable')) {
          DOM.removeAttribute(oldV, 'contentEditable');
        }

        if (newV) {
          const selector = unique(newV, { excludeRegex: RegExp('om-web-selector') });
          this.setSelector(selector);
          if (this.paths.length === 0) {
            this.paths = getFullPath(newV);
          }
        } else {
          this.setSelector('');
          this.paths = [];
        }
      },
      displayChanges() {
        this.onDisplayChanges();
      },
      initializedServices() {
        this.initializedServices.heapTrack({ location: 'user-page', action: 'editor-opened' });

        const productTourService = this.initializedServices.getProductTourService();
        if (productTourService.mode === 'dynamic_content' && productTourService.getDriver()) {
          this.modal.$show('productTour');
        }
        window.PNCTracker = this.initializedServices.heapTrack;
      },
      changes: {
        handler(value, old) {
          storage.set(OM_CHANGES_KEY, value);

          if (this.displayChanges) {
            this.onDisplayChanges();
          }
        },
        deep: true,
      },
    },
    mounted() {
      window.addEventListener('resize', this.resizeHandler);

      this.emitter.on('display-changes', (state) => {
        this.displayChanges = state;
        this.toggleFloatingActionBars();
      });

      this.emitter.on('unsaved-changes-action', async (action) => {
        const token = apiService.getToken();

        if (action === 'save') {
          await this.saveAndExit();
          return;
        }

        if (action === 'discard') {
          backToAdmin(token, { back: true, isNew: this.isNew });
        }
      });
      this.emitter.on('edit-HTML-action', ({ id, existingChange = null }) => {
        if (existingChange) {
          const index = this.changes.findIndex(
            (change) => change.newHTMLEditSelector === existingChange.newHTMLEditSelector,
          );
          ActionService.applyChange({ change: this.changes[index] });
          return;
        }

        const change = this.changes.find((change) => change.id === id);
        ActionService.applyChange({ change });
      });

      this.emitter.on('delete-change', (change) => {
        this.unmountFloatingActionBarInstanceById(change.id);
        this.deleteChange(change);
        ActionService.removeChangesFromDOM([change]);
        delete this.floatingActionBarInstances[change.id];
      });
    },
    unmounted() {
      document.removeEventListener('resize', this.resizeHandler);
      document.removeEventListener('click', this.singleClickHandler);
      document.removeEventListener('mouseover', this.mouseEnterHandler);
      document.removeEventListener('mouseout', this.mouseLeaveHandler);
      document.removeEventListener('keydown', this.keydownHandler);
      document.removeEventListener('keyup', this.keyupHandler);
    },
    async created() {
      await this.fetchAndSetChanges();
      this.setChanges(ActionService.applyChanges(this.changes, { init: true }));
      await this.fetchPPOData();
      this.subscribeListeners();

      await this.prepareToLoad();

      setTimeout(() => {
        this.displayChanges = true;
      }, 500);
    },
    methods: {
      ...mapDCMutations(['setChanges']),
      ...mapHistoryMutations([
        'flushUndoStack',
        'resetUnsavedChangesCount',
        'incrementUnsavedChangesCount',
      ]),
      ...mapSPPOActions(['fetchPPOData']),
      ...mapMutations(['setSelector', 'setIsNew', 'setSelectedMode']),
      ...mapDCActions([
        'saveCampaign',
        'fetchAndSetChanges',
        'modifyChangeById',
        'addChange',
        'modifyChange',
        'modifyChangeById',
        'replaceChangeById',
        'removeChange',
        'removeChangeById',
        'undo',
        'redo',
      ]),
      ...mapRepositionMutations(['setRepositionState', 'setRepositionChange']),
      ...mapSPActions(['prepareToLoad']),
      hasNotExistingElement,
      handleBreadcrumbPathClicked({ selector, element }) {
        this.setActivePath(selector);
        OverlayService.calculateOverlayPosition(element);
        this.toggleContextMenu({ visibility: false });

        const event = new Event('selected');
        Object.defineProperty(event, 'target', { writable: false, value: element });
        this.singleClickHandler(event);
      },
      changesModalEdit(change) {
        const element = ActionService.getElement(change);
        scrollToElement(element);

        if (change.type === TYPES.APPEARANCE) {
          this.handleHiddenElementClicked(change, element);
        }

        if (change.type === TYPES.TEXT) {
          this.setSelectedElement(element);
          this.setContextMenuElement(this.selectedHtmlElement);
          this.toggleContextMenu({ visibility: true });
        }

        if (change.type === TYPES.SMART_AB_TEST) {
          this.handleSmartABTest({ change });
        }

        if (change.type === TYPES.HTML) {
          this.handleShowHTMLEditorOnClick(change);
        }

        if (change.type === TYPES.INSERT_TEXT || change.type === TYPES.EDIT_STYLE) {
          this.setSelectedElement(element);
          this.toggleInsertMenu({ visibility: true, change, editMode: true });
        }

        if (change.type === TYPES.INSERT_HTML) {
          this.modal.$show('HTMLEditor', { change, exist: true });
        }

        if (change.type === TYPES.SMART_PRODUCT_TAG) {
          this.setSelectedElement(element);
          this.toggleInsertMenu({ visibility: true, change, editMode: true });
        }

        if (change.type === TYPES.SMART_PERSONALIZATION) {
          this.setSelectedElement(element);
          this.modal.$show('smartPersonalization', {
            element: this.selectedHtmlElement,
            change,
          });
        }
      },
      changesModalHighlight(change) {
        this.setLastHighlightedChange(change);
        const element = ActionService.getElement(change);
        scrollToElement(element);

        ActionService.toggleChanges({ changes: [change], isOn: this.displayChanges });
      },
      changesModalDelete(change) {
        this.deleteChange(change);
      },
      handleUndoClick() {
        if (this.displayChanges) this.displayChanges = false;

        setTimeout(() => {
          ActionService.removeChangesFromDOM(this.changes);
          this.undo();
          ActionService.applyChanges(this.changes, { init: true });

          window.PNCTracker({ location: 'top-bar', action: `undo` });
        }, 50);
      },
      handleRedoClick() {
        if (this.displayChanges) this.displayChanges = false;

        setTimeout(() => {
          ActionService.removeChangesFromDOM(this.changes);
          this.redo();
          ActionService.applyChanges(this.changes, { init: true });

          window.PNCTracker({ location: 'top-bar', action: `redo` });
        }, 50);
      },
      handleSmartProductTagModalAction(variableName, productTag) {
        const change = SmartProductTag.createChange(this.selector, productTag, variableName);
        this.addChange(change);
        ActionService.applyChange({ change, type: 'smart-product-tag' });
        OverlayService.hideOverlay();
        const element = DOM.getElementById(change.id);
        this.setSelectedElement(element);
        this.toggleInsertMenu({ visibility: true, change });
      },
      handleSmartProductTagModalClose() {
        this.restoreEditedElementState();
      },
      handleSmartProductTagClicked() {
        this.toggleContextMenu({ visibility: false });
        this.modal.$show('smartProductTag');
      },
      handleSmartPersonalization({ change }) {
        this.modal.$show('smartPersonalization', {
          element: this.selectedHtmlElement,
          change,
        });
      },
      handleEditStyle() {
        this.toggleContextMenu({ visibility: false });
        const textContent = DOM.getElement(this.selector)?.textContent.trim();
        const change = EditStyle.createChange(this.selector, textContent);
        this.addChange(change);
        ActionService.applyChange({ change });
        OverlayService.hideOverlay();
        this.toggleInsertMenu({ visibility: true, change });
      },
      createPreviewUrl() {
        const url = new URL(window.location.href);
        const searchParams = new URLSearchParams();
        searchParams.append('om-preview-v3-id', this.variantId);
        url.search = searchParams;
        return url.href;
      },
      async saveAsDraft() {
        if (this.isNew) {
          await this.saveCampaign();
          this.setIsNew(false);
        }
        await apiService.createPreview(this.changes);
      },
      async preview() {
        await this.saveAsDraft();
        const previewUrl = this.createPreviewUrl();
        window.open(previewUrl, '_blank', 'noopener=true');
      },
      async copyUrlToClipboard() {
        await this.saveAsDraft();
        const previewUrl = this.createPreviewUrl();
        navigator.clipboard.writeText(previewUrl);
      },
      resizeHandler() {
        if (!OverlayService.isOverlayVisible()) return;

        OverlayService.calculateOverlayPosition(this.selectedHtmlElement);
      },
      handleToggleElement() {
        const changes = this.changes;
        const hasExistingChange = changes.find(
          (change) => change.selector === this.selector && change.type === TYPES.APPEARANCE,
        );

        const resultedChange = ActionService.toggleVisibility({
          element: this.selectedHtmlElement,
          selector: this.selector,
        });

        if (hasExistingChange) {
          this.removeChange(hasExistingChange);
        } else if (resultedChange) {
          this.addChange(resultedChange);
        }
      },
      handleInsertText() {
        this.toggleContextMenu({ visibility: false });
        const change = InsertText.createChange(this.selector);
        this.addChange(change);
        ActionService.applyChange({ change });
        OverlayService.hideOverlay();
        const element = DOM.getElementById(change.id);
        this.setSelectedElement(element);
        this.toggleInsertMenu({ visibility: true, change });
        this.incrementUnsavedChangesCount();
      },
      handleInsertHTML() {
        this.modal.$show('HTMLEditor', { type: TYPES.INSERT_HTML });
      },
      handleSmartABTest({ change }) {
        this.toggleContextMenu({ visibility: false });
        this.modal.$show('smartABTest', { change });
      },
      handleInsertMenuDone(change) {
        this.toggleInsertMenu({ visibility: false });
        const element = DOM.getElementById(change.id);
        if (change.type === 'insert-text' && element.innerText === '') {
          // FIXME: amíg üres az insert text nem kellene engedni a Done gombot megnyomni és akkor ez törölhető.
          this.removeChangeById(change.id);
          InsertText.deleteChange(change);
        }
        DOM.removeAttribute(element, 'contentEditable');
        this.restoreEditedElementState();
      },
      handleInsertMenuCancel(change, editMode) {
        this.toggleInsertMenu({ visibility: false });
        this.restoreEditedElementState();
        if (editMode) {
          ActionService.applyChange({ change: this.changes.find(({ id }) => id === change.id) });
        } else {
          this.removeChangeById(change.id);
          ActionService.removeChangesFromDOM([change]);
        }
        DOM.removeAttribute(DOM.getElementById(change.id), 'contentEditable');
      },
      handleSmartABTestClose() {
        this.restoreEditedElementState();
      },
      setSABPreviewMode(value) {
        this.smartAbTestPreviewMode = value;
      },
      setSABPreviewParams(params) {
        this.smartAbTestParameters = params;
      },
      smartAbTestPreview({ versions, selector, tempChange }) {
        this.setSABPreviewMode(true);
        this.setSABPreviewParams({ versions, selector, tempChange });
        this.displayChangesMemory = this.displayChanges;
        this.displayChanges = false;
        ActionService.toggleChanges({ changes: [tempChange], isOn: this.displayChanges });
      },
      closeSmartAbTestPreview(tempChange) {
        this.setSABPreviewMode(false);
        const existingChange = !!this.changes.find(({ id }) => id === tempChange.id);
        if (!existingChange) {
          this.modal.$show('smartABTest', { change: { ...tempChange, temp: true } });
        }
        this.displayChanges = this.displayChangesMemory;
        this.displayChangesMemory = false;
        ActionService.toggleChanges({ changes: [tempChange], isOn: this.displayChanges });
        this.setSABPreviewParams(null);
      },
      async saveCampaignOnly() {
        try {
          const res = await this.saveCampaign();
          if (res.status !== 200 || !res.ok) throw new Error();

          this.notify(this.$t('notifications.campaignSaved'), 'success');
          this.resetUnsavedChangesCount();
          storage.delete(OM_CHANGES_KEY);
          if (this.changesByType(TYPES.SMART_AB_TEST).length) {
            const { changes } = await res.json();
            this.setRunningSmartABTests(changes);
          }
        } catch (error) {
          this.notify(this.$t('notifications.unableToSave'), 'error');
        }
      },
      setActivePath(selector) {
        this.paths.forEach((path) => {
          if (path.selector === selector) {
            path.active = true;
          } else {
            path.active = false;
          }
        });
      },
      getSelectors() {
        return this.changes.map(({ selector }) => selector);
      },
      back(e) {
        e.preventDefault();
        e.stopPropagation();

        if (this.unsavedChangesCount) {
          this.modal.$show('unsavedChanges');
          return;
        }

        const token = apiService.getToken();
        backToAdmin(token, { back: true, isNew: this.isNew });
      },
      deleteChange(change) {
        ActionService.deleteChange(change);

        this.removeChange(change);
        this.flushUndoStack();
      },
      onDisplayChanges() {
        ActionService.toggleChanges({ changes: this.changes, isOn: this.displayChanges });
        this.toggleFloatingActionBars();
      },
      showChanges() {
        if (!this.hasChanges || this.currentlyEditing) return;

        if (!this.isSmartAbTestMode) this.displayChanges = false;
        this.modal.$show('changes');
      },

      restoreEditedElementState() {
        this.currentlyEditing = false;
        this.setSelectedElement(null);

        OverlayService.hideOverlay();
        OverlayService.restoreScroll();
        this.toggleContextMenu({ visibility: false });
        // NOTE: selected element never be null again
      },

      handleEditingCanceled() {
        const changeIndex = this.changes.findIndex(
          ({ selector, type }) => selector === this.selector && type === 'text',
        );

        if (changeIndex > -1) {
          const { replaceTo } = this.changes[changeIndex];
          this.selectedHtmlElement.innerHTML = replaceTo;
        } else {
          this.selectedHtmlElement.innerHTML = this.originalHTML;
          this.originalHTML = '';
        }

        this.restoreEditedElementState();
      },

      handleEditingFinished() {
        this.elementTextModified = false;
        const modifiedValue = this.selectedHtmlElement.innerHTML;
        const changeIndexBySelector = this.getChangeIndexBySelector(this.selector);

        this.restoreEditedElementState();
        this.handleContextMenuClosed();

        if (this.changesCount >= MAX_ACCEPTABLE_CHANGES) {
          this.modal.$show('tooManyChanges');
          return;
        }

        if (modifiedValue === this.originalText) return;
        if (changeIndexBySelector === -1) {
          const change = EditText.createChange(this.selector, modifiedValue, this.originalText);
          this.addChange(change);
        } else {
          this.modifyChange({
            change: this.changes[changeIndexBySelector],
            property: 'replaceTo',
            to: modifiedValue,
          });
        }
      },
      async saveAndExit(event) {
        event?.preventDefault();

        this.resetUnsavedChangesCount();
        storage.delete(OM_CHANGES_KEY);
        await this.saveCampaign();
        const token = apiService.getToken();
        backToAdmin(token);
      },
      subscribeListeners() {
        document.addEventListener('click', this.singleClickHandler, true);
        document.addEventListener('mouseover', this.mouseEnterHandler, true);
        document.addEventListener('mouseout', this.mouseLeaveHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keyup', this.keyupHandler, true);
      },
      getChangeIndexBySelector(selector) {
        return this.changes.findIndex(
          (change) => change.selector === selector && change.type === 'text',
        );
      },
      keydownHandler(e) {
        if (
          this.isElementInsideTopBar(e.target) ||
          !this.isContextMenuActive ||
          e.keyCode === ESC_KEYCODE
        )
          return;

        OverlayService.calculateOverlayPosition(this.selectedHtmlElement);
        this.elementTextModified = true;
      },
      keyupHandler(e) {
        if (this.isElementInsideTopBar(e.target) || !this.isContextMenuActive) return;

        if (e.keyCode === ESC_KEYCODE) {
          if (this.elementTextModified) {
            this.handleEditingCanceled();
            this.elementTextModified = false;
          }
          this.restoreEditedElementState();
          this.initializedServices.heapTrack({ location: 'finish-editing', action: 'cancel' });
          return;
        }

        OverlayService.calculateOverlayPosition(this.selectedHtmlElement);
      },
      hasTextContent(element) {
        return !!element?.textContent?.trim();
      },
      isElementInsideTopBar(target) {
        return target.closest('#om-web-selector') !== null;
      },
      isToastElement(target) {
        return target.closest('.Toastify') !== null;
      },
      checkEventShouldBeIgnored(e) {
        const isProductTourActivated = this.initializedServices
          .getProductTourService()
          .getDriver()?.isActivated;
        const isAboveIgnoreLine = isEventAboveIgnoreLine(e);
        const isNotSelectMode = this.selectedMode !== 'select';
        const isInsideTopBar = this.isElementInsideTopBar(e.target);
        const isContextMenuActive = this.isContextMenuActive;
        const isToast = this.isToastElement(e.target);
        const isFloatingActionBar = e.target.closest(`.${FLOATING_ACTION_BAR_WRAPPER_CLASS}`);

        return (
          isProductTourActivated ||
          isAboveIgnoreLine ||
          isNotSelectMode ||
          isInsideTopBar ||
          isContextMenuActive ||
          isToast ||
          this.smartAbTestPreviewMode ||
          isFloatingActionBar
        );
      },
      setLastHighlightedChange(value) {
        this.lastHighlightedChange = value;
      },
      deselectHighlightedChange() {
        if (!this.lastHighlightedChange) return;

        ActionService.toggleChanges({
          changes: [this.lastHighlightedChange],
          isOn: this.displayChanges,
        });
        this.setLastHighlightedChange(null);
      },
      getSABModeTarget(e, target) {
        const isEditable = isEditableElement(target.tagName) || isOnlyTextDiv(target);
        const targetTextContent = target.textContent.trim();
        const elementFromClickContent = document
          .elementFromPoint(e.clientX, e.clientY)
          .textContent.trim();

        const hasAfterAttribute = window.getComputedStyle(target, '::after').content !== 'none';

        if (isEditable && targetTextContent === elementFromClickContent && !hasAfterAttribute) {
          return target;
        }

        const elementsWithOutTarget = document
          .elementsFromPoint(e.clientX, e.clientY)
          .filter((element) => element !== target);
        const editableElement = elementsWithOutTarget.find(
          (element) => isEditableElement(element.tagName) || isOnlyTextDiv(target),
        );

        return editableElement || null;
      },
      singleClickHandler(e) {
        this.previewBtnClicked = false;
        this.deselectHighlightedChange();

        let target = this.target(e);
        const isClickedOnEditedElement = this.selectedHtmlElement?.contains(target);
        if (isClickedOnEditedElement) {
          e.preventDefault();
        }

        if (this.checkEventShouldBeIgnored(e)) {
          return;
        }

        e.preventDefault();
        if (!this.isSmartAbTestMode) this.displayChanges = false;

        if (target.closest(`.${FLOATING_ACTION_BAR_CONTENT_CLASS}`)) {
          target = target.closest(`.${FLOATING_ACTION_BAR_CONTENT_CLASS}`).parentElement;
        }

        if (this.inRepositionState) {
          this.reposition(e, target);
          return;
        }

        if (this.isSmartAbTestMode) {
          target = this.getSABModeTarget(e, target);
          if (!target) return;
        } else {
          const hasSPPOParent = SmartProductTag.hasProductTagParent(target);
          target = hasSPPOParent || target;
        }

        this.setSelectedElement(target);
        this.setOriginalHTML(target.innerHTML);
        DOM.removeClass(this.selectedHtmlElement, OUTLINE_HOVER_CLASS);

        const existingChange = this.changes.find(
          (change) =>
            change.id === this.selectedHtmlElement.dataset.omDcId ||
            change.id === this.selectedHtmlElement.id,
        );

        if (this.isSmartAbTestMode) {
          this.$nextTick(() => {
            this.modal.$show('smartABTest', { change: existingChange });
          });
          return;
        }

        if (existingChange?.type === TYPES.HTML) {
          this.modal.$show('HTMLEditor', { change: existingChange, exist: true });
        }

        this.setupContextMenu(target, existingChange);

        this.$nextTick(() => {
          if (this.selectedHtmlElement.style.display === 'none') {
            Appearance.toggleChanges({ element: this.selectedHtmlElement });
          }
          if (this.selectedHtmlElement.classList.contains('om-element-hidden')) {
            OverlayService.hideOverlay();
            OverlayService.createOverlay({
              element: this.selectedHtmlElement,
              onClick: this.handleEditingFinished,
            });
          }
        });
      },
      setupContextMenu(target, change) {
        const isInsertType = [
          TYPES.INSERT_TEXT,
          TYPES.INSERT_HTML,
          TYPES.SMART_PRODUCT_TAG,
          TYPES.EDIT_STYLE,
        ].includes(change?.type);

        if (isInsertType) {
          this.toggleInsertMenu({
            visibility: true,
            change,
            editMode: true,
          });
          if (change.type === TYPES.INSERT_HTML) {
            this.modal.$show('HTMLEditor', { change, exist: true });
          }
        } else {
          this.toggleContextMenu({ visibility: true });
        }

        this.setContextMenuElement(target);
      },
      mouseEnterHandler(e) {
        if (this.checkEventShouldBeIgnored(e)) {
          return;
        }

        const parent = findParentDiv(e.target, SELECTABLE_TAG_NAMES);
        const hasEditableTextContent = this.hasTextContent(parent);
        const isHighlighted = parent?.classList.contains(OUTLINE_SHOW_CLASS);
        if (!parent || !hasEditableTextContent || isHighlighted) return;

        DOM.addClass(parent, OUTLINE_HOVER_CLASS);
      },
      mouseLeaveHandler(event) {
        const parent = findParentDiv(event.target, SELECTABLE_TAG_NAMES);
        if (!parent) return;

        if (this.currentlyEditing && parent === this.selectedHtmlElement) return;

        DOM.removeClass(parent, OUTLINE_HOVER_CLASS);
      },
      handleContextMenuClosed(change) {
        Appearance.hideElements();
        this.restoreEditedElementState();
        DOM.removeClass(this.contextMenu.lastElement, OUTLINE_SHOW_CLASS);
        this.flushUndoStack();

        if (change) {
          // after done, force to set attributes to show small type badge on highlight
          ActionService.applyChange({ change });
        }
      },
      setSelectedElement(element) {
        this.selectedHtmlElement = element;
      },
      setOriginalText(text) {
        this.originalText = text;
      },
      setOriginalHTML(html) {
        this.originalHTML = html;
      },
      setContextMenuElement(element) {
        this.contextMenu.lastElement = element;
      },
      toggleContextMenu({ visibility }) {
        this.contextMenu.active = visibility;
      },
      toggleInsertMenu({ visibility, change, editMode = false }) {
        this.insertMenu.active = visibility;
        this.insertMenu.change = change;
        this.insertMenu.editMode = editMode;
      },
      handleContextMenuEdit() {
        if (this.selectedMode !== 'select') return;

        setTimeout(() => {
          this.selectedHtmlElement.focus();
        }, 0);

        if (this.isElementInsideTopBar(this.contextMenu.lastElement)) {
          return;
        }

        const isSmartChange = this.changes.some(
          (change) =>
            change.selector === this.selector &&
            (change.type === TYPES.SMART_AB_TEST || change.type === TYPES.SMART_PERSONALIZATION),
        );

        if (isSmartChange) {
          return;
        }

        const parent = findParentDiv(this.contextMenu.lastElement, SELECTABLE_TAG_NAMES);
        const hasEditableTextContent = this.hasTextContent(parent);
        if (!parent || !hasEditableTextContent) return;

        DOM.setAttribute(parent, 'contentEditable', true);
        this.currentlyEditing = true;
        this.selectedPage = window.location.pathname;
        this.setSelectedElement(parent);
        this.setOriginalText(parent.innerHTML);
        DOM.removeClass(this.selectedHtmlElement, OUTLINE_HOVER_CLASS);

        this.initializedServices.heapTrack({ location: 'user-page', action: 'element-selected' });
      },
      handleHiddenElementClicked(change, element) {
        this.setSelectedElement(element);
        this.setOriginalText(element.innerHTML);
        this.setContextMenuElement(element);
        ActionService.toggleChanges({ changes: [change] });
        DOM.setAttribute(element, 'contentEditable', true);
        this.toggleContextMenu({ visibility: true });
      },
      handleContextMenuHTML({ change }) {
        this.handleShowHTMLEditorOnClick(change);
      },
      handleShowHTMLEditorOnClick(change) {
        if (change) {
          this.modal.$show('HTMLEditor', { change, exist: true });
          return;
        }

        DOM.removeAttribute(this.contextMenu.lastElement, 'contentEditable');

        this.modal.$show('HTMLEditor', {
          content: this.contextMenu.lastElement.outerHTML,
          type: TYPES.HTML,
        });
      },
      handleHTMLModalCancel() {
        const parent = findParentDiv(this.contextMenu.lastElement, SELECTABLE_TAG_NAMES);
        const hasEditableTextContent = this.hasTextContent(parent);

        if (!parent || !hasEditableTextContent) return;

        DOM.setAttribute(parent, 'contentEditable', true);
      },
      handleHTMLDone(change, wasEditMode) {
        this.toggleContextMenu({ visibility: false });
        this.addChange(change);
        ActionService.applyChange({ change });
        const element = DOM.getElementById(change.id);
        this.setSelectedElement(element);
        if (!wasEditMode) {
          OverlayService.hideOverlay();
          OverlayService.createOverlay({ element, onClick: this.handleEditingFinished });
          this.toggleInsertMenu({ visibility: true, change });
        }
      },
      target(e) {
        return this.advancedSelect ? document.elementsFromPoint(e.clientX, e.clientY)[1] : e.target;
      },
      createFloatingActionBars() {
        this.changes.forEach((change) => {
          const element = ActionService.getElement(change);
          if (!element) return;

          const content = element.innerHTML;
          const floatingActionBarInstance = createApp(FloatingActionBar, {
            change,
            content,
          });
          floatingActionBarInstance.config.globalProperties.emitter = emitter;
          floatingActionBarInstance.mount(element);
          this.floatingActionBarInstances[change.id] = floatingActionBarInstance;
        });
      },
      unmountFloatingActionBarInstanceById(changeId) {
        if (this.floatingActionBarInstances.hasOwnProperty(changeId)) {
          this.floatingActionBarInstances[changeId].unmount();
        }
      },
      toggleFloatingActionBars() {
        Object.keys(this.floatingActionBarInstances).map((changeId) =>
          this.unmountFloatingActionBarInstanceById(changeId),
        );

        if (this.displayChanges) {
          this.createFloatingActionBars();
        }
      },
      handleReposition({ change, selector = null }) {
        this.setSelectedMode('select');
        this.setRepositionState(true);
        this.setRepositionChange(change);

        if (selector) {
          const target = document.querySelector(selector);
          this.repositionWithSelector(target);
          return;
        }

        this.toggleInsertMenu({ visibility: false });
        OverlayService.hideOverlay();
        OverlayService.restoreScroll();
      },
      repositionWithSelector(target) {
        this.setSelectedElement(target);
        this.$nextTick(() => {
          this.modifyChangeById({
            id: this.repositionChange.id,
            property: 'selector',
            to: this.selector,
          });
          ActionService.applyChange({ change: this.repositionChange });

          const element = DOM.getElementById(this.repositionChange.id);
          OverlayService.calculateOverlayPosition(element);

          this.setRepositionState(false);
          this.setRepositionChange(null);
        });
      },
      reposition(e, target) {
        this.setSelectedElement(target);
        this.$nextTick(() => {
          if (this.selector !== `#${this.repositionChange.id}`) {
            const isRepositionApplicable = this.checkRepositionIsApplicable(
              e,
              target,
              this.repositionChange,
            );
            if (!isRepositionApplicable) return;

            this.modifyChangeById({
              id: this.repositionChange.id,
              property: 'selector',
              to: this.selector,
            });
            ActionService.applyChange({ change: this.repositionChange });

            this.postRepositionProcesses();
            this.setRepositionState(false);
            this.setRepositionChange(null);
          }
        });
      },
      checkRepositionIsApplicable(e, target, repositionChange) {
        if (repositionChange.type !== TYPES.TEXT) return true;

        const isEditable = !!getEditableElement(e, target);
        if (isEditable) return true;

        this.notify(this.$t('notifications.unableToReposition'), 'warning');
      },
      postRepositionProcesses() {
        if ([TYPES.INSERT_TEXT, TYPES.INSERT_HTML].includes(this.repositionChange.type)) {
          this.toggleInsertMenu({
            visibility: true,
            change: this.repositionChange,
            editMode: true,
          });
        }
      },
      setRunningSmartABTests(changes) {
        changes.forEach((change) => {
          const { running } = change;
          if (running?.length) {
            this.modifyChangeById({
              id: change.id,
              property: 'running',
              to: running,
            });
          }
        });
      },
    },
  };
</script>
