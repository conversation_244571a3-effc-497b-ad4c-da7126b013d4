<template lang="pug">
v-card
  v-card-title
    | Smart Personalization
    v-spacer
  v-card-text
    v-select(label="Select a prompt" :items="items" v-model="selectedItem" @change="handleChange")
    v-textarea(label="Prompt" v-model="prompt")
    v-card-actions
      div You can use the following placeholders: <strong>{originalContent}, {domainContext}, {adContext}</strong>
      v-spacer
      template(v-if="existing")
        v-btn(@click="edit()") Save
      template(v-else)
        v-btn(@click="save()" :disabled="!selectedItem || !prompt") Save
</template>

<script>
  const API_RESOURCE_URI = '/ai-prompts/smart-personalization';

  export default {
    data: () => ({
      loading: false,
      items: ['headline', 'subheadline', 'text', 'list'],
      selectedItem: '',
      prompts: [],
      prompt: '',
      existing: false,
    }),
    mounted() {
      this.getPrompts();
    },
    methods: {
      handleChange() {
        const result = this.prompts.find((prompt) => prompt.type === this.selectedItem);
        if (!result) {
          this.existing = false;
          this.prompt = '';
          return;
        }

        this.existing = true;
        this.prompt = result.prompt;
      },
      async getPrompts() {
        this.loading = true;
        const {
          data: { result },
        } = await this.$axios.get(API_RESOURCE_URI);
        if (result) {
          this.prompts = result.value;
        }
        this.loading = false;
      },
      async save() {
        const response = await this.$axios.post(API_RESOURCE_URI, {
          contentType: this.selectedItem,
          prompt: this.prompt,
        });
        await this.notify(response.status);
        this.existing = true;
      },
      async edit() {
        const response = await this.$axios.patch(API_RESOURCE_URI, {
          contentType: this.selectedItem,
          prompt: this.prompt,
        });
        await this.notify(response.status);
      },
      async notify(statusCode) {
        if (statusCode === 200) {
          this.$notifySuccess({
            title: 'Saved successfully',
          });
          await this.getPrompts();
        } else {
          this.$notifyError({
            text: 'Error while saving prompt.',
          });
        }
      },
    },
  };
</script>
