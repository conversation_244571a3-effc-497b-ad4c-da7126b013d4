<template lang="pug">
div
  .thank-you-block
    h2.thank-you-container-success-message-l1 {{ $t('sales-form.thank-you-h2') }}
    .thank-you-container-success-message-l2 {{ $t('sales-form.thank-you-success') }}

  .thank-you-block
    .thank-you-container-journey
      span.thank-you-container-journey-l1 {{ $t('sales-form.thank-you-journey') }}
      template(v-if="passwordSetUrl")
        span.thank-you-container-journey-l2 {{ $t('sales-form.thank-you-journey-v-2-step') }}
      template(v-else)
        span.thank-you-container-journey-l2 {{ $t('sales-form.thank-you-journey-v-1-step') }}

  .thank-you-block
    template(v-if="passwordSetUrl")
      .thank-you-container-steps
        .thank-you-container-step
          //- .thank-you-container-step-left
          //-   .thank-you-container-step-content-block
          //-     .thank-you-container-step-number 1.
          .thank-you-container-step-center
            .thank-you-container-step-content-block
              .thank-you-container-step-title 1. {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.set-password.title') }}
              .thank-you-container-step-info {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.set-password.info') }}
          .thank-you-container-step-right
            .thank-you-container-step-content-block
              .thank-you-container-set-password-button
                a(:href="passwordSetUrl" target="_blank")
                  form-button {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.set-password.button') }}

        .thank-you-container-step
          .thank-you-container-step-center
            .thank-you-container-step-content-block
              .thank-you-container-step-title 2. {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.title') }}
              .thank-you-container-step-info {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.info') }}
          .thank-you-container-step-right
            .thank-you-container-step-content-block
              .thank-you-container-set-password-button
                a(
                  :href="$t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.button.href')"
                  target="_blank"
                )
                  form-button {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.button.label') }}

    template(v-else)
      .thank-you-container-steps.one-step-version
        .thank-you-container-step
          .thank-you-container-step-center
            .thank-you-container-step-content-block
              .thank-you-container-step-title {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.title') }}
              .thank-you-container-step-info {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.info') }}
          .thank-you-container-step-right
            .thank-you-container-step-content-block
              .thank-you-container-set-password-button
                a(
                  :href="$t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.button.href')"
                  target="_blank"
                )
                  form-button {{ $t('sales-form.thank-you-audit-form.birthday-2024.steps.audit.button.label') }}

      .thank-you-block
        .thank-you-container-login-link-block
          span.thank-you-container-login-link-block-text-part {{ $t('sales-form.thank-you-audit-form.birthday-2024.login-option.part1') }}
          a.thank-you-container-login-link-block-link-part(:href="adminUrl" target="_blank") {{ $t('sales-form.thank-you-audit-form.birthday-2024.login-option.part2') }}
</template>

<script>
  import { createNamespacedHelpers } from 'vuex';
  import SuccessCheckmark from '../../../components/sales-form/SuccessCheckmark.vue';
  import FormButton from '../../../components/sales-form/FormButton.vue';

  const { mapState } = createNamespacedHelpers('sales-form');

  export default {
    components: {
      SuccessCheckmark,
      FormButton,
    },
    layout: 'sales-form',
    computed: {
      ...mapState({
        paymentMethod: (state) => state.form.paymentMethod,
      }),
      passwordSetUrl() {
        return this.$route.query.passwordSetUrl;
      },
      adminUrl() {
        return `${this.$store.state.vars.ADMIN_URL}/dashboard`;
      },
    },
  };
</script>
