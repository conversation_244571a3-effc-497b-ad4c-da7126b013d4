const { arrayify } = require('@om/common');
const PPOPromptTemplateRepository = require('../../lib/repositories/PPOPromptTemplateRepository');

const list = async (req, res) => {
  const data = await PPOPromptTemplateRepository.list();

  res.send({ success: true, data });
};

const create = async (req, res) => {
  try {
    const { id, prompt } = req.body;
    await PPOPromptTemplateRepository.upsert({
      id,
      prompt: {
        ...prompt,
        variableNames: arrayify(prompt.variableNames),
      },
    });
    res.send({ success: true });
  } catch (e) {
    res.send({ success: false, message: e.extensions?.message ?? e?.message });
  }
};

const remove = async (req, res) => {
  await PPOPromptTemplateRepository.remove(req.body);
  res.send({ success: true });
};

module.exports = {
  list,
  create,
  remove,
};
