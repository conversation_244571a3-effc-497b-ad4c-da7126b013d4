const accountRepository = require('../../lib/repositories/accountRepository');
const customerRepository = require('../../lib/repositories/customerRepository');
const paymentService = require('../../lib/services/paymentService');
const visitorLimitResetService = require('../../lib/services/visitorLimitReset');

const { PAYMENT_METHOD_TYPE } = require('../../lib/helpers/paymentMethod');
const braintreeDeclineDetails = require('../../lib/services/details/braintreeDeclineDetails');
const bankTransferDeclineDetails = require('../../lib/services/details/bankTransferDeclineDetails');
const shopifyDeclineDetails = require('../../lib/services/details/shopifyDeclineDetails');

const getVisitorLimitResetDetails = async (req, res) => {
  const { id } = req.params;
  const account = await accountRepository.get(id);
  const customer = await customerRepository.getByOptimonkId(account.databaseId);
  const dateNext = visitorLimitResetService.calculateNextLimitResetDate(
    account.billing.dateExpires,
    customer.payment_method,
  );
  const datePrev = await visitorLimitResetService.getLastLimitResetDate(account.databaseId);
  const historyLogs = await visitorLimitResetService.getLimitResetLogList(account.databaseId);
  const limitResetInfo = {
    dateNext,
    datePrev,
    historyLogs,
  };
  res.send({
    success: true,
    limitResetInfo,
  });
};

const getFlexiPayDetails = async (req, res) => {
  const { id } = req.params;
  const account = await accountRepository.get(id);
  const region = await accountRepository.getRegion(id);

  if (account.type === 'sub') {
    return res.json({ success: true, details: {} });
  }

  const customer = await customerRepository.getByOptimonkId(account.databaseId);
  const paymentMethod = customer?.payment_method ?? null;

  const flexiPayDetail = await paymentService.getFlexiPayDetail(account, region);
  const dateNext = visitorLimitResetService.calculateNextLimitResetDate(
    account.billing.dateExpires,
    paymentMethod,
  );

  const details = {
    ...flexiPayDetail,
    nextLimitResetDate: dateNext,
  };
  return res.json({ success: true, details });
};

const getDeclinedDetails = async (req, res) => {
  const { id } = req.params;
  const customer = await customerRepository.getByOptimonkId(id);
  const paymentMethod = customer?.payment_method ?? null;
  let details = {};

  switch (paymentMethod) {
    case PAYMENT_METHOD_TYPE.BRAINTREE:
    case PAYMENT_METHOD_TYPE.BRAINTREE_PAYPAL:
      details = await braintreeDeclineDetails.getDeclineDetails({ customer });
      break;
    case PAYMENT_METHOD_TYPE.BANK_TRANSFER:
      details = await bankTransferDeclineDetails.getDeclineDetails({ customer });
      break;
    case PAYMENT_METHOD_TYPE.SHOPIFY:
      details = await shopifyDeclineDetails.getDeclineDetails({ customer });
      break;
    default:
      return res.send({ success: false });
  }

  return res.send({
    success: true,
    details: {
      paymentMethod,
      ...details,
    },
  });
};

module.exports = {
  getVisitorLimitResetDetails,
  getFlexiPayDetails,
  getDeclinedDetails,
};
