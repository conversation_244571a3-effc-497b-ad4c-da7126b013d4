const { discountPlanModel, couponCodeFor, models } = require('../plans');
const getBaseDeal = require('./baseDeal');

const getDeal = (account, login, locale) => {
  let couponCode = 'INTERNATIONALBIRTHDAY2021';
  locale = 'en';

  if (account && account.billing.package !== 'FREE') {
    return null;
  }

  if (login) {
    couponCode = couponCodeFor(login.email, couponCode);
  }

  const discount = 50;
  const planModel = discountPlanModel(models.threeMonthFreemium, discount);

  return {
    ...getBaseDeal(locale, planModel),
    discount,
    deal: 'international-birthday-2021',
    couponCode,
    zapier: 'https://hooks.zapier.com/hooks/catch/107350/bbk3dfw/',
  };
};

module.exports = getDeal;
