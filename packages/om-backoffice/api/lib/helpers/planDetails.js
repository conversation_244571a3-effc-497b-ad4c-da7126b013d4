const {
  freemiumPackages,
  notFreemiumPackages,
  invalidSkues,
  fromString,
  toString,
  isNonPayingPackage,
  isNonPayingPlan,
  isFreemiumPackage,
  isFreemiumSKU,
  isNotFreemiumPackage,
  isOldPackage,
  isDowngrade,
  isUpgrade,
} = require('@om/payment/src/helpers/planDetails');
const { FREE_PACKAGE_SKU, TRIAL_PACKAGE_SKU } = require('@om/payment/src/plans');

const getPackagePrefixFromRegion = (region) => {
  switch (region) {
    case 'Hungary':
      return 'Package-HU';
    case 'Germany':
      return 'Package-DE';
    default:
      return 'Package-EN';
  }
};

const fromRegionPackagePeriod = (region, _package, period) => {
  const packagePrefixRegion = getPackagePrefixFromRegion(region);
  return `${packagePrefixRegion}-${_package.toUpperCase()}-${period}`;
};

const isEnterpriseOrMaster = (sku) => {
  return sku.toLowerCase().includes('master') || sku.toLowerCase().includes('enterprise');
};

module.exports = {
  FREE_PACKAGE_SKU,
  TRIAL_PACKAGE_SKU,
  freemiumPackages,
  notFreemiumPackages,
  invalidSkues,
  isOldPackage,
  isDowngrade,
  isUpgrade,

  fromRegionPackagePeriod,
  fromString,
  toString,
  isFreemiumSKU,
  isFreemiumPackage,
  isNotFreemiumPackage,
  isNonPayingPackage,
  isNonPayingPlan,
  getPackagePrefixFromRegion,
  isEnterpriseOrMaster,
};
