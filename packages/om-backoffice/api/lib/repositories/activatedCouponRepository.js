const { ObjectID } = require('mongodb');
const { getCollectionRef } = require('../../../util/mongo');

const CONFIG_COLL_REF = 'activated_coupon';
const CONFIG_COLL_REF_ACCOUNTS = 'accounts';
const CONFIG_COLL_REF_LOGINS = 'logins';
const PROJECTION_RECURRING_COUPON = {
  accountId: 1,
  couponCode: 1,
  couponId: 1,
  dateStart: 1,
  dateEnd: 1,
  createdAt: 1,
  cancelledAt: 1,
  cancelledBy: 1,
  fixedPriceForFirstPlan: 1,
};

const _getCollection = () => getCollectionRef(CONFIG_COLL_REF);
const _getAccountsCollection = () => getCollectionRef(CONFIG_COLL_REF_ACCOUNTS);
const _getLoginsCollection = () => getCollectionRef(CONFIG_COLL_REF_LOGINS);

const getByDBId = (_id) => _getCollection().findOne({ _id });

const insert = (coupon) => _getCollection().insertOne(coupon);

const updateBy = (accountId, criteria, properties) =>
  _getCollection().updateMany(
    { accountId: parseInt(accountId, 10), ...criteria },
    { $set: properties },
  );

const listBy = (accountId, criteria) =>
  _getCollection().find({ accountId: parseInt(accountId, 10), ...criteria });

const getLastBy = async (accountId, criteria) => {
  const documents = await listBy(accountId, criteria).sort({ _id: -1 }).limit(1).toArray();

  return documents?.length === 1 ? documents[0] : null;
};

const __getAccountData = async (coupons) => {
  const databaseIds = new Set();
  for (const coupon of coupons) {
    if (coupon.accountId) databaseIds.add(coupon.accountId);
  }

  const accounts = await _getAccountsCollection()
    .find(
      {
        databaseId: { $in: Array.from(databaseIds.values()) },
      },
      { projection: { databaseId: 1, users: 1 } },
    )
    .toArray();

  const loginIds = new Set();
  for (const account of accounts) {
    if (account?.users?.[0]?.loginId) loginIds.add(`${account?.users?.[0]?.loginId}`);
  }

  const logins = await _getLoginsCollection()
    .find(
      {
        _id: { $in: Array.from(loginIds.values()).map((id) => ObjectID(id)) },
      },
      { projection: { email: 1 } },
    )
    .toArray();

  return { accounts, logins };
};

const __mergeAccountData = async (coupons) => {
  const { accounts, logins } = await __getAccountData(coupons);

  coupons.forEach((coupon, index) => {
    const account = accounts.find(({ databaseId }) => coupon.accountId === databaseId);
    const loginId = account?.users?.[0]?.loginId;
    const login = logins.find(({ _id }) => loginId && _id.equals(loginId));
    const email = login?.email;
    if (email && coupons[index]) {
      coupons[index].email = email;
    }
  });

  return coupons;
};

const __filterCoupons = ({ coupons, criteria }) => {
  if (!criteria?.length) return coupons;

  return coupons.filter((coupon) => {
    return criteria.some(
      ({ key, predicate }) => predicate?.test?.(`${coupon[key]}`) ?? coupon[key] === predicate,
    );
  });
};

const _aggregatedCouponInfo = async ({
  criteria,
  prefiltering = {},
  isCount = false,
  page = null,
  limit = null,
  sorting = null,
}) => {
  const rawActivatedCoupons = await _getCollection()
    .find(prefiltering, PROJECTION_RECURRING_COUPON)
    .sort(sorting)
    .toArray();
  const activatedCoupons = await __mergeAccountData(rawActivatedCoupons);

  const filtered = __filterCoupons({ coupons: activatedCoupons, criteria });

  if (isCount) return filtered?.length ?? 0;

  if (page >= 0 && limit > 0) {
    const startIndex = page * limit;
    return filtered.slice(startIndex, startIndex + limit);
  }

  return filtered;
};

const listAll = async (options) => _aggregatedCouponInfo({ ...options, isCount: false });

const countAll = async (options) => {
  const result = await _aggregatedCouponInfo({ ...options, isCount: true });

  return result ?? 0;
};

const updateByDBId = (id, properties) => {
  return _getCollection().updateOne({ _id: id }, { $set: { ...properties } });
};

const runUpdateByDatbaseId = async (databaseId, query) => {
  return _getCollection().updateMany({ accountId: parseInt(databaseId, 10) }, query, {
    new: true,
  });
};

module.exports = {
  ObjectID,
  getByDBId,
  insert,
  updateBy,
  listBy,
  getLastBy,
  listAll,
  countAll,
  updateByDBId,
  runUpdateByDatbaseId,
};
