require('dotenv-flow').config();
const agencyUsageReportService = require('./agencyUsageReportService');

const SUB_NAMES = {
  SUB1: 'Sub 1',
  SUB2: 'Sub 2',
  SUB3: 'Sub 3',
};

describe('Agency usage report service', () => {
  test('Daily stats', () => {
    const rows = [
      { subAccountName: SUB_NAMES.SUB1, usedVisitor: 0, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB2, usedVisitor: 100, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB1, usedVisitor: 200, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB2, usedVisitor: 1200, type: 'daily' },
    ];

    const result = agencyUsageReportService.calculateDayDifferences(rows);

    expect(result[SUB_NAMES.SUB1]).toBe(200);
    expect(result[SUB_NAMES.SUB2]).toBe(1100);
  });
  test('Daily with reset stats', () => {
    const rows = [
      { subAccountName: SUB_NAMES.SUB1, usedVisitor: 0, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB2, usedVisitor: 100, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB3, usedVisitor: 50, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB1, usedVisitor: 200, type: 'reset' },
      { subAccountName: SUB_NAMES.SUB2, usedVisitor: 200, type: 'reset' },
      { subAccountName: SUB_NAMES.SUB1, usedVisitor: 200, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB2, usedVisitor: 1200, type: 'daily' },
      { subAccountName: SUB_NAMES.SUB3, usedVisitor: 90, type: 'daily' },
    ];

    const result = agencyUsageReportService.calculateDayDifferences(rows);

    expect(result[SUB_NAMES.SUB1]).toBe(400);
    expect(result[SUB_NAMES.SUB2]).toBe(1300);
    expect(result[SUB_NAMES.SUB3]).toBe(40);
  });
});
