<template lang="pug">
form-section.summary-section
  .optional-login-block(v-if="!customerAuthenticated && !onlyNewRegistration")
    span {{ $t('sales-form.login-if-has-account.part-1') }}
    span.login-link(@click="loginVisible = true") {{ $t('sales-form.login-if-has-account.part-2') }}
    span {{ $t('sales-form.login-if-has-account.part-3') }}
  h2
    | {{ $t('sales-form.personal-section-heading') }}
    span.login-text(v-if="customerAuthenticated")
      span {{ $t('sales-form.login-with-other-account') }}
      a.switch-login(href="javascript:void(0)" @click="loginVisible = true") {{ $t('sales-form.here') }}
      | )
  form-row(:label="$t('sales-form.email-label')" :v="v.email")
    form-text-input(
      v-model.trim="v.email.$model"
      type="email"
      :disabled="customerPrefetched"
      autocomplete="email"
      :class="{ 'is-invalid': !!registrationError }"
    )
    form-validation-error(v-if="registrationError")
      .message(v-if="registrationError" v-html="registrationError")
    .login-text(v-if="loginNeeded && !customerAuthenticated && !onlyNewRegistration")
      span {{ $t('sales-form.login-needed') }}&nbsp;
      a.login-link-below(href="javascript:void(0)" @click.prevent="loginVisible = true") {{ $t('sales-form.login') }}
    .login-text(v-if="loginNeeded && !customerAuthenticated && onlyNewRegistration")
      span.form-error-message {{ $t('sales-form.email-already-in-use') }}&nbsp;
  .personal-info(v-show="form.paymentMethod.name !== 'shopify'")
    template(v-if="locale === 'hu'")
      form-row(:label="$t('sales-form.lastname-label')" :v="v.lastname")
        form-text-input(v-model.trim="v.lastname.$model" autocomplete="family-name")
      form-row(:label="$t('sales-form.firstname-label')" :v="v.firstname")
        form-text-input(v-model.trim="v.firstname.$model" autocomplete="given-name")
    template(v-else)
      form-row(:label="$t('sales-form.firstname-label')" :v="v.firstname")
        form-text-input(v-model.trim="v.firstname.$model" autocomplete="given-name")

      form-row(:label="$t('sales-form.lastname-label')" :v="v.lastname")
        form-text-input(v-model.trim="v.lastname.$model" autocomplete="family-name")

    form-row(:label="$t('sales-form.phone-label')" :v="v.phone")
      form-phone-input(
        ref="phoneInput"
        autocomplete="tel"
        v-model.trim="v.phone.$model"
        :initialCountry="this.form.country && this.form.country.iso"
      )
</template>

<script>
  import { createNamespacedHelpers } from 'vuex';
  import { mapFields } from 'vuex-map-fields';
  import FormSection from './FormSection.vue';
  import FormRow from './FormRow.vue';
  import FormCheckbox from './FormCheckbox.vue';
  import FormTextInput from './FormTextInput.vue';
  import FormPhoneInput from './FormPhoneInput.vue';
  import FormButton from './FormButton.vue';
  import FormValidationError from './FormValidationError.vue';

  const { mapState } = createNamespacedHelpers('sales-form');

  export default {
    components: {
      FormSection,
      FormRow,
      FormCheckbox,
      FormTextInput,
      FormPhoneInput,
      FormButton,
      FormValidationError,
    },
    props: {
      v: { type: Object, required: true },
    },
    computed: {
      ...mapState([
        'registrationError',
        'form',
        'locale',
        'customerPrefetched',
        'loginNeeded',
        'customerAuthenticated',
        'onlyNewRegistration',
      ]),
      ...mapFields('sales-form', ['loginVisible']),
    },
    watch: {
      'form.country': function (country) {
        if (!this.form.phone && country) {
          this.$refs.phoneInput.setCountry(country.iso);
        }
      },
    },
  };
</script>

<style lang="sass">
  .login-text
    font-size: 14px
    font-weight: 700

  .login-text
    font-size: 16px
    font-weight: 400
    margin-left: 10px

  .optional-login-block
    margin-top: 30px
    margin-bottom: 30px

  .login-link
    cursor: pointer
    font-weight: 700
    color: #ed5a29

  .message a
    color: #ed5a29
    font-weight: bold
</style>
