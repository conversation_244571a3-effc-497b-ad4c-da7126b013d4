const { logger } = require('@om/logger');

const finishedHandler = async ({ step, params }) => {
  const trace = step.flowId;
  const { jobs } = params;
  const failed = jobs.find((job) => job.errorResult);
  if (failed) {
    step.setRetry(false);
    const err = new Error('Error in Load query');
    logger.error({ trace, err, data: { jobs } }, err.message);
    throw err;
  }

  const { appId, accountId, providerServiceId, resource } = step.context;
  logger.info(
    {
      trace,
      data: {
        appId,
        accountId,
        providerServiceId,
        resource,
      },
    },
    'Shoprenter ELT flow successfully finished',
  );
};

module.exports = {
  finishedHandler,
};
