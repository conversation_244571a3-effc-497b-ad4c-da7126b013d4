FROM node:16.14-alpine
WORKDIR /usr/src/app
COPY package.json yarn.lock .yarnrc.yml ./
COPY .yarn .yarn
COPY ./packages/workflow-executors/periodic-query-controller/package.json ./packages/workflow-executors/periodic-query-controller/
COPY ./libraries/. /usr/src/app/libraries/
WORKDIR /usr/src/app/packages/workflow-executors/periodic-query-controller
ENV CI=true
RUN yarn workspaces focus @om/workflow-executor-periodic-query-controller --production
COPY ./packages/workflow-executors/periodic-query-controller/. .
CMD [ "yarn", "node", "src/index.js" ]
