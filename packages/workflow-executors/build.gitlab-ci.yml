# This file should not be included in the root .gitlab-ci.yml file
# since it will be triggered as a downstream pipeline.

stages:
  - build

include:
  - local: 'ops/ci/docker-devops.yml'
  - local: 'ops/ci/gcloud-devops.yml'
  - local: 'ops/ci/build.yml'

build executors:
  extends: .build-workflow-gitlab
  stage: build
  parallel:
    matrix:
      - PACKAGE_NAME:
          - asset-downloader
          - delay
          - etl
          - flow
          - heap
          - periodic-query-controller
          - poll
          - ppo-chatgpt
          - ppo-controller
          - ppo-initializer
          - ppo-publisher
          - product-feed-controller
          - product-feed-parser
          - query
          - shoprenter-elt-controller
          - shoprenter-extract
          - spr-generation-controller
          - spr-text-embedding-controller
