const { getFormattedTableName, getPPOResultTableId, runQuery } = require('@om/queries');
const { logger } = require('@om/logger');
const { BigQuery } = require('@google-cloud/bigquery');
const { PPO_COLLECTION_NAME } = require('../common/const');
const { TimeoutError } = require('../common/errors');

const ONE_MINUTE_MS = 60 * 1000;

const checkPPOResultRowQuery = (tableId) => {
  return `
  WITH deduplicatedProducts AS (
    SELECT
      productId,
      error
    FROM ${getFormattedTableName(tableId)}
    WHERE timestamp >= @startedAt
    QUALIFY ROW_NUMBER() OVER (PARTITION BY productId ORDER BY timestamp DESC, IF(error IS NULL, 0, 1) DESC) = 1
  )
  SELECT
    COUNT(*) AS processedProductCount,
    COUNTIF(error IS NOT NULL) as erroredProductCount
  FROM deduplicatedProducts as ppo
`;
};

const checkTimeout = ({ startedAt, lastProgressCountChangeAt, processedProductCount }) => {
  const didProcessAnything = processedProductCount > 0;
  const now = new Date();

  if (lastProgressCountChangeAt) {
    const timeSinceLastProgressCountChange = now - lastProgressCountChangeAt;
    if (didProcessAnything && timeSinceLastProgressCountChange > 10 * ONE_MINUTE_MS) {
      throw new TimeoutError('No progress for 10 minutes');
    }
  }

  const timeSinceStarted = now - startedAt;
  if (!didProcessAnything && timeSinceStarted > 180 * ONE_MINUTE_MS) {
    throw new TimeoutError('No progress for 3 hours');
  }
};

const handleTimeout = async (err, { mongoClient, step, data }) => {
  const {
    context: { databaseId, uuid },
  } = step;

  logger.warn(
    {
      trace: step.flowId,
      err,
      data: {
        step,
        data,
      },
    },
    'Timeout error in checkProgressHandler',
  );

  await mongoClient
    .db()
    .collection(PPO_COLLECTION_NAME)
    .updateOne(
      {
        databaseId: +databaseId,
        'lastGeneration.uuid': uuid,
      },
      {
        $set: {
          'lastGeneration.error': {
            code: 'GENERATION_TIMEOUT',
            message: err.message,
          },
        },
      },
    );

  return step.nextStep.finished();
};

const fetchCurrentState = async ({ projectId, step, data }) => {
  const { startedAt } = data;
  const bigQueryClient = new BigQuery({ projectId });
  const {
    context: { providerServiceId, databaseId, uuid, location },
  } = step;

  const ppoResultTableId = getPPOResultTableId(databaseId, providerServiceId, uuid);
  const [currentState] = await runQuery(bigQueryClient, checkPPOResultRowQuery(ppoResultTableId), {
    params: {
      uuid,
      startedAt: new Date(startedAt),
    },
    location,
  });

  return currentState;
};

const updateState = async (
  { processedProductCount, erroredProductCount },
  { mongoClient, step, data },
) => {
  const { originalProcessedProductCount, loadedProductCount } = data;
  const {
    context: { databaseId, uuid },
  } = step;
  const filter = {
    databaseId: +databaseId,
    'lastGeneration.uuid': uuid,
  };
  const updateDoc = {
    $set: {
      // number of products loaded for the current generation
      'lastGeneration.loadedProductCount': loadedProductCount,
      // number of processed products during the current generation (both successful and failed)
      'lastGeneration.processedProductCount': processedProductCount,
      // number of errored products during the current generation
      'lastGeneration.erroredProductCount': erroredProductCount,
      // original number of processed products in result table, before the new generation has started
      'lastGeneration.originalProcessedProductCount': originalProcessedProductCount,
    },
  };

  logger.debug({ trace: step.flowId, data: { filter, updateDoc } }, 'Updating generation state');
  await mongoClient.db().collection(PPO_COLLECTION_NAME).updateOne(filter, updateDoc);
};

const determineNextStep = (currentState, { step, data }) => {
  const { startedAt, loadedProductCount, lastProgressCountChangeAt, lastProgressCount = 0 } = data;
  const { processedProductCount } = currentState;
  const hasAllResult = currentState.processedProductCount >= loadedProductCount;
  logger.info(
    {
      trace: step.flowId,
      data: { step, data, currentState, hasAllResult },
    },
    'CheckProgress compare',
  );

  if (hasAllResult) {
    return step.nextStep.finished();
  }

  checkTimeout({
    startedAt: new Date(startedAt),
    lastProgressCountChangeAt: lastProgressCountChangeAt
      ? new Date(lastProgressCountChangeAt)
      : null,
    processedProductCount,
  });

  // 1 min
  step.nextStepConfig.delay = { delay: 60000 };

  if (lastProgressCount !== processedProductCount) {
    data.lastProgressCountChangeAt = new Date();
  }

  data.lastProgressCount = processedProductCount;

  return step.nextStep.delay(data);
};

async function checkProgressHandler({ projectId, step, data, mongoClient }) {
  logger.info(
    {
      trace: step.flowId,
      data: {
        step,
        data,
      },
    },
    'CheckProgress handler invoked',
  );

  try {
    const currentState = await fetchCurrentState({
      projectId,
      data,
      step,
    });

    await updateState(currentState, { mongoClient, step, data });

    determineNextStep(currentState, { step, data });
  } catch (err) {
    if (err instanceof TimeoutError) {
      return handleTimeout(err, { mongoClient, step, data });
    }
    logger.error(
      { trace: step.flowId, err, data: { step, data } },
      'Error in checkProgressHandler',
    );

    throw err;
  }
}

module.exports = {
  checkProgressHandler,
};
