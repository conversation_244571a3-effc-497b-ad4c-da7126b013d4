{% set iframeless = parameters.isIFrameless ? "Iframeless" : "" %}
{% extends 'campaignBase' ~ iframeless ~ '.html.twig' %}

{% block iframe_container %}
    <div class="optimonk-container optimonk-{{ parameters.displayGroup }}-container"
            id="optimonk-overlay-campaign-{{ parameters.campaignId }}"
            style="display: none; background: rgba({{ parameters.overlayColor }}, {{ parameters.overlayOpacity }});">

        <div class="optimonk-middle">
            {{ parent() }}
        </div>

        {% if poweredByLink %}
            <div class="powered-by-optimonk">
                <a
                        href="{{ poweredByLinkBase }}/?utm_source=link&utm_medium=optimonk_popup&utm_campaign={{ parameters.campaignId }}&domain={{ parameters.domain }}"
                        target="_blank"
                        rel="nofollow"
                        class="powered-by-optimonk-link">
                </a>
            </div>
        {% endif %}
    </div>
{% endblock %}
