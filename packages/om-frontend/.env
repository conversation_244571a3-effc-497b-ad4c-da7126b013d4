APP_PORT=9300

# MongoDb
MONGO_ADDRESS=mongodb://localhost:30000/optimonk2

# OM Domains
DOMAIN_FRONTEND=https://front.optimonk.dev
DOMAIN_CDN_STATIC=https://cdn-static.optimonk.dev
DOMAIN_BACKEND=https://backend.optimonk.dev
DOMAIN_SYMFONY=
CDN_FRONTEND_DOMAIN=https://front.optimonk.dev
CONTENT_URL=https://s3.optimonk.dev/test-bucket
WHITELABEL_CONTENT_URL=https://cdn.whitelabel.dev/test-bucket
SSR_URL=https://ssr.optimonk.dev/ssr
SSR_CDN_URL=https://cdn-renderer.optimonk.dev/ssr
EMBEDDED_CDN_CONTENT_URL=https://front.optimonk.dev/public
EMBEDDED_CDN_DOMAIN=https://front.optimonk.dev/public
ACCOUNT_DATA_CDN_DOMAIN=https://front.optimonk.dev/public
LIMIT_CDN_DOMAIN=https://front.optimonk.dev/public
FAKECLIENT_URL=https://fakeclient.optimonk.dev

# Redis
REDIS_HOST=localhost
REDIS_EXPIRE=30

POWERED_BY_LINK_BASE_HU=https://landing.optimonk.hu/powered-by-optimonk
POWERED_BY_LINK_BASE_EN=https://www.optimonk.com/powered-by-optimonk

TOKEN=7p0pRC1ijdMBz2zABxTRNzXEI26gpsROGsJddMheIyInbskBYdrCXBmx0Yt8Gkalfjbf2dMik3afeJjBtaBw1NpHXK1eM9egSPRh
WEBHOOK_TOKEN=ZQEn6EPbnj3ufsShgWEg4nE9tzLGEpR3kYRw7X3fMRdV6wNqzT6PXAUCtzHcnEju

BACKOFFICE_AUTH_USER=q89FfSHnBjFYg2nFB0SuLtB6wN1LtSMcHFZ9IpJok9ejgtXj0CcyuEabCKWiNupj
BACKOFFICE_AUTH_PASS=2w2pxAXJEAJUpw9DODdxNSN4bssRjAfWN5TTw4sLDyANM0JRx91u3tLBs4gYjVuU

ENVIRONMENT=dev

# superadmin
SUPERADMIN_USERNAME=admin
SUPERADMIN_PASSWORD=0219-Kowalsky

# Backend
BACKEND_USERNAME=kGrGY7uZWoWIMQZH5hc863HT9eDDKcQTtKrB2YRfY9k1I7fssTKlA9AUNiu0NxVW
BACKEND_PASSWORD=gz0svbUwvlRIkvU1qh0YziplTlvkSNp6t8XrE2kUIw1TcPLeMccJJhCrxud2erMH

# ProductSyncService
PRODUCT_SYNC_SERVICE_URL=https://product-sync-service-staging.optimonk.com
OM_SHARED_KEY=62OPlu4YnC3uW4yq8YkKsGhKhdH3tvjBgKyyMcOVAToLLXpAANyA1XEsMdXnaBUXdmwrLOR3aJ5f5UuidXFvDcgwKsqDl1eldUeA

# Quickchart

DOMAIN_QC=https://quickchart.io
CHART_WIDTH=480
CHART_HEIGHT=144

# Jet Fabric Script
JF_JS_SCRIPT_URL=https://gs-cdn-stage.optimonk.com/jfclientsdk/latest/jfclientsdk.min.js?ts=9
JF_API_URL=https://jfapistaging.optimonk.com/v2/

PNC_JS_SCRIPT_URL=https://pnc.optimonk.dev/main.js

BUNNY_CDN_API_KEY=
BUNNY_CDN_JF_PULLZONE_NAME=
BUNNY_CDN_FRONT_PULLZONE_NAME=
BUNNY_CDN_SCRIPT_PULLZONE_NAME=

BUNNY_CDN_EMBEDDED_PULLZONE_NAME=
BUNNY_CDN_STORAGE_ZONE_PASS=
BUNNY_CDN_STORAGE_DOMAIN=
BUNNY_CDN_STORAGE_ROOT=

# Store showed variants in cookie for these databaseIds (comma separated list of numbers, e.g: "1,2,3,4")
TRUE_AB_TEST_ACCOUNT_IDS=1426,23856,37243,86514,88147,118971,58417

SPA_ACCOUNTS_DOMAINS=44:app.optimonk.dev,58417:blendjet.com,58417:bln-nacelle-git-customerchatenableonfirstview.nacelle-x-blendjet.vercel.app,124710:shop.netatmo.com

JF_APP_ID=optimonk

ASSETS_PATH=https://front.optimonk.dev/

# backoffice
BACKOFFICE_DOMAIN=https://backoffice.optimonk.dev
BACKOFFICE_USERNAME=5B8jFwG9HUrFsRaF
BACKOFFICE_PASSWORD=tpNvxj7VKthgZ8PsnVDet76Rxe5zaQrEqvpBwNhX8FufDLLDyBg8kDAW6vKLdLFc

# Shopify
SHOPIFY_API_VERSION=2025-01

ACERCCDB_USERNAME=2snhj775t1hgu1bi599q7t81jh
ACERCCDB_PASSWORD=bddlpuc5lqnbu5m0ihkehgam14b5sncfbiu8riihsi2k1cvnk7t

HEAP_APP_ID=local-project

TRACK_JS_API_KEY=

COUNTRY_HEADER_NAME=X-BunnyCDN-CountryCode
IP_HEADER_NAME=X-BunnyCDN-Client-IP

AI_CDN_URL=https://front.optimonk.dev/ai-data
AI_PPO_CDN_URL=https://front.optimonk.dev/ai-data
