.om-tab-wrapper-v2
  position: absolute
  z-index: 10
  cursor: pointer
  .canv-col
    display: flex
  .om-element
    flex: 1
  .om-view-content-helper
    width: 100%
  @media screen and (max-width: 576px)
    position: fixed
.om-teaser-canvas
  display: flex
  flex-wrap: nowrap !important
  .om-teaser-close
    top: auto
    right: auto
    margin: .5em
    width: 1.5em
    min-width: 1.5em
    height: 1.5em
    background: #f3f5f8
    color: rgba(0,0,0,0.6)
    display: flex
    align-items: center
    justify-content: center
    border-radius: 0.5rem


@media screen and (max-width: 576px)
  .om-teaser-canvas
    /* if column count 2 or 3 */
    $total-columns: 9

    @for $i from 3 through $total-columns
      $w: $i / 12
      [class~="grid"]>[class*="col-#{$i}"],
      [class*="grid-"]>[class*="col-#{$i}"],
      [class*="grid_"]>[class*="col-#{$i}"]
        flex-basis: $w * 100%
        max-width: $w * 100%
    /* if column count 2 or 3 */
