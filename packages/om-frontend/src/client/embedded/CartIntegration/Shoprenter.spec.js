import {
  BUILD_ADD_2_CART_CONFIG,
  BUILD_FETCH_CART_CONFIG,
  HANDLE_ADD_2_CART_RESPONSE,
  HANDLE_FETCH_CART,
} from '../../shared/CartIntegration/constants';
import { fakeResult, add2CartResult, cartClasses } from '../../shared/CartIntegration/fixtures';
import { Shoprenter } from './Shoprenter';

const { extendOptiMonkEmbedded } = require('../../test/utils');

const mockOptiMonkEmbeddedAsDesired = () => {
  extendOptiMonkEmbedded({
    CartIntegration: {
      dispatch: jest.fn(),
    },
  });
};

const getInstance = (debug = false) => new Shoprenter(null, debug);

describe('CartIntegration - Shoprenter::BUILD_ADD_2_CART_CONFIG', () => {
  const subject = getInstance(true);

  const id = '1234';
  const data = new URLSearchParams({ quantity: 1, product_id: '1234' });
  const expectBase = {
    method: 'POST',
    url: '/index.php?route=module/cart/callback',
  };
  test('fetch configuration', () => {
    const config = subject[BUILD_ADD_2_CART_CONFIG]({ id }, true);
    expect(config).toStrictEqual({
      ...expectBase,
      body: data.toString(),
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
  });

  test('XHR configuration', () => {
    const config = subject[BUILD_ADD_2_CART_CONFIG]({ id }, false);
    expect(config).toStrictEqual({
      ...expectBase,
      payload: data.toString(),
    });
  });
});

describe('CartIntegration - Shoprenter::BUILD_FETCH_CART_CONFIG', () => {
  const subject = getInstance(true);

  const expectBase = {
    method: 'GET',
    url: '/cart.json',
  };
  test('fetch configuration', () => {
    const config = subject[BUILD_FETCH_CART_CONFIG](null, true);
    expect(config).toStrictEqual(expectBase);
  });

  test('XHR configuration', () => {
    const config = subject[BUILD_FETCH_CART_CONFIG](null, false);
    expect(config).toStrictEqual({ ...expectBase, payload: null });
  });
});

describe('CartIntegration - Shoprenter::HANDLE_FETCH_CART', () => {
  const subject = getInstance(true);

  const adapter = {
    Cart: {
      get: jest.fn().mockReturnValue({}),
      add: jest.fn(),
      clear: jest.fn(),
    },
  };

  test('add to visitor cart', async () => {
    const fakeProduct = fakeResult.items[0];
    delete fakeProduct.option;
    delete fakeProduct.values;
    const fakeProduct2 = fakeResult.items[1];
    delete fakeProduct2.option;
    delete fakeProduct2.values;
    await subject[HANDLE_FETCH_CART]({ result: fakeResult, getAdapter: async () => adapter });
    expect(adapter.Cart.clear).toHaveBeenCalledTimes(1);
    expect(adapter.Cart.add).toHaveBeenCalledWith(fakeProduct.id, fakeProduct);
    expect(adapter.Cart.add).toHaveBeenCalledWith(fakeProduct2.id, fakeProduct2);
    expect(adapter.Cart.add).toHaveBeenCalledTimes(2);
  });
});

const createEvent = (type, detail) => new CustomEvent(type, { detail });

describe('CartIntegration - Shoprenter::HANDLE_ADD_2_CART_RESPONSE', () => {
  mockOptiMonkEmbeddedAsDesired();
  const subject = getInstance(true);

  const cartToken = 'cart';
  const { sku, id, price, currency, name } = add2CartResult.products[0];
  const product = { sku, id, price, currency, quantity: 1, name };

  test('event (AddToCart)', () => {
    const event = createEvent('AddToCart', { cartToken, product });
    const created = subject.createAddToCartEvt(cartToken, product, product.id);
    expect(created).toStrictEqual(event);
  });

  test('event (cartChanged)', () => {
    const event = createEvent('cartChanged', {
      data: add2CartResult,
      products: add2CartResult.products,
    });
    const created = subject.createAddToCartEvt(cartToken, fakeResult);
    expect(created).toStrictEqual(event);
  });

  test('localstorage setter', () => {
    subject.setLocalStorageItem();
    expect(window.localStorage.getItem('cartModified')).toBe('1');
  });

  test('event triggering', () => {
    document.dispatchEvent = jest.fn();

    subject[HANDLE_ADD_2_CART_RESPONSE]({
      result: add2CartResult,
      id: add2CartResult.products[0].productId,
    });

    const addToCartEvt = createEvent('AddToCart', { cartToken, product });
    const cartChangedEvt = createEvent('cartChanged', {
      data: add2CartResult,
      products: add2CartResult.products,
    });

    expect(document.dispatchEvent).toHaveBeenCalledTimes(2);
    expect(document.dispatchEvent).toHaveBeenCalledWith(addToCartEvt);
    expect(document.dispatchEvent).toHaveBeenCalledWith(cartChangedEvt);
  });

  test.each([
    [true, cartClasses.empty],
    [false, cartClasses.notEmpty],
  ])('getClasses(%p): %p', (empty, expected) => {
    const result = subject.getClasses(empty);
    expect(result).toStrictEqual(expected);
  });

  test.each([
    [undefined, 2],
    [null, 2],
    [0, 2],
    [1, 2],
  ])('updateDoms(%p, %p)', (count, timesCalled) => {
    const subject = getInstance();
    subject.domUpdate = jest.fn();
    subject.updateDoms(count);
    expect(subject.domUpdate).toHaveBeenCalledTimes(timesCalled);
  });
});
