import CouponComponentFactory from './CouponComponentFactory';
import { reportEventJF } from '../../shared/JFEventReport';
import { DOM } from '../shared/DOM';
import { TYPES } from './Coupon.enum';
import { asyncFilter } from '../../shared/helpers';

/**
 *
 * @property {{[key: Number]: CouponSetting[]}} couponsByCampMap
 * @property {{[key: Number]: AbstractCoupon[]}} couponInstancesByCamp
 */
class CouponManager {
  constructor() {
    this.couponsByCampMap = {};
    this.couponInstancesByCamp = {};
    this.couponLocksByCamp = {};
    this.loggedImpressions = {};

    this.addCouponCopyListener();
  }

  /**
   *
   * @param {Array<CouponSetting>} couponSettingsForCampaign
   */
  add(couponSettingsForCampaign) {
    couponSettingsForCampaign.forEach((couponSetting) => {
      if (!this.couponsByCampMap[couponSetting.campaignId]) {
        this.couponsByCampMap[couponSetting.campaignId] = [];
      }
      if (!this.couponInstancesByCamp[couponSetting.campaignId]) {
        this.couponInstancesByCamp[couponSetting.campaignId] = [];
      }

      const couponInstance = CouponComponentFactory.create(couponSetting.type);
      couponInstance.init(
        couponSetting.campaignId,
        couponSetting.variantId,
        couponSetting.uid,
        couponSetting.settings,
      );
      this.couponInstancesByCamp[couponSetting.campaignId].push(couponInstance);
      this.couponsByCampMap[couponSetting.campaignId].push(couponSetting);
    });
  }

  async validateFor(campaignId) {
    if (this.couponInstancesByCamp[campaignId] === false) {
      return true;
    }

    return this.lockCouponsForCampaign(campaignId);
  }

  async lockCouponsForCampaign(campaignId) {
    if (!this.couponInstancesByCamp[campaignId]) {
      console.warn(`No coupons for campaign ${campaignId}`);
      return false;
    }

    const hasCodes = await asyncFilter(
      this.couponInstancesByCamp[campaignId],
      async (couponInst) => {
        return couponInst.hasCode();
      },
    );

    if (hasCodes.length) {
      this.startLockCouponsForCampaign(campaignId);
    }

    return hasCodes.length;
  }

  startLockCouponsForCampaign(campaignId) {
    this.clearLockForCampaign(campaignId);

    if (this.couponLocksByCamp[campaignId]) {
      this.couponLocksByCamp[campaignId] = {};
    }

    this.couponInstancesByCamp[campaignId].forEach((couponInst) => {
      this.couponLocksByCamp[campaignId] = setInterval(() => {
        couponInst.lock();
      }, 10000);
    });
  }

  clearLockForCampaign(campaignId) {
    if (!this.couponLocksByCamp[campaignId]) {
      return;
    }

    clearInterval(this.couponLocksByCamp[campaignId]);
  }

  /**
   *
   * @param campaignId
   * @param page
   *
   * @return Promise<AbstractCoupon[]|null>
   */
  async showCoupon(campaignId, page) {
    if (!this.couponInstancesByCamp[campaignId]) return null;

    /**
     *
     * @type {AbstractCoupon[]}
     */
    const couponsOnPage = this.couponInstancesByCamp[campaignId]?.filter(
      (couponInst) => couponInst.getPage() === page,
    );

    await Promise.all(
      couponsOnPage.map(async (couponInst) => {
        await couponInst.showCoupon(campaignId, page);
        couponInst.handleAutoRedeem();
        clearInterval(this.couponLocksByCamp[campaignId]);
      }),
    );

    this.logImpression(couponsOnPage);

    return couponsOnPage;
  }

  getCouponsOnPage(campaignId, page) {
    if (!this.couponInstancesByCamp[campaignId]) return null;

    return this.couponInstancesByCamp[campaignId]?.filter(
      (couponInst) => couponInst.getPage() === page,
    );
  }

  /**
   * @param campaignId
   * @param page
   * @return {boolean}
   */
  hasCouponOnPage(campaignId, page) {
    if (!this.couponInstancesByCamp[campaignId]) return false;

    const couponOnPage = this.couponInstancesByCamp[campaignId].filter(
      (couponInst) => couponInst.getPage() === page,
    );

    return !!couponOnPage.length;
  }

  /**
   *
   * @param {AbstractCoupon[]} couponInstances
   */
  logImpression(couponInstances) {
    couponInstances.forEach((couponInst) => {
      if (couponInst.getType() === TYPES.FIXED) {
        return;
      }

      const loggedImpressionKey = `${couponInst.getCampaignId()}-${couponInst.getElementId()}`;
      if (
        this.loggedImpressions[loggedImpressionKey] ||
        couponInst.code === couponInst.getFallback()
      ) {
        return;
      }

      try {
        reportEventJF('couponImpression', {
          code: couponInst.code,
          couponType: couponInst.getType(),
          campaignId: couponInst.getCampaignId(),
          variantId: couponInst.getVariantId(),
          domain: window.location.hostname,
          deviceType: OptiMonkRegistry.isMobile ? 'mobile' : 'desktop',
        });
        window.JFClientSDK.v2.go();
        this.loggedImpressions[loggedImpressionKey] = true;
      } catch (e) {
        console.warn('[OM-Coupon] Unable to log coupon impression:', e.message);
      }
    });
  }

  addCouponCopyListener() {
    const _self = this;
    DOM.addEventListener(DOM.getDocument(), 'click', function couponClick(e) {
      const target = e.target;
      const campaignEle = target.closest('.om-workspace-content');

      if (!campaignEle) return;

      const campaignId = campaignEle.id.replace('om-campaign-', '');
      if (!_self.couponsByCampMap[campaignId]) return;

      const couponWrapper = target.closest('.om-coupon');
      if (!couponWrapper) return;

      const couponInstance = _self.couponInstancesByCamp[campaignId].find(
        (couponInst) => couponWrapper.id === `${couponInst.getElementId()}_wrapper`,
      );

      if (!couponInstance) return;

      couponInstance.copyCouponToClipboard();
    });
  }

  clearSettingsData(campaignId) {
    if (!this.couponInstancesByCamp[campaignId]) return;

    this.couponInstancesByCamp[campaignId].forEach((couponInst) => {
      couponInst.removeSettingsData();
    });
  }
}

const instance = new CouponManager();

export default instance;
