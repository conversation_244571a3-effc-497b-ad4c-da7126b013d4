import { ActionHandler } from './ActionHandler';
import {
  storePreviousViewedPage,
  storeNotViewedPageRecent,
} from '../shared/Validator/helper/StoreViewedPage';
import { createAdapter } from '../shared/Visitor/Adapter';
import { componentHolders } from './ComponentsHolder';
import { CookieManager } from './CookieManager';
import { ReportManager } from './ReportManager';
import { CustomJSRunner } from './CustomJS/CustomJSRunner';
import {
  getAccountId,
  setClientId,
  getClientId,
  setSessionStart,
  triggerEvent,
  waitForElement,
  getRequestService,
  addListener,
  createCampaignsArray,
  filterCampaignMetaDataByAbTestManager,
} from '../shared/helpers';
import { shouldBlockLinuxVisitors } from '../shared/features/helpers';
import { setIsMobile } from '../shared/DeviceHelper';
import { RequestAfterPreInit } from './BackendRequest/RequestAfterPreInit';
import { AbTestManager } from '../shared/AbTestManager';
import { DynamicContentCampaign } from '../dynamic-content/DynamicContentCampaign';
import { ControlVariantCampaign } from '../control-variant/ControlVariantCampaign';
import { Experiments } from '../preload/Util/Experiments';
import JFRuleService from './VisitorData/JFRuleService';
import { callWithTimeout } from '../shared/Utils/callWithTimeout';
import { log } from '../shared/log';
import { initShopAttributes } from '../shared/Attributes/shopAttributes';
import { setupSmartABTest } from './SmartAbTest';
import { setProductSmartTags } from './SmartPPO';
import { setPageSmartTags } from './SmartPageTag';
import SmartPersonalizer from './SmartPersonalizer';
import PreviewService from './PreviewService';
import AnalyticsEventManager from '../shared/Analytics/AnalyticsEventManager';
import { ScraperForSPPO } from './Scraper/SPPO/scraperForSPPO';
import { purifyDomain } from '../shared/Utils/domain';
import * as popup from '../v2/init';
import { setupCountryData, setupIPBlockData } from '../shared/header';
import { PNC } from '../shared/pnc';
import { saveFirstPartyData } from '../preload/firstPartyData';
import { reportEventJF } from '../shared/JFEventReport';
import { Cookie } from '../shared/Cookie';
import { LZString } from '../shared/lz-string';
import { processPreRegisteredReportEvents } from '../shared/ReportManagerProcess';
import { filterCampaignMetaDataByExperienceManager } from './experienceHelper';
import { setSourceAttributes } from '../shared/source';
import { redirectToSplitURLTestVariant } from '../shared/SplitUrlTest';
import { initCrossDomainTracking } from '../shared/crossDomainTracking';

const handleButtonClick = (event) => {
  const targetElement = event.target;

  Object.entries(componentHolders).forEach(([key, value]) => {
    const actionComponent = targetElement.closest(value);
    const v2EmbeddedWrapper = targetElement.closest('.om-embedded-campaign-v2');

    if (!actionComponent || !v2EmbeddedWrapper) return;

    return ActionHandler.handle({
      targetElement,
      actionComponent,
      campaignWrapper: v2EmbeddedWrapper,
      component: key,
      event,
    });
  });
};

const initButtonEventHandler = () => {
  document.querySelector('html').addEventListener('click', handleButtonClick, false);
};

const manualPositionedCampaignReplace = async (id, html, domInstances) => {
  return new Promise((resolve) => {
    import('./EmbeddedCampaignDOMHtml').then(({ EmbeddedCampaignDOMHtml }) => {
      let instanceCount = 0;
      document
        .querySelectorAll(`.om-embedded-campaign[data-campaign-id="${id}"]`)
        .forEach((campaignElement) => {
          const embeddedDOM = new EmbeddedCampaignDOMHtml(id, html, instanceCount);
          embeddedDOM.insertDirectly(campaignElement);
          domInstances.push(embeddedDOM);
          instanceCount++;
        });

      resolve();
    });
  });
};

const hasSiblingMatch = (campaignId, selector) => {
  // fix: SPA multiple push state
  const nextSiblings = document.querySelectorAll(
    `${selector} ~ .om-embedded-campaign[data-campaign-id="${campaignId}"]`,
  );

  // need to check previous sibling also (PNC above position)
  const element = document.querySelector(selector);
  let sibling = element.previousSibling;
  let hasPrevSibling = false;

  while (sibling) {
    if (sibling.nodeType === 1 && sibling.dataset?.campaignId === campaignId.toString()) {
      hasPrevSibling = true;
      break;
    }
    sibling = sibling.previousSibling;
  }

  return nextSiblings.length > 0 || hasPrevSibling;
};

const tryReplaceElements = async (id, html, selector, position, instanceNumbers) => {
  let tryCount = 0;
  const maxTry = 5;
  return new Promise((resolve) => {
    import('./EmbeddedCampaignDOMHtml').then(({ EmbeddedCampaignDOMHtml }) => {
      const intervalId = setInterval(function () {
        if (tryCount < maxTry) {
          let instanceCount = instanceNumbers.length || 0;
          const elements = document.querySelectorAll(selector);
          if (elements?.length) {
            console.log('embedded:debug', 'inserting embedded', selector);
            const insertPosition = position === 'above' ? 'beforebegin' : 'afterend';
            elements.forEach((element) => {
              if (hasSiblingMatch(id, selector)) return;

              const embeddedDOM = new EmbeddedCampaignDOMHtml(id, html, instanceCount);
              embeddedDOM.insertWithWrapper(element, insertPosition);
              instanceNumbers.push(embeddedDOM);
              instanceCount++;
            });
            clearInterval(intervalId);
            resolve();
          } else {
            tryCount++;
          }
        }

        if (maxTry === tryCount) {
          console.log('embedded:debug', 'wait timeout reached no element found', selector);
          clearInterval(intervalId);
          resolve();
        }
      }, 10);
    });
  });
};

const pncPositionedCampaignReplace = (positions, id, html, instanceNumbers) => {
  const { selector, position } = positions[0];

  return tryReplaceElements(id, html, selector, position, instanceNumbers);
};

const createDOMInstances = async ({ id, positions }, html) => {
  const domInstances = [];

  await manualPositionedCampaignReplace(id, html, domInstances);

  if (positions?.length) {
    await pncPositionedCampaignReplace(positions, id, html, domInstances);
  }

  return domInstances;
};

const getAccountInfo = async () => {
  let accInfoHeaders;
  try {
    const { response, headers } = await OptiMonkEmbedded.RequestService.loadAccountInfo();

    setupCountryData(headers);
    setupIPBlockData(headers);

    const domain = purifyDomain(window.location.hostname);

    const experimentsForDomain = response.settings.experiments.filter(
      (experiment) => purifyDomain(experiment.domain) === domain,
    );

    const spaDomains = response.settings.spaDomains || [];
    return {
      features: response.settings.features,
      embeddedCampaigns: response.embeddedCampaigns,
      dynamicContentCampaigns: response.dynamicContentCampaigns || [],
      experiments: experimentsForDomain,
      campaignMetaData: response.embeddedCampaigns,
      brand: response.settings.brand,
      trackParams: response.settings.trackParams || {},
      experimentalSettings: response.settings.experimentalSettings,
      spaDomain: spaDomains.find((spaDomain) => spaDomain === domain),
      poweredBy: response.settings.poweredBy || {},
      crossDomainTrackingRoles: response?.settings?.crossDomainTrackingRoles || {},
      providerServiceIdOverrides: response?.settings?.providerServiceIdOverrides || {},
      splitUrlABTests: response.splitUrlABTests || [],
    };
  } catch (e) {
    console.error(e.message);
    return {
      features: [],
      embeddedCampaigns: [],
      dynamicContentCampaigns: [],
      experiments: {},
      campaignMetaData: [],
      brand: 'OptiMonk',
      trackParams: {},
      experimentalSettings: {},
      spaDomain: null,
      poweredBy: {},
      crossDomainTrackingRoles: {},
      providerServiceIdOverrides: {},
      splitUrlABTests: [],
    };
  }
};

const runScraper = async () => {
  const requestService = OptiMonkEmbedded.RequestService;

  const currentUrl = window.location.href;
  const isMobile = !!OptiMonkRegistry?.isMobile;
  const scraperForSPPO = new ScraperForSPPO({ requestService, isMobile });
  scraperForSPPO.setCurrentUrl(currentUrl); // TODO[SPPO-scraper] SPA handling
  scraperForSPPO.runScraper();
};

const getCampaignsJson = async () => {
  try {
    const { response } = await getRequestService().loadCampaignsJson();
    response.campaigns = response.campaigns.map((campaign) => {
      return { ...campaign, id: campaign.campaignId };
    });
    return response;
  } catch (e) {
    console.error(e.message);
    return { campaigns: [] };
  }
};

const prepareBackendRequest = (requestService, cookieManager) => {
  window.OptiMonkRegistry.beforeLoadCampaigns = {
    script: false,
    popup: false,
  };

  const backendRequest = new RequestAfterPreInit(getClientId(), requestService, OptiMonkEmbedded);
  OptiMonkEmbedded.BackendRequestAfterPreInit = backendRequest.getPublicIF();

  const trySendRequest = (event) => {
    window.OptiMonkRegistry.beforeLoadCampaigns[event.parameters.type] = true;

    const { script, popup } = window.OptiMonkRegistry.beforeLoadCampaigns;
    if (script !== true || popup !== true) return;

    backendRequest.setAccountCookie(cookieManager.getOriginalCookies());
    backendRequest.request();
  };

  addListener(document.querySelector('html'), 'optimonk#campaigns-before-load', trySendRequest);
};

const prepare = async () => {
  setClientId();
  setIsMobile();
  saveFirstPartyData();
  setSourceAttributes();
  runScraper(); // not awaited
  ReportManager.getInstance().resetCampaignStates();
  await processPreRegisteredReportEvents();

  const requestService = OptiMonkEmbedded.RequestService;

  OptiMonkEmbedded.campaigns = [];

  const visitorAdapter = createAdapter();

  visitorAdapter.clearShop();
  initShopAttributes(visitorAdapter);

  const cookieManager =
    window.OptiMonkEmbedded.CookieManager ||
    new CookieManager({
      accountId: getAccountId(),
      miniScriptLoaded: OptiMonkEmbedded.mini,
    });
  OptiMonkEmbedded.CookieManager = cookieManager;

  prepareBackendRequest(requestService, cookieManager);

  const [accountInfo, limitData, popupCampaigns] = await Promise.all([
    getAccountInfo(),
    requestService.getLimitData(),
    getCampaignsJson(),
  ]);

  if (redirectToSplitURLTestVariant(accountInfo.splitUrlABTests)) return;

  OptiMonkRegistry.features = accountInfo.features;
  OptiMonkRegistry.experiments = accountInfo.experiments;
  OptiMonkRegistry.brand = accountInfo.brand;
  OptiMonkRegistry.poweredBy = accountInfo.poweredBy;
  OptiMonkRegistry.experimentalSettings = accountInfo.experimentalSettings;
  OptiMonkRegistry.crossDomainTrackingRoles = accountInfo.crossDomainTrackingRoles;
  OptiMonkRegistry.providerServiceIdOverrides = accountInfo.providerServiceIdOverrides;

  if (shouldBlockLinuxVisitors(OptiMonkRegistry.features)) return;

  Experiments.initSafe();
  JFRuleService.addScript();

  initCrossDomainTracking();

  await Promise.all([setProductSmartTags(), setPageSmartTags()]);

  try {
    popup.init({ limitData, campaignsData: popupCampaigns, cookieManager });
  } catch (e) {
    console.error({ message: 'Error loading popups', e });
  }

  const experimentCookie = Cookie.local.getItem('optiMonkExperiments');

  if ((!experimentCookie || experimentCookie == '[]') && OptiMonkRegistry?.account == 222976) {
    const omCookie = Cookie.local.getItem('optiMonkClient') || '{}';

    reportEventJF('om_experiment', {
      clientId: OptiMonkRegistry?.clientId,
      experiments: JSON.stringify(OptiMonkRegistry?.experiments),
      experimentCookie,
      omCookie: LZString.decompressFromBase64(omCookie),
    });
  }

  if (accountInfo.spaDomain) {
    import('../preload/preload-spa').then(({ initSPA }) => {
      initSPA();
    });
    // SPA code
    window.OptiMonk.isSPA = true;
    if (!window.OMHistoryOverriddenForEmbedded) {
      const SPACloak = () => {
        if (window.OMCreateAntiFlicker) {
          window.OMCreateAntiFlicker();
        }
        window.OptiMonkEmbedded.loadEmbedded();
      };
      window.history.onpushstateEmbedded = SPACloak;
      window.addEventListener('popstate', SPACloak);
      window.OMHistoryOverriddenForEmbedded = true;
      OptiMonkEmbedded.mini = false;
    }
  }

  SmartPersonalizer.getInstance().setSmartPersonalizationParameter(accountInfo);

  let campaignMetaData = [...accountInfo.embeddedCampaigns, ...accountInfo.dynamicContentCampaigns];

  if (PreviewService.isPreview()) {
    campaignMetaData = [];
    const {
      response: { success, campaign },
    } = await requestService.getPreview(PreviewService.getPreviewVariantId());

    if (success) {
      campaignMetaData.push(campaign);
      OptiMonkEmbedded.campaigns = createCampaignsArray(campaignMetaData);
      cookieManager.resetPreviewCampaign(campaignMetaData.id);
      return campaignMetaData;
    }
    return [];
  }

  const htmlElement = document.querySelector('html');
  if (PreviewService.isPopupPreview()) {
    triggerEvent(htmlElement, 'optimonk#campaigns-before-load', { type: 'script' });
    return [];
  }

  if (!limitData.remaining) {
    triggerEvent(htmlElement, 'optimonk#campaigns-before-load', { type: 'script' });
    return [];
  }

  if (!campaignMetaData?.length) {
    triggerEvent(htmlElement, 'optimonk#campaigns-before-load', { type: 'script' });
    return [];
  }

  const { Validators } = await import('../shared/Validator');
  campaignMetaData = await Validators.beforeInit.validate(campaignMetaData, { cookieManager });

  if (!campaignMetaData.length) {
    triggerEvent(htmlElement, 'optimonk#campaigns-before-load', { type: 'script' });
    return;
  }

  OptiMonkEmbedded.campaigns = createCampaignsArray(campaignMetaData);
  cookieManager.addCampaigns(campaignMetaData);
  campaignMetaData.forEach((campaignData) => {
    JFRuleService.addCampaign(campaignData);
  });

  log('Has JF rule', JFRuleService.hasJFRule());

  if (JFRuleService.hasJFRule()) {
    try {
      await callWithTimeout(JFRuleService.collectData, 300);
    } catch (e) {
      log('Cannot load JF data', e.message);
    }
  }

  log('Campaigns before experience validation', campaignMetaData);
  campaignMetaData = await filterCampaignMetaDataByExperienceManager(campaignMetaData);
  log('Campaigns after experience validation', campaignMetaData);
  if (!campaignMetaData.length) {
    OptiMonkEmbedded.campaigns = [];
    triggerEvent(htmlElement, 'optimonk#campaigns-before-load', { type: 'script' });
    return [];
  }

  const abTestManager = new AbTestManager(campaignMetaData, { type: 'embedded' });
  campaignMetaData = filterCampaignMetaDataByAbTestManager(campaignMetaData, abTestManager);

  storePreviousViewedPage(campaignMetaData);
  storeNotViewedPageRecent(campaignMetaData, cookieManager);

  OptiMonkEmbedded.campaigns = createCampaignsArray(campaignMetaData);

  triggerEvent(htmlElement, 'optimonk#embedded-campaigns-init', campaignMetaData);
  triggerEvent(htmlElement, 'optimonk#campaigns-before-load', { type: 'script' });

  return campaignMetaData;
};

const initEmbeddedCampaigns = (campaigns) => {
  let hasInitedCssAndEventHandlers = false;

  if (!campaigns.length) return;

  return (async () => {
    const { EmbeddedCampaign } = await import('./EmbeddedCampaign');
    const { default: CouponManager } = await import('./Coupon/CouponManager');
    const campaignPromises = campaigns.map(async (campaignData) => {
      const campaignId = campaignData.id;
      CustomJSRunner.runPageLoad(campaignData);

      if (campaignData.coupons) {
        CouponManager.add(campaignData.coupons);
      }

      const { Validators } = await import('../shared/Validator');
      if (!(await Validators.embedded.validate(campaignId))) return Promise.resolve(false);

      if (!hasInitedCssAndEventHandlers) {
        OptiMonkEmbedded.AssetManager.loadAsset(
          OptiMonkRegistry.getAssetUrlFor('/assets/css/om.base.css'),
          'css',
          () => {},
        );
        initButtonEventHandler();
        hasInitedCssAndEventHandlers = true;
      }

      return OptiMonkEmbedded.RequestService.loadVariant(
        campaignData.variantId,
        campaignData.ts,
      ).then(async (html) => {
        const locale = navigator.language || navigator.userLanguage;

        // Handle post validation, we need the variant HTML to check
        const isValid = await Validators.embedded.postValidate(campaignId, html);
        if (!isValid) {
          return Promise.resolve(false);
        }

        const domInstances = await createDOMInstances(campaignData, html);
        CouponManager.clearSettingsData(campaignId);

        CustomJSRunner.runShow(campaignData);
        const instancePromises = domInstances.map(async (domInstance) => {
          const campaign = new EmbeddedCampaign({
            campaignData,
            requestService: OptiMonkEmbedded.RequestService,
            assetManager: OptiMonkEmbedded.AssetManager,
            cookieManager: OptiMonkEmbedded.CookieManager,
            couponManager: CouponManager,
            locale,
            instanceNum: domInstance.getInstanceNumber(),
            embeddedCampaignDom: domInstance,
          });
          OptiMonkEmbedded.CampaignRegistry.add(campaign);
          campaign.init(); // not awaited

          OptiMonkEmbedded.CookieManager.addImpression(campaign.getId()); // Slow to read + update cookie, so it will be reported once
          ReportManager.getInstance().reportCampaignShow(campaign); // Manager handles reporting only once

          return Promise.resolve(campaign);
        });

        return Promise.all(instancePromises);
      });
    });

    return Promise.all(campaignPromises).catch(console.error);
  })();
};

const initDynamicContentCampaigns = async (campaigns) => {
  setupSmartABTest(campaigns);

  SmartPersonalizer.getInstance().setup(campaigns);

  const dcInserts = campaigns.map(async (campaign) => {
    const { Validators } = await import('../shared/Validator');
    if (!(await Validators.embedded.validate(campaign.id))) return;

    await SmartPersonalizer.getInstance().setDTRs(campaign);

    const dcCampaign = new DynamicContentCampaign(campaign);
    const results = await dcCampaign.applyChanges();
    const hasAppliedChange = (await Promise.all(results.map(async (r) => r))).find(Boolean);
    if (hasAppliedChange) {
      OptiMonkEmbedded.CookieManager.addImpression(dcCampaign.getId());
      ReportManager.getInstance().reportCampaignShow(dcCampaign);
    }
  });

  await Promise.all(dcInserts);

  if (window.OMCustomAntiFlicker) {
    window.OMCustomAntiFlicker.show();
  }
};

const initControlVariantCampaigns = async (campaigns) => {
  const campaignPromises = campaigns.map(async (controlVariant) => {
    const { Validators } = await import('../shared/Validator');
    if (!(await Validators.embedded.validate(controlVariant.id))) return;

    const controlVariantCampaign = new ControlVariantCampaign(controlVariant);
    OptiMonkEmbedded.CookieManager.addImpression(controlVariantCampaign.getId());
    ReportManager.getInstance().reportCampaignShow(controlVariantCampaign);
  });

  await Promise.all(campaignPromises);
};

const initCampaigns = async (campaignsData) => {
  const dynamicContentCampaigns = [];
  const embeddedCampaigns = [];
  const controlVariantCampaigns = [];
  campaignsData.forEach((campaignData) => {
    if (campaignData.isControlVariant) {
      controlVariantCampaigns.push(campaignData);
    } else if (campaignData.type === 'dynamic_content') {
      dynamicContentCampaigns.push(campaignData);
    } else if (campaignData.type === 'embedded') {
      embeddedCampaigns.push(campaignData);
    }
  });

  if (OptiMonkRegistry.isDebug) {
    log('Campaigns to load', {
      dynamicContentCampaigns,
      embeddedCampaigns,
      controlVariantCampaigns,
    });
  }

  initDynamicContentCampaigns(dynamicContentCampaigns);
  initEmbeddedCampaigns(embeddedCampaigns);
  initControlVariantCampaigns(controlVariantCampaigns);
};

function start(response) {
  if (PNC.isWebSelectorEnabled()) return;
  setSessionStart();
  waitForElement('body', () => {
    AnalyticsEventManager.init();
    initCampaigns(response);
  });
}

export { prepare, start };
