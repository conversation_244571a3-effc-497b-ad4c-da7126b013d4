import { Engine } from '../../preload/Engine/Engine';
import { _get } from '../Utils/object';
import { ShopifyAdapter } from '../Platform/ShopifyAdapter';
import { getAccountId } from '../helpers';
import { purifyDomain } from '../Utils/domain';
import { applyProviderServiceIdOverride } from '../Utils/providerServiceIdOverride';

function getProviderData() {
  const siteInfo = Engine.getInfo();

  let provider = siteInfo.type;
  let providerServiceId = purifyDomain(siteInfo.shopId || window.location.host);

  const accountId = getAccountId();

  if (accountId === 58417) {
    providerServiceId = 'blendjet.myshopify.com';
    provider = 'shopify';
  } else if (accountId === 88202) {
    providerServiceId = 'rugby-is-life.myshopify.com';
    provider = 'shopify';
  }

  // Apply provider service ID override if configured
  providerServiceId = applyProviderServiceIdOverride(providerServiceId);

  return { provider, providerServiceId };
}

function getCustomerId() {
  let customerId;

  if (ShopifyAdapter.isShop()) {
    customerId = ShopifyAdapter.getCustomerId();
  } else if (window.ShopRenter) {
    customerId = _get(window.ShopRenter, 'customer.userId');
  }
  return customerId ? `${customerId}` : undefined;
}

function getDeviceType() {
  const deviceType = 'mobile';
  const regex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  if (regex.test(navigator.userAgent)) {
    return deviceType;
  }

  if (window.navigator.userAgentData && window.navigator.userAgentData.mobile) {
    return deviceType;
  }

  if (window.matchMedia('(max-width: 767px)').matches) {
    return deviceType;
  }
  return 'desktop';
}

function getCanonicalUrl() {
  return window.document?.querySelector?.('link[rel="canonical"]')?.getAttribute('href') ?? null;
}

export { getProviderData, getCustomerId, getDeviceType, getCanonicalUrl };
