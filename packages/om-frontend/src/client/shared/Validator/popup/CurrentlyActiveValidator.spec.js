const { CurrentlyActiveValidator } = require('./CurrentlyActiveValidator');
const { setupRegistry, extendOptiMonk } = require('../../../test/utils');

setupRegistry();
extendOptiMonk();

describe('CurrentlyActiveValidator', () => {
  test('valid if there is no other active campaigns of same type', () => {
    OptiMonk.campaigns = {};
    const result = CurrentlyActiveValidator.validate({ getFrontendType: () => 'popup' });
    expect(result).toBe(true);
  });

  test('valid if there is other active campaign of different type', () => {
    OptiMonk.campaigns = { 1: { getFrontendType: () => 'sidemessage' } };
    OptiMonk.ActivatedCampaignManager.activateCampaign(1);
    const result = CurrentlyActiveValidator.validate({ getFrontendType: () => 'popup' });
    expect(result).toBe(true);
  });

  test('valid if there is other active campaign of same type with visible teaser', () => {
    OptiMonk.campaigns = { 1: { getFrontendType: () => 'popup', isTeaserShowing: () => true } };
    OptiMonk.ActivatedCampaignManager.activateCampaign(1);
    const result = CurrentlyActiveValidator.validate({ getFrontendType: () => 'popup' });
    expect(result).toBe(true);
  });

  test('valid if this campaign is active', () => {
    OptiMonk.campaigns = { 1: { getFrontendType: () => 'popup' } };
    OptiMonk.ActivatedCampaignManager.activateCampaign(1);
    const result = CurrentlyActiveValidator.validate({
      getFrontendType: () => 'popup',
      getId: () => 1,
    });
    expect(result).toBe(true);
  });

  test('invalid if there is other active campaign of same type with hidden teaser', () => {
    OptiMonk.campaigns = { 1: { getFrontendType: () => 'popup', isTeaserShowing: () => false } };
    OptiMonk.ActivatedCampaignManager.activateCampaign(1);
    const result = CurrentlyActiveValidator.validate({
      getFrontendType: () => 'popup',
      getId: () => 2,
    });
    expect(result).toBe(false);
  });
});
