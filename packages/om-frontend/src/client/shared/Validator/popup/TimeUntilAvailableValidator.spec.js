const { setupOptiMonk, setupRegistry } = require('../../../test/utils');

setupRegistry();
setupOptiMonk();

const { TimeUntilAvailableValidator } = require('./TimeUntilAvailableValidator');

describe('TimeUntilAvailableValidator', () => {
  test('always valid if there is no tua campaign cookie', () => {
    const result = TimeUntilAvailableValidator.validate({
      cookie: {
        getTimeUntilAvailable: () => null,
      },
    });
    expect(result).toBe(true);
  });
  test('valid if tua is in the past', () => {
    // minus 1 day
    const pastDate = (new Date().getTime() - 24 * 60 * 60 * 1000) / 1000;
    const result = TimeUntilAvailableValidator.validate({
      cookie: {
        getTimeUntilAvailable: () => pastDate,
      },
    });
    expect(result).toBe(true);
  });

  test('invalid if tua is in the future', () => {
    // plus 1 day
    const pastDate = (new Date().getTime() + 24 * 60 * 60 * 1000) / 1000;
    const result = TimeUntilAvailableValidator.validate({
      cookie: {
        getTimeUntilAvailable: () => pastDate,
      },
    });
    expect(result).toBe(false);
  });
});
