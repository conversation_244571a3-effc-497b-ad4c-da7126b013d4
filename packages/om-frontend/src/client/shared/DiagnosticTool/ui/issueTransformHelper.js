import { OTHER_CAMPAIGN_DISPLAYED_ISSUE, FOLLOWUP_COUPON_ISSUE } from './constants';

export const mergeOtherCampaignDisplayedIssues = (issues) => {
  let mergedIssues = JSON.parse(JSON.stringify(issues));

  const types = OTHER_CAMPAIGN_DISPLAYED_ISSUE.validatorTypes;
  const filteredIssues = mergedIssues.find((issue) => types.includes(issue.validatorType));
  if (filteredIssues) {
    mergedIssues = mergedIssues.filter((issue) => !types.includes(issue.validatorType));
    mergedIssues.push({
      stage: 'validations',
      validatorType: OTHER_CAMPAIGN_DISPLAYED_ISSUE.mergedType,
    });
  }

  return mergedIssues;
};

export const mergeFollowUpCouponIssues = (issues) => {
  let mergedIssues = JSON.parse(JSON.stringify(issues));

  const followUpIssue = mergedIssues.find(
    (issue) => issue.validatorType === FOLLOWUP_COUPON_ISSUE.validatorType,
  );
  if (followUpIssue) {
    mergedIssues = mergedIssues.filter(
      (issue) => !FOLLOWUP_COUPON_ISSUE.typesToRemove.includes(issue.validatorType),
    );
  }

  return mergedIssues;
};
