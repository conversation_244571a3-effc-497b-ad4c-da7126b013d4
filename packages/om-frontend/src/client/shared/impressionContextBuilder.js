import { getCurrentExperiments } from '../preload/Util/Experiments';
import { SiteInfo } from '../preload/SiteInfo/SiteInfo';
import { <PERSON><PERSON> } from './Cookie';
import { getDeviceType, getCanonicalUrl } from './JF/helpers';

export const prepareImpressionContext = () => {
  const visitorInExperiment = getCurrentExperiments();
  const deviceType = getDeviceType();
  SiteInfo.initialize();
  const siteInfo = SiteInfo.collect(true);
  const ctx = {
    accountId: OptiMonkRegistry.account,
    deviceId: OptiMonkRegistry.clientId,
    siteType: siteInfo.platform,
    siteId: siteInfo.shopId || siteInfo.hostname,
    shopifyY: Cookie.local.getItem('_shopify_y'),
    userAgent: navigator.userAgent,
    url: location.href,
    canonicalUrl: getCanonicalUrl(),
    visitorInExperiment,
    deviceType,
  };

  return window.btoa(unescape(encodeURIComponent(JSON.stringify(ctx))));
};
