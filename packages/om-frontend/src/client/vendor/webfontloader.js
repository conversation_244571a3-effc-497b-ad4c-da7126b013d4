/* Web Font Loader v1.6.28 - (c) Adobe Systems, Google. License: Apache 2.0 */
export function OMWebfont(t){!function(){function n(t,n,i){return t.call.apply(t.bind,arguments)}function i(t,n,i){if(!t)throw Error();if(2<arguments.length){var e=Array.prototype.slice.call(arguments,2);return function(){var i=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(i,e),t.apply(n,i)}}return function(){return t.apply(n,arguments)}}function e(t,o,a){return(e=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?n:i).apply(null,arguments)}var o=Date.now||function(){return+new Date};function a(t,n){this.a=t,this.o=n||t,this.c=this.o.document}var s=!!t.FontFace;function r(t,n,i,e){if(n=t.c.createElement(n),i)for(var o in i)i.hasOwnProperty(o)&&("style"==o?n.style.cssText=i[o]:n.setAttribute(o,i[o]));return e&&n.appendChild(t.c.createTextNode(e)),n}function f(t,n,i){(t=t.c.getElementsByTagName(n)[0])||(t=document.documentElement),t.insertBefore(i,t.lastChild)}function h(t){t.parentNode&&t.parentNode.removeChild(t)}function c(t,n,i){n=n||[],i=i||[];for(var e=t.className.split(/\s+/),o=0;o<n.length;o+=1){for(var a=!1,s=0;s<e.length;s+=1)if(n[o]===e[s]){a=!0;break}a||e.push(n[o])}for(o=0,n=[];o<e.length;o+=1){for(s=0,a=!1;s<i.length;s+=1)if(e[o]===i[s]){a=!0;break}a||n.push(e[o])}t.className=n.join(" ").replace(/\s+/g," ").replace(/^\s+|\s+$/,"")}function l(t,n){for(var i=t.className.split(/\s+/),e=0,o=i.length;e<o;e++)if(i[e]==n)return!0;return!1}function u(t,n,i){function e(){c&&o&&a&&(c(h),c=null)}n=r(t,"link",{rel:"stylesheet",href:n,media:"all"});var o=!1,a=!0,h=null,c=i||null;s?(n.onload=function(){o=!0,e()},n.onerror=function(){o=!0,h=Error("Stylesheet failed to load"),e()}):setTimeout(function(){o=!0,e()},0),f(t,"head",n)}function p(t,n,i,e){var o=t.c.getElementsByTagName("head")[0];if(o){var a=r(t,"script",{src:n}),s=!1;return a.onload=a.onreadystatechange=function(){s||this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(s=!0,i&&i(null),a.onload=a.onreadystatechange=null,"HEAD"==a.parentNode.tagName&&o.removeChild(a))},o.appendChild(a),setTimeout(function(){s||(s=!0,i&&i(Error("Script load timeout")))},e||5e3),a}return null}function g(){this.a=0,this.c=null}function $(t){return t.a++,function(){t.a--,v(t)}}function d(t,n){t.c=n,v(t)}function v(t){0==t.a&&t.c&&(t.c(),t.c=null)}function m(t){this.a=t||"-"}function w(t,n){this.c=t,this.f=4,this.a="n";var i=(n||"n4").match(/^([nio])([1-9])$/i);i&&(this.a=i[1],this.f=parseInt(i[2],10))}function _(t){var n=[];t=t.split(/,\s*/);for(var i=0;i<t.length;i++){var e=t[i].replace(/['"]/g,"");-1!=e.indexOf(" ")||/^\d/.test(e)?n.push("'"+e+"'"):n.push(e)}return n.join(",")}function y(t){return t.a+t.f}function b(t){var n="normal";return"o"===t.a?n="oblique":"i"===t.a&&(n="italic"),n}function x(t){var n=4,i="n",e=null;return t&&((e=t.match(/(normal|oblique|italic)/i))&&e[1]&&(i=e[1].substr(0,1).toLowerCase()),(e=t.match(/([1-9]00|normal|bold)/i))&&e[1]&&(/bold/i.test(e[1])?n=7:/[1-9]00/.test(e[1])&&(n=parseInt(e[1].substr(0,1),10)))),i+n}function j(t,n){this.c=t,this.f=t.o.document.documentElement,this.h=n,this.a=new m("-"),this.j=!1!==n.events,this.g=!1!==n.classes}function k(t){if(t.g){var n=l(t.f,t.a.c("wf","active")),i=[],e=[t.a.c("wf","loading")];n||i.push(t.a.c("wf","inactive")),c(t.f,i,e)}S(t,"inactive")}function S(t,n,i){t.j&&t.h[n]&&(i?t.h[n](i.c,y(i)):t.h[n]())}function T(){this.c={}}function C(t,n){this.c=t,this.f=n,this.a=r(this.c,"span",{"aria-hidden":"true"},this.f)}function N(t){f(t.c,"body",t.a)}function E(t){return"display:block;position:absolute;top:-9999px;left:-9999px;font-size:300px;width:auto;height:auto;line-height:normal;margin:0;padding:0;font-variant:normal;white-space:nowrap;font-family:"+_(t.c)+";"+("font-style:"+b(t)+";font-weight:"+t.f)+"00;"}function W(t,n,i,e,o,a){this.g=t,this.j=n,this.a=e,this.c=i,this.f=o||3e3,this.h=a||void 0}function A(t,n,i,e,o,a,s){this.v=t,this.B=n,this.c=i,this.a=e,this.s=s||"BESbswy",this.f={},this.w=o||3e3,this.u=a||null,this.m=this.j=this.h=this.g=null,this.g=new C(this.c,this.s),this.h=new C(this.c,this.s),this.j=new C(this.c,this.s),this.m=new C(this.c,this.s),t=E(t=new w(this.a.c+",serif",y(this.a))),this.g.a.style.cssText=t,t=E(t=new w(this.a.c+",sans-serif",y(this.a))),this.h.a.style.cssText=t,t=E(t=new w("serif",y(this.a))),this.j.a.style.cssText=t,t=E(t=new w("sans-serif",y(this.a))),this.m.a.style.cssText=t,N(this.g),N(this.h),N(this.j),N(this.m)}m.prototype.c=function(t){for(var n=[],i=0;i<arguments.length;i++)n.push(arguments[i].replace(/[\W_]+/g,"").toLowerCase());return n.join(this.a)},W.prototype.start=function(){var t=this.c.o.document,n=this,i=o(),e=new Promise(function(e,a){!function s(){var r;o()-i>=n.f?a():t.fonts.load(b(r=n.a)+" "+r.f+"00 300px "+_(r.c),n.h).then(function(t){1<=t.length?e():setTimeout(s,25)},function(){a()})}()}),a=null;Promise.race([new Promise(function(t,i){a=setTimeout(i,n.f)}),e]).then(function(){a&&(clearTimeout(a),a=null),n.g(n.a)},function(){n.j(n.a)})};var O={D:"serif",C:"sans-serif"},F=null;function B(){if(null===F){var t=/AppleWebKit\/([0-9]+)(?:\.([0-9]+))/.exec(window.navigator.userAgent);F=!!t&&(536>parseInt(t[1],10)||536===parseInt(t[1],10)&&11>=parseInt(t[2],10))}return F}function L(t,n,i){for(var e in O)if(O.hasOwnProperty(e)&&n===t.f[O[e]]&&i===t.f[O[e]])return!0;return!1}function P(t,n){setTimeout(e(function(){h(this.g.a),h(this.h.a),h(this.j.a),h(this.m.a),n(this.a)},t),0)}function M(t,n,i){this.c=t,this.a=n,this.f=0,this.m=this.j=!1,this.s=i}A.prototype.start=function(){this.f.serif=this.j.a.offsetWidth,this.f["sans-serif"]=this.m.a.offsetWidth,this.A=o(),function t(n){var i,a,s=n.g.a.offsetWidth,r=n.h.a.offsetWidth;(i=s===n.f.serif&&r===n.f["sans-serif"])||(i=B()&&L(n,s,r)),i?o()-n.A>=n.w?B()&&L(n,s,r)&&(null===n.u||n.u.hasOwnProperty(n.a.c))?P(n,n.v):P(n,n.B):(a=n,setTimeout(e(function(){t(this)},a),50)):P(n,n.v)}(this)};var q=null;function D(t){0==--t.f&&t.j&&(t.m?((t=t.a).g&&c(t.f,[t.a.c("wf","active")],[t.a.c("wf","loading"),t.a.c("wf","inactive")]),S(t,"active")):k(t.a))}function H(t){this.j=t,this.a=new T,this.h=0,this.f=this.g=!0}function I(n,i,o,a,s){var r=0==--n.h;(n.f||n.g)&&setTimeout(function(){var n=s||null,f=a||{};if(0===o.length&&r)k(i.a);else{i.f+=o.length,r&&(i.j=r);var h,l=[];for(h=0;h<o.length;h++){var u=o[h],p=f[u.c],g=i.a,$=u;if(g.g&&c(g.f,[g.a.c("wf",$.c,y($).toString(),"loading")]),S(g,"fontloading",$),g=null,null===q){if(t.FontFace){var $=/Gecko.*Firefox\/(\d+)/.exec(window.navigator.userAgent),d=/OS X.*Version\/10\..*Safari/.exec(window.navigator.userAgent)&&/Apple/.exec(window.navigator.vendor);q=$?42<parseInt($[1],10):!d}else q=!1}g=q?new W(e(i.g,i),e(i.h,i),i.c,u,i.s,p):new A(e(i.g,i),e(i.h,i),i.c,u,i.s,n,p),l.push(g)}for(h=0;h<l.length;h++)l[h].start()}},0)}function z(t,n){this.c=t,this.a=n}function G(t,n){this.c=t,this.a=n}function K(t,n){t?this.c=t:this.c=U,this.a=[],this.f=[],this.g=n||""}M.prototype.g=function(t){var n=this.a;n.g&&c(n.f,[n.a.c("wf",t.c,y(t).toString(),"active")],[n.a.c("wf",t.c,y(t).toString(),"loading"),n.a.c("wf",t.c,y(t).toString(),"inactive")]),S(n,"fontactive",t),this.m=!0,D(this)},M.prototype.h=function(t){var n=this.a;if(n.g){var i=l(n.f,n.a.c("wf",t.c,y(t).toString(),"active")),e=[],o=[n.a.c("wf",t.c,y(t).toString(),"loading")];i||e.push(n.a.c("wf",t.c,y(t).toString(),"inactive")),c(n.f,e,o)}S(n,"fontinactive",t),D(this)},H.prototype.load=function(t){this.c=new a(this.j,t.context||this.j),this.g=!1!==t.events,this.f=!1!==t.classes,function t(n,i,e){var o,a=[],s=e.timeout;(o=i).g&&c(o.f,[o.a.c("wf","loading")]),S(o,"loading");var a=function t(n,i,e){var o,a=[];for(o in i)if(i.hasOwnProperty(o)){var s=n.c[o];s&&a.push(s(i[o],e))}return a}(n.a,e,n.c),r=new M(n.c,i,s);for(n.h=a.length,i=0,e=a.length;i<e;i++)a[i].load(function(t,i,e){I(n,r,t,i,e)})}(this,new j(this.c,t),t)},z.prototype.load=function(t){var n=this,i=n.a.projectId,e=n.a.version;if(i){var o=n.c.o;p(this.c,(n.a.api||"https://fast.fonts.net/jsapi")+"/"+i+".js"+(e?"?v="+e:""),function(e){e?t([]):(o["__MonotypeConfiguration__"+i]=function(){return n.a},function n(){if(o["__mti_fntLst"+i]){var e,a=o["__mti_fntLst"+i](),s=[];if(a)for(var r=0;r<a.length;r++){var f=a[r].fontfamily;void 0!=a[r].fontStyle&&void 0!=a[r].fontWeight?(e=a[r].fontStyle+a[r].fontWeight,s.push(new w(f,e))):s.push(new w(f))}t(s)}else setTimeout(function(){n()},50)}())}).id="__MonotypeAPIScript__"+i}else t([])},G.prototype.load=function(t){var n,i,e=this.a.urls||[],o=this.a.families||[],a=this.a.testStrings||{},s=new g;for(n=0,i=e.length;n<i;n++)u(this.c,e[n],$(s));var r=[];for(n=0,i=o.length;n<i;n++)if((e=o[n].split(":"))[1])for(var f=e[1].split(","),h=0;h<f.length;h+=1)r.push(new w(e[0],f[h]));else r.push(new w(e[0]));d(s,function(){t(r,a)})};var U=OptiMonkRegistry.features.BUNNY_FONTS?"https://fonts.bunny.net/css":"https://fonts.googleapis.com/css";function V(t){this.f=t,this.a=[],this.c={}}var X={latin:"BESbswy","latin-ext":"\xe7\xf6\xfcğş",cyrillic:"йяЖ",greek:"αβΣ",khmer:"កខគ",Hanuman:"កខគ"},Y={thin:"1",extralight:"2","extra-light":"2",ultralight:"2","ultra-light":"2",light:"3",regular:"4",book:"4",medium:"5","semi-bold":"6",semibold:"6","demi-bold":"6",demibold:"6",bold:"7","extra-bold":"8",extrabold:"8","ultra-bold":"8",ultrabold:"8",black:"9",heavy:"9",l:"3",r:"4",b:"7"},J={i:"i",italic:"i",n:"n",normal:"n"},Q=/^(thin|(?:(?:extra|ultra)-?)?light|regular|book|medium|(?:(?:semi|demi|extra|ultra)-?)?bold|black|heavy|l|r|b|[1-9]00)?(n|i|normal|italic)?$/;function R(t,n){this.c=t,this.a=n}var Z={Arimo:!0,Cousine:!0,Tinos:!0};function tt(t,n){this.c=t,this.a=n}function tn(t,n){this.c=t,this.f=n,this.a=[]}R.prototype.load=function(t){var n=new g,i=this.c,e=new K(this.a.api,this.a.text),o=this.a.families;!function t(n,i){for(var e=i.length,o=0;o<e;o++){var a=i[o].split(":");3==a.length&&n.f.push(a.pop());var s="";2==a.length&&""!=a[1]&&(s=":"),n.a.push(a.join(s))}}(e,o);var a=new V(o);(function t(n){for(var i=n.f.length,e=0;e<i;e++){var o=n.f[e].split(":"),a=o[0].replace(/\+/g," "),s=["n4"];if(2<=o.length){var r,f,h=o[1];if(r=[],h)for(var h=h.split(","),c=h.length,l=0;l<c;l++){if((f=h[l]).match(/^[\w-]+$/)){var u=Q.exec(f.toLowerCase());if(null==u)f="";else{if(f=null==(f=u[2])||""==f?"n":J[f],null==(u=u[1])||""==u)u="4";else var p=Y[u],u=p||(isNaN(u)?"4":u.substr(0,1));f=[f,u].join("")}}else f="";f&&r.push(f)}0<r.length&&(s=r),3==o.length&&(o=o[2],r=[],0<(o=o?o.split(","):r).length&&(o=X[o[0]])&&(n.c[a]=o))}for(n.c[a]||(o=X[a])&&(n.c[a]=o),o=0;o<s.length;o+=1)n.a.push(new w(a,s[o]))}})(a),u(i,function t(n){if(0==n.a.length)throw Error("No fonts to load!");if(-1!=n.c.indexOf("kit="))return n.c;for(var i=n.a.length,e=[],o=0;o<i;o++)e.push(n.a[o].replace(/ /g,"+"));return i=n.c+"?family="+e.join("%7C"),0<n.f.length&&(i+="&subset="+n.f.join(",")),0<n.g.length&&(i+="&text="+encodeURIComponent(n.g)),i}(e),$(n)),d(n,function(){t(a.a,a.c,Z)})},tt.prototype.load=function(t){var n=this.a.id,i=this.c.o;n?p(this.c,(this.a.api||"https://use.typekit.net")+"/"+n+".js",function(n){if(n)t([]);else if(i.Typekit&&i.Typekit.config&&i.Typekit.config.fn){n=i.Typekit.config.fn;for(var e=[],o=0;o<n.length;o+=2)for(var a=n[o],s=n[o+1],r=0;r<s.length;r++)e.push(new w(a,s[r]));try{i.Typekit.load({events:!1,classes:!1,async:!0})}catch(f){}t(e)}},2e3):t([])},tn.prototype.load=function(t){var n,i=this.f.id,e=this.c.o,o=this;i?(e.__webfontfontdeckmodule__||(e.__webfontfontdeckmodule__={}),e.__webfontfontdeckmodule__[i]=function(n,i){for(var e=0,a=i.fonts.length;e<a;++e){var s=i.fonts[e];o.a.push(new w(s.name,x("font-weight:"+s.weight+";font-style:"+s.style)))}t(o.a)},p(this.c,(this.f.api||"https://f.fontdeck.com/s/css/js/")+((n=this.c).o.location.hostname||n.a.location.hostname)+"/"+i+".js",function(n){n&&t([])})):t([])};var ti=new H(t);ti.a.c.custom=function(t,n){return new G(n,t)},ti.a.c.fontdeck=function(t,n){return new tn(n,t)},ti.a.c.monotype=function(t,n){return new z(n,t)},ti.a.c.typekit=function(t,n){return new tt(n,t)},ti.a.c.google=function(t,n){return new R(n,t)};var te={load:e(ti.load,ti)};t.WebFont=te,t.WebFontConfig&&ti.load(t.WebFontConfig)}()}
