import { CampaignCookie } from '../Cookie/CampaignCookie';
import { EmbeddedCampaignDisplay } from '../DisplayHandler/EmbeddedCampaignDisplay';
import { EmbeddedSubInstance } from './Embedded/EmbeddedSubInstance';
import { Validators } from '../../shared/Validator/index';
import { CampaignProgressState } from '../../shared/CampaignProgressState';
import {
  EMBEDDED_CAMPAIGN,
  EVENT_EMBEDDED_SHOW,
  FRONTEND_EMBEDDED,
} from '../../shared/Campaign/campaignConstans';
import { OptiMonk } from '../../OptiMonk';
import { Replacer } from '../../shared/DTR/Replacer';
import { TYPE_SHOP } from '../../shared/DTR/Components/consts';
import { updatePopUpLastSeen } from '../../preload/firstPartyData';
import { getExperimentGroupId, getCurrentExperiments } from '../../preload/Util/Experiments';
import { campaignCookieSchema } from '../../shared/Cookie/helpers';
import { getCanonicalUrl, getDeviceType } from '../../shared/JF/helpers';
import { getVisitorAdapter } from '../../shared/helpers';

const Registry = window.OptiMonkRegistry;

export class EmbeddedCampaign {
  constructor() {
    this.activated = false;
    this.analytics = { type: '', status: 0 };
    this.effect = '';
    this.campaignName = '';
    this.displayGroup = { position: null, type: null };
    this.frontendType = FRONTEND_EMBEDDED;
    this.events = {};
    this.insertHtml = '';
    this.sortOrder = 0;
    this.rules = {
      previouslyViewedPage: {},
      campaignProgressState: [],
      maximumPopupDisplay: {
        delay: '',
        value: '',
      },
      timeBasedActualPage: 0,
      timeBasedSession: '',
    };
    this.template = '';
    this.cookie = new CampaignCookie(0, {});
    this.showable = true;
    this.initiatedEvents = [];
    this.type = EMBEDDED_CAMPAIGN;
    this.eventListeners = {};
    this.initialized = false;
    this.creativeId = 0;
    this.creativeName = '';
    this.creativeUri = '';
    this.creativeUpdateTimestamp = '';
    this.instances = [];
    this.coupons = [];
    this.data = {
      pageUserId: '',
      converted: false,
    };
    this.products = [];
    this.converted = false;
    this.lastFilledInstance = null;
    this.initiatedCouponListeners = false;
    this.showReported = false;
    this.currentExperimentId = null;
    this.countdowns = [];
    this.visitorAdapter = getVisitorAdapter().createAdapter();
  }

  setFromObject(object, campaignCookie) {
    this._id = object._id;
    this.campaignId = object.campaignId;
    this.campaignName = object.campaignName;
    this.analytics = object.analytics;
    this.displayGroup = object.displayGroup;
    this.template = object.template;
    this.insertHtml = object.insertHtml;
    this.sortOrder = object.sortOrder;
    this.rules = object.rules;
    this.setEvents(object.events);
    this.effect = object.effect;
    this.creativeId = object.creativeId;
    this.creativeUri = object.creativeUri;
    this.creativeName = object.creativeName;
    this.creativeUpdateTimestamp = object.creativeUpdateTimestamp;
    this.cookie =
      campaignCookie ||
      new CampaignCookie(object.campaignId, campaignCookieSchema(object.creativeId));
    this.DisplayHandler = EmbeddedCampaignDisplay;
    this.currentExperimentId = object.currentExperimentId;
    if (object.frontendType === 'invalid') {
      OptiMonk.Logger.error('Incorrect frontend type', { template: this.template });
    }
  }

  setEvents(events) {
    this.events = events;
    this.events[OptiMonk.Event.TYPES.load] = {};
  }

  init() {
    const self = this;
    OptiMonk.addListener(
      document.querySelector('html'),
      'optimonk#campaign-page-show',
      function (e) {
        if (e.parameters.campaignId === self.getId()) {
          const elementsOnPage = self.getCurrentStep().querySelectorAll('.om-element');
          for (let i = 0; i < elementsOnPage.length; i += 1) {
            const element = elementsOnPage[i];
            Replacer.replace(element);
          }
        }
      },
    );
    const xhr = new OptiMonk.native.XMLHttpRequest();
    this.data.pageUserId = this.getPageUserId();

    xhr.onreadystatechange = function () {
      if (this.readyState === 4 && this.status === 200) {
        self.on('campaign-fill', (param) => {
          self.lastFilledInstance = param.instance;
          self.setFilled();
        });

        self.on('has-coupon', () => {
          if (self.initiatedCouponListeners) {
            return;
          }
          self.initiatedCouponListeners = true;
          OptiMonk.addListener(document.querySelector('html'), EVENT_EMBEDDED_SHOW, function () {
            if (self.coupons[1]) {
              self.coupons[1].showCoupon();
            }
            self.showStep(1);
          });
          self.on('get-coupon', (params) => {
            self.coupons.forEach((coupon) => {
              coupon.emit('get-coupon', params);
            });
          });
          self.on('coupon-code', (param) => {
            self.instances.forEach((instance) => {
              instance.emit('show-coupon-code', param);
            });
          });
        });
        self.on('filled', () => {
          self.markFilled();
        });
        self.on('send-feedback', (param) => {
          self.sendFeedback(param);
          self.storeFeedbackData(param);
        });
        /* self.on('close', () => {
                          self.closeAllSub()
                        }); */
        self.on('social-interaction', (param) => {
          self.reportSocialInteraction(param.method);
        });
        self.on('instance-initialized', () => {
          let allInit = true;
          self.instances.forEach((inst) => {
            allInit = allInit && inst.initialized;
          });
          self.initialized = allInit;
          OptiMonk.triggerEvent(
            document.querySelector('html'),
            'optimonk#embedded-campaign-after_initialized',
          );
          if (allInit) {
            self.callCustomJs(self.getId());
          }
        });
        document
          .querySelectorAll(`.om-embedded-campaign[data-campaign-id="${self.getId()}"]`)
          .forEach((inst, instanceNum) => {
            const response = xhr.responseText;
            const element = inst;
            element.setAttribute('data-instance', instanceNum.toString());
            element.setAttribute('id', `embedded-${self.getId()}-${instanceNum.toString()}`);
            element.style.display = 'none';
            const holder = OptiMonk.appendEmbedded(self, 'om');
            const workspaceContent = holder.querySelector(`#om-campaign-${self.getId()}`);
            element.appendChild(holder);
            workspaceContent.innerHTML = response;
            const subInstance = new EmbeddedSubInstance(instanceNum, self, EmbeddedCampaignDisplay);
            self.instances.push(subInstance);
            subInstance.init();
            if (instanceNum === 0) {
              workspaceContent.querySelectorAll('script').forEach((script) => {
                script.parentNode.replaceChild(OptiMonk.Util.nodeScriptClone(script), script);
              });
            }
            self.countdowns = subInstance.countdowns;
          });
      }
    };
    xhr.open('GET', this.getCreativeUrl(), true);
    xhr.send();
  }

  setFilled() {
    const campaignId = this.getId();
    const caEl = document.querySelector('html');
    OptiMonk.triggerEvent(caEl, 'optimonk#campaign-before_mark_filled', {
      campaignId,
    });
    this.cookie.setFilled();
    OptiMonk.triggerEvent(caEl, 'optimonk#campaign-after_mark_filled', {
      campaignId,
    });
    this.setLastFilledCookie();
  }

  markFilled() {
    this.report('filled');
  }

  isFilled() {
    return this.cookie.isFilled();
  }

  setLastFilledCookie() {
    const FILLED_COOKIE_EXPIRE_DAYS = 14;
    const date = new Date();
    date.setTime(date.getTime() + FILLED_COOKIE_EXPIRE_DAYS * 24 * 60 * 60 * 1000);
    const expires = `; expires=${date.toUTCString()}`;
    const value = JSON.stringify({
      ts: new Date().getTime(),
      creativeId: this.getCreativeId(),
    });
    document.cookie = `omLastFilled=${value}${expires}; path=/`;
  }

  getEmailInput() {
    return this.instances[this.lastFilledInstance].getEmailInput();
  }

  getEmailInputOf(instanceNumber) {
    return this.instances[instanceNumber].getEmailInput();
  }

  sendFeedback(feedback) {
    if (!feedback.length) return;
    const xhr = new OptiMonk.native.XMLHttpRequest();
    xhr.open('POST', this.getFeedbackUrl(), true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.send(`feedback=${encodeURIComponent(JSON.stringify(feedback))}`);
  }

  getCreativeUrl() {
    return this.creativeUri;
  }

  emit(name, params = {}) {
    const listeners = this.eventListeners[name] || [];
    listeners.forEach((func) => {
      func(params);
    });
  }

  callCustomJs() {
    const fn = window[`OMCustomJS_${this.getId()}`];
    if (fn) {
      OptiMonk.loadScript('/vendor/jquery.min-1.11.3.js', () => {
        fn(OptiMonk, OptiMonk.$, this);
      });
    }
  }

  getPrimaryId() {
    return this._id;
  }

  getId() {
    return this.campaignId;
  }

  getType() {
    return this.type;
  }

  getSortOrder() {
    return this.sortOrder;
  }

  getFrontendType() {
    return this.frontendType;
  }

  isInitialized() {
    return this.initialized;
  }

  getName() {
    return this.campaignName;
  }

  setShowable(value) {
    this.showable = value;
  }

  getCreativeName() {
    return this.creativeName;
  }

  getAnalytics() {
    return this.analytics;
  }

  getCreativeId() {
    return this.creativeId;
  }

  getVariantId() {
    return this.creativeId;
  }

  hasCampaignProgressStateRule() {
    const rule = this.getRule('campaignProgressState');
    if (!rule) return false;
    return !rule.hasOwnProperty('length');
  }

  hasCampaignViewedPageRule() {
    const rule = this.getRule('viewedPage');
    if (!rule) return false;
    return !rule.hasOwnProperty('length');
  }

  hasCampaignSourceRule() {
    const rule = this.getRule('source');
    if (!rule) return false;
    return !rule.hasOwnProperty('length');
  }

  hasCampaignSubscriberRule() {
    const rule = this.getRule('subscribers');
    if (!rule) return false;
    return !rule.hasOwnProperty('length');
  }

  hasVisitorAttributeRules() {
    const rule = this.getRule('visitorAttribute');
    if (!rule) return false;
    return !rule.hasOwnProperty('length');
  }

  getRule(name) {
    return this.rules[name] || null;
  }

  getCampaignContainerSelector() {
    return `.om-campaign-${this.getId()}`;
  }

  isValid() {
    return Validators.embeddedOld.validate(this, { event: 'load' });
  }

  displayOnEvent() {
    const parameters = {
      campaignId: this.getId(),
      event: 'load',
      version: 2,
    };
    this.displayTrigger(parameters);
    const campaignCookie = this.cookie;
    if (campaignCookie.getState() === CampaignProgressState.STATE_INIT) {
      campaignCookie.setShowed();
    }
    this.setActivated();
    this.instances.forEach((inst, index) => {
      this.DisplayHandler.displayCampaignSub(this.getId(), index);
    });
  }

  setActivated() {
    this.activated = true;
  }

  getBoxContainerSelector() {
    return `.om-embedded-campaign[data-campaign-id="${this.getId()}"] .om-canvas`;
  }

  getCurrentStep() {
    if (!this.currentStep) {
      this.currentStep = document.querySelector(`${this.getBoxContainerSelector()}.actual`);
    }
    return this.currentStep;
  }

  displayTrigger(parameters) {
    OptiMonk.triggerEvent(document.querySelector('html'), EVENT_EMBEDDED_SHOW, parameters);
    updatePopUpLastSeen();
    const campaignContainerEl = document.querySelector(this.getCampaignContainerSelector());
    if (!campaignContainerEl) return;
    OptiMonk.triggerEvent(campaignContainerEl, EVENT_EMBEDDED_SHOW, parameters);
  }

  getStepContainer(instance, pageNumber) {
    const index = this.instances.indexOf(instance);
    const currentInst = this.instances[index];
    return currentInst.getStepContainer(pageNumber);
  }

  showStep(pageNum) {
    this.instances.forEach((instance) => {
      instance.showStep(pageNum);
    });
    this.reportEvent('optimonk#embedded-campaign-page-show', {
      type: `Page: ${pageNum}`,
      pageNum,
    });
  }

  closeAllSub() {
    this.instances.forEach((instance) => {
      instance.hide();
    });
  }

  reportEvent(event, parameters) {
    parameters = parameters || {};
    parameters.event = event;
    this.report('event', parameters);
  }

  report(type, parameters) {
    parameters = parameters || {};
    parameters.campaignId = this.getId();
    const message = {
      type,
      parameters,
    };
    const handler = OptiMonk.MessageHandler.get(type);
    handler.handle(message);
  }

  reportSocialInteraction(method) {
    const data = this.getBaseConversionData();
    data.final = true;
    this.convert(data, function () {});
    this.report('analyticsReport', { type: `social_${method}` });
  }

  getFeedbackUrl() {
    return `${Registry.baseUrl}/public/${
      Registry.account
    }/creative/${this.getCreativeId()}/feedback`;
  }

  getBaseConversionData() {
    OptiMonk.SiteInfo.initialize();
    const experimentGroupId = getExperimentGroupId(this.currentExperimentId);
    const visitorInExperiment = getCurrentExperiments();
    const deviceType = getDeviceType();
    const siteInfo = JSON.stringify(OptiMonk.SiteInfo.collect(true));
    return {
      final: false,
      needSetConverted: false,
      converted: false,
      'visitor[url]': encodeURIComponent(window.location.href),
      'visitor[canonicalUrl]': encodeURIComponent(getCanonicalUrl()),
      deviceType,
      creative: this.creativeId,
      clientId: Registry.clientId,
      siteInfo,
      experimentGroupId,
      visitorInExperiment,
      userAgent: navigator.userAgent,
    };
  }

  on(name, func) {
    if (!this.eventListeners[name]) {
      this.eventListeners[name] = [];
    }
    this.eventListeners[name].push(func);
  }

  getPageUserId() {
    return `${Registry.uuid}-${this.creativeId}`;
  }

  convert(data, callback, onSent) {
    const self = this;
    let needSetConverted = true;
    Object.assign(this.data, this.getBaseConversionData());
    if (typeof data.needSetConverted !== 'undefined' && !data.needSetConverted)
      needSetConverted = false;
    delete data.needSetConverted;
    this.data = OptiMonk.Util.assign(this.data, data);
    return new Promise((resolve) => {
      this.sendData(
        function () {
          if (callback) {
            callback();
          } else {
            self.markFilled();
          }
          resolve();
        },
        needSetConverted,
        onSent,
      );
      this.converted = true;
    });
  }

  addCoupon({ coupon, page }) {
    this.coupons[page] = coupon;
  }

  lockCoupons() {
    let result = true;
    this.coupons.forEach(function (coupon) {
      const response = coupon.lockCoupon();
      result = result && response;
    });
    return result;
  }

  addProducts(products) {
    this.products = products;
  }

  getProducts() {
    return this.products;
  }

  storeFeedbackData(feedback) {
    feedback.forEach((input) => {
      const { inputId, value } = input;
      this.visitorAdapter.attr(inputId, value);
    });
  }

  storeFormData() {
    Object.entries(this.data).forEach(([key, value]) => {
      if (
        key.includes('custom_fields') ||
        key === 'visitor[email]' ||
        key === 'visitor[firstname]' ||
        key === 'visitor[lastname]'
      ) {
        const inputId = key.match(/\[([^\]\[]*)\]/)[1];
        this.visitorAdapter.attr(inputId, value);
      }
    });
  }

  sendData(cb, needSetConverted, onSent) {
    this.storeFormData();
    const xhr = new OptiMonk.native.XMLHttpRequest();
    xhr.open('POST', this.getConversionUrl(), true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 2 && onSent) {
        // HEADERS_RECEIVED
        onSent();
      } else if (xhr.readyState === 4) {
        // DONE
        cb();
      }
    };
    if (needSetConverted) this.data.converted = this.converted;
    xhr.send(OptiMonk.Util.serializeObject(this.data));
  }

  getConversionUrl() {
    return `${Registry.baseUrl}/public/${
      Registry.account
    }/creative/${this.getCreativeId()}/conversionExtended`;
  }

  close() {}

  async isValidOnEvent(event) {
    return Validators.embeddedOld.validate(this.campaignId, { event });
  }

  showInstances() {
    this.instances.forEach((instance) => {
      const campaignElement = instance.getCampaignElement();
      campaignElement.style.display = '';
    });
  }

  isInline() {
    return true;
  }

  hasCoupon() {
    for (let index = 0, instLeng = this.instances.length; index < instLeng; index += 1) {
      const inst = this.instances[index];
      if (inst.hasCoupon()) {
        return true;
      }
    }
    return false;
  }

  hasShopifyAutoCoupon() {
    for (let index = 0, instLeng = this.instances.length; index < instLeng; index += 1) {
      const inst = this.instances[index];
      if (inst.hasShopifyAutoCoupon()) {
        return true;
      }
    }
    return false;
  }

  getAppearance() {
    return this.cookie.getAppearance();
  }

  getCookie() {
    return this.cookie;
  }

  isProductsConnectedToShop() {
    for (let index = 0, instLeng = this.instances.length; index < instLeng; index += 1) {
      const inst = this.instances[index];
      if (inst.isProductsConnectedToShop()) {
        return true;
      }
    }
    return false;
  }

  isShowReported() {
    return this.showReported;
  }

  setShowReported() {
    this.showReported = true;
  }

  getFollowupCoupons() {
    return this.instances.map((instance) => instance.getFollowupCoupons()).flat();
  }

  queueForDisplay(done) {
    window.OptiMonkEmbedded.AssetManager.loadAsset(
      OptiMonkRegistry.getAssetUrlFor('/assets/css/om.base.css'),
      'css',
      done,
    );
  }

  hasShopDTR() {
    const elements = document
      .querySelector(this.getBoxContainerSelector())
      .querySelectorAll('.om-element');
    for (let i = 0; i < elements.length; i += 1) {
      const element = elements[i];
      if (Replacer.hasContentToReplace(element.innerHTML, TYPE_SHOP)) {
        return true;
      }
    }
    return false;
  }
}
