import { DynamicContentCampaign } from './DynamicContentCampaign';
import { DCValidators } from './Validators';
import { setupSmartABTest } from '../embedded/SmartAbTest';
import SmartPersonalizer from '../embedded/SmartPersonalizer';
import PreviewService from '../embedded/PreviewService';
import { CookieManager } from '../embedded/CookieManager';
import {
  createCampaignsArray,
  filterCampaignMetaDataByAbTestManager,
  getAccountId,
  setClientId,
} from '../shared/helpers';
import { shouldBlockLinuxVisitors } from '../shared/features/helpers';
import { Visitor } from '../shared/Visitor';
import { reportCampaignShow } from '../shared/ReportManagerProcess';
import { setIsMobile } from '../shared/DeviceHelper';
import { saveFirstPartyData } from '../preload/firstPartyData';
import { createAdapter } from '../shared/Visitor/Adapter';
import {
  storeNotViewedPageRecent,
  storePreviousViewedPage,
} from '../shared/Validator/helper/StoreViewedPage';
import { ControlVariantCampaign } from '../control-variant/ControlVariantCampaign';
import { AbTestManager } from '../shared/AbTestManager';
import { purifyDomain } from '../shared/Utils/domain';
import { Experiments } from '../preload/Util/Experiments';
import JFRuleService from '../embedded/VisitorData/JFRuleService';
import { setProductSmartTags } from '../embedded/SmartPPO';
import { setPageSmartTags } from '../embedded/SmartPageTag';
import { callWithTimeout } from '../shared/Utils/callWithTimeout';
import { log } from '../shared/log';
import { filterCampaignMetaDataByExperienceManager } from '../embedded/experienceHelper';
import { setupCountryData, setupIPBlockData } from '../shared/header';
import { initShopAttributes } from '../shared/Attributes/shopAttributes';
import { PNC } from '../shared/pnc';
import { setSourceAttributes } from '../shared/source';
import { redirectToSplitURLTestVariant } from '../shared/SplitUrlTest';
import { initCrossDomainTracking } from '../shared/crossDomainTracking';

const setFromHeader = (header) => {
  try {
    header = JSON.parse(header);
  } catch (e) {
    log('[OM] Error: Cannot set header values', header);
  }
  setupCountryData(header);
  setupIPBlockData(header);
};

const insertEmbeddedJSCodeAndShowPage = () => {
  const script = document.createElement('script');
  script.type = 'module';
  script.src = getEsmFile('embedded');
  document.querySelector('head').appendChild(script);

  if (window.OMCustomAntiFlicker) {
    window.OMCustomAntiFlicker.show();
  }
};

const unifyAccountInfo = (accountInfo) => {
  const domain = purifyDomain(window.location.hostname);
  const spaDomains = accountInfo.settings.spaDomains || [];
  return {
    embeddedCampaigns: accountInfo.embeddedCampaigns,
    dynamicContentCampaigns: accountInfo.dynamicContentCampaigns || [],
    splitUrlABTests: accountInfo.splitUrlABTests || [],
    campaignMetaData: accountInfo.embeddedCampaigns,
    features: accountInfo.settings.features,
    experiments: accountInfo.settings.experiments.filter(
      (experiment) => purifyDomain(experiment.domain) === domain,
    ),
    brand: accountInfo.settings.brand,
    trackParams: accountInfo.settings.trackParams || {},
    experimentalSettings: accountInfo.settings.experimentalSettings,
    spaDomain: spaDomains.find((spaDomain) => spaDomain === domain),
    poweredBy: accountInfo.settings.poweredBy || {},
    crossDomainTrackingRoles: accountInfo?.settings?.crossDomainTrackingRoles || {},
    providerServiceIdOverrides: accountInfo?.settings?.providerServiceIdOverrides || {},
  };
};

const setChangesOpacity = (dcCampaign, value) => {
  dcCampaign.changes.forEach((change) => {
    const querySelector = document.querySelector(change.selector);
    if (!querySelector) return;
    querySelector.style.opacity = value;
  });
};

const initDynamicContentCampaigns = async (campaigns) => {
  setupSmartABTest(campaigns);
  SmartPersonalizer.getInstance().setup(campaigns);

  const dcInserts = campaigns.map(async (campaign) => {
    setChangesOpacity(campaign, 0);
    logTime('hide changes before validation');
    if (!(await DCValidators.dynamicContent.validate(campaign.id))) {
      return setChangesOpacity(campaign, 1);
    }

    await SmartPersonalizer.getInstance().setDTRs(campaign);
    const dcCampaign = new DynamicContentCampaign(campaign);
    const results = await dcCampaign.applyChanges();
    logTime('Changes applied');
    setChangesOpacity(campaign, 1);
    const hasAppliedChange = (await Promise.all(results.map(async (r) => r))).find(Boolean);
    if (hasAppliedChange) {
      window.OptiMonkEmbedded.CookieManager.addImpression(dcCampaign.getId());
      reportCampaignShow(dcCampaign);
    }
  });

  try {
    await Promise.all(dcInserts);
  } catch (e) {
    console.log(e);
  }
  logTime('all dc handled, insert embedded code');

  insertEmbeddedJSCodeAndShowPage();
};

const initControlVariantCampaigns = async (campaigns) => {
  const campaignPromises = campaigns.map(async (controlVariant) => {
    if (!(await DCValidators.dynamicContent.validate(controlVariant.id))) return;

    const controlVariantCampaign = new ControlVariantCampaign(controlVariant);
    window.OptiMonkEmbedded.CookieManager.addImpression(controlVariantCampaign.getId());
    reportCampaignShow(controlVariantCampaign);
  });

  await Promise.all(campaignPromises);
};

const prepareCampaigns = async (accountInfo, cookieManager) => {
  if (redirectToSplitURLTestVariant(accountInfo.splitUrlABTests)) return;

  OptiMonkRegistry.features = accountInfo.features;
  OptiMonkRegistry.experiments = accountInfo.experiments;
  OptiMonkRegistry.brand = accountInfo.brand;
  OptiMonkRegistry.poweredBy = accountInfo.poweredBy;
  OptiMonkRegistry.experimentalSettings = accountInfo.experimentalSettings;
  OptiMonkRegistry.crossDomainTrackingRoles = accountInfo.crossDomainTrackingRoles;
  OptiMonkRegistry.providerServiceIdOverrides = accountInfo.providerServiceIdOverrides;

  if (shouldBlockLinuxVisitors(OptiMonkRegistry.features)) return;

  setClientId();
  setIsMobile();
  saveFirstPartyData();
  setSourceAttributes();
  setFromHeader(window.OMHeaderData);

  const visitorAdapter = createAdapter();

  visitorAdapter.clearShop();
  initShopAttributes(visitorAdapter);

  if (PreviewService.isPreview() || PNC.isWebSelectorEnabled() || !hasRemaining) {
    insertEmbeddedJSCodeAndShowPage();
    return;
  }

  Experiments.initSafe();
  JFRuleService.addScript();

  initCrossDomainTracking();

  await Promise.all([setProductSmartTags(), setPageSmartTags()]);

  let campaignMetaData = [...accountInfo.embeddedCampaigns, ...accountInfo.dynamicContentCampaigns];
  campaignMetaData = await DCValidators.beforeInit.validate(campaignMetaData, { cookieManager });

  campaignMetaData.forEach((campaignData) => {
    JFRuleService.addCampaign(campaignData);
  });

  log('Has JF rule', JFRuleService.hasJFRule());

  if (JFRuleService.hasJFRule()) {
    try {
      await callWithTimeout(JFRuleService.collectData, 300);
    } catch (e) {
      log('Cannot load JF data', e.message);
    }
  }

  log('Campaigns before experience validation', campaignMetaData);
  campaignMetaData = await filterCampaignMetaDataByExperienceManager(campaignMetaData);
  log('Campaigns after experience validation', campaignMetaData);

  const abTestManager = new AbTestManager(campaignMetaData, { type: 'embedded' });
  campaignMetaData = filterCampaignMetaDataByAbTestManager(campaignMetaData, abTestManager);

  storePreviousViewedPage(campaignMetaData);
  storeNotViewedPageRecent(campaignMetaData, cookieManager);

  SmartPersonalizer.getInstance().setSmartPersonalizationParameter(accountInfo);

  window.OptiMonkEmbedded.campaigns = createCampaignsArray(campaignMetaData);
  cookieManager.addCampaigns(campaignMetaData);

  const dynamicContentCampaigns = [];
  const embeddedCampaigns = [];
  const controlVariantCampaigns = [];
  campaignMetaData.forEach((campaignData) => {
    if (campaignData.isControlVariant) {
      controlVariantCampaigns.push(campaignData);
    } else if (campaignData.type === 'dynamic_content') {
      dynamicContentCampaigns.push(campaignData);
    } else if (campaignData.type === 'embedded') {
      embeddedCampaigns.push(campaignData);
    }
  });

  logTime('variables setup done, campaigns sorted');
  initDynamicContentCampaigns(dynamicContentCampaigns);
  initControlVariantCampaigns(controlVariantCampaigns);
};

const cookieManager = new CookieManager({ accountId: getAccountId() });
window.OptiMonkEmbedded.CookieManager = cookieManager;
window.OptiMonkEmbedded.Visitor = Visitor;
window.OptiMonkEmbedded.mini = true;

const accountInfo = unifyAccountInfo(aInfo);

PreviewService.setup();
prepareCampaigns(accountInfo, cookieManager);
