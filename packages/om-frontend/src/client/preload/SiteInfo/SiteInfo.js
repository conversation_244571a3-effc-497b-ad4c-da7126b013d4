import { Engine } from '../Engine/Engine';
import { getKlaviyoInfo } from './KlaviyoDetector';
import { getRequestService } from '../../shared/helpers';
import { ajax } from '../../shared/ajax';
import { applyProviderServiceIdOverride } from '../../shared/Utils/providerServiceIdOverride';

let _inited = false;

export const SiteInfo = {
  initialize() {
    if (!_inited) {
      Engine.initialize();
      _inited = true;
    }
  },
  collect(useProviderServiceIdOverride = false) {
    const engine = Engine.getInfo();

    let shopId = engine.shopId;

    // Apply provider service ID override if requested
    if (useProviderServiceIdOverride) {
      shopId = applyProviderServiceIdOverride(shopId);
    }

    return {
      account: OptiMonkRegistry.account,
      platform: engine.type,
      shopId,
      hostname: location.hostname,
    };
  },
  async getSiteStatus() {
    const request = await getRequestService().getSiteStatus();
    return request;
  },
  getKlaviyoInfo,
  sendIsKlaviyoDetected() {
    const payload = getKlaviyoInfo();
    ajax.post(`data=${JSON.stringify(payload)}`, `${OptiMonkRegistry.baseUrl}/analytics/klaviyo`);
  },
  send() {
    if (!_inited) throw new Error('[SiteInfo] Not initialized');
    const payload = SiteInfo.collect();
    ajax.post(`data=${JSON.stringify(payload)}`, `${OptiMonkRegistry.baseUrl}/analytics/siteinfo`);
  },
  async checkSiteStatus() {
    const result = await SiteInfo.getSiteStatus();

    if (!result || result.error) {
      console.error('[OM] Site status check failed');
      return;
    }

    if (result.site) {
      setTimeout(function () {
        SiteInfo.initialize();
        SiteInfo.send();
      }, 5000);
    }

    if (result.klaviyo) {
      SiteInfo.checkKlaviyo();
    }
  },
  checkKlaviyo() {
    // check klaviyo cookie 10 times with 1 sec delay
    let counter = 10;
    let isKlaviyoDetected;
    setTimeout(function () {
      const interval = setInterval(function () {
        isKlaviyoDetected = SiteInfo.getKlaviyoInfo().isKlaviyoDetected;
        counter--;
        if (isKlaviyoDetected || counter < 1) {
          SiteInfo.sendIsKlaviyoDetected();
          clearInterval(interval);
        }
      }, 1000);
    }, 5000);
  },
};
