import { <PERSON><PERSON> } from '../../shared/Cookie';

const EXPERIMENTS_COOKIE_PREFIX = 'optiMonkExperiments';

const toArguments = function () {
  // eslint-disable-next-line no-undef
  return arguments;
};

export const Experiments = {
  init() {
    if (!OptiMonkRegistry.experimentsInitialized) {
      let cookie = Experiments.readCookie();

      if (cookie) {
        cookie = this.cleanupOldExperimentsFromCookie(cookie);
      }

      if (OptiMonkRegistry.experiments && OptiMonkRegistry.experiments.length) {
        this.setVisitorGroups(cookie);
        this.runJsSnippets();
      }

      this.reportToGA();
      OptiMonkRegistry.experimentsInitialized = true;
    }
  },

  initSafe() {
    try {
      this.init();
    } catch (e) {
      console.error('[frontend] Experiments error', e);
    }
  },

  sendToGA({ experimentName, groupName }) {
    window.dataLayer = window.dataLayer || [];

    // this code sends event directly to ga4
    // gtag function does the same, this way we do not need to wait and retry for gtag
    // https://developers.google.com/tag-platform/devguides/datalayer
    window.dataLayer.push(
      toArguments('event', 'visitor_in_ab_test', {
        om_ab_test_name: experimentName,
        om_ab_test_group: groupName,
      }),
    );

    window.dataLayer.push({
      om_ab_test_name: experimentName,
      om_ab_test_group: groupName,
      event: 'visitor_in_ab_test',
    });
  },

  reportToGA() {
    const cookie = Experiments.readCookie();
    if (cookie && cookie.length) {
      cookie.forEach((experimentCookie) => {
        const parts = experimentCookie.split('_');
        const experimentId = parts[1];

        const experiment = OptiMonkRegistry.experiments.find(
          (experiment) => experiment._id === experimentId,
        );

        if (experiment) {
          const groupId = parts[3];
          const groupName = experiment.groups.find(
            (group) => group._id.toString() === groupId.toString(),
          )?.name;

          if (groupName) {
            this.sendToGA({ experimentName: experiment.name, groupName });
          }
        }
      });
    }
  },

  readCookie() {
    const experimentCookie = Cookie.local.getItem(EXPERIMENTS_COOKIE_PREFIX);
    if (experimentCookie) {
      return JSON.parse(experimentCookie);
    }
    return null;
  },

  cleanupOldExperimentsFromCookie(cookie) {
    const purgedCookie = JSON.parse(JSON.stringify(cookie));
    cookie.forEach((experimentGroup, index) => {
      const experimentId = experimentGroup.split('_')[1];
      const isInExperiments = OptiMonkRegistry.experiments.some(
        (experiment) => experiment._id === experimentId,
      );
      if (!isInExperiments) {
        purgedCookie.splice(index, 1);
      }
    });
    Cookie.local.setItem(EXPERIMENTS_COOKIE_PREFIX, purgedCookie);
    return purgedCookie;
  },

  inVisitorGroupPerExperiment(cookie, experiment) {
    return cookie?.some((visitorGroup) => visitorGroup.startsWith(`experiment_${experiment._id}`));
  },

  sortToGroupByTrafficShare(experiment) {
    const randomNumber = Math.floor(Math.random() * 1000 * experiment.groups.length) + 1;

    let sum = 0;
    for (const group of experiment.groups) {
      sum += group.trafficShare * 1000;
      if (randomNumber <= sum) {
        return `experiment_${experiment._id}_group_${group._id.toString()}`;
      }
    }
  },

  runJsSnippet(visitorExperiment, snippet) {
    try {
      // eslint-disable-next-line no-new-func
      const wrapped = new Function(snippet);
      wrapped();
      console.log('[frontend] JS snippet run for experiment:', visitorExperiment);
    } catch (err) {
      console.error('[frontend] Experiment JS snippet error.', visitorExperiment, err);
    }
  },
  runJsSnippets() {
    const visitorExperiments = Experiments.readCookie();
    visitorExperiments?.forEach((visitorExperiment) => {
      const visitorExperimentParts = visitorExperiment.split('_');
      const visitorExperimentId = visitorExperimentParts[1];
      const visitorGroupId = visitorExperimentParts[3];
      const experiment = OptiMonkRegistry.experiments?.find((e) => e._id === visitorExperimentId);
      const group = experiment?.groups?.find((e) => e._id === visitorGroupId);
      if (group?.jsSnippet) {
        this.runJsSnippet(visitorExperiment, group.jsSnippet);
      }
    });
  },
  setVisitorGroups(cookie) {
    const cookieContent = [];

    OptiMonkRegistry.experiments.forEach((experiment) => {
      if (!this.inVisitorGroupPerExperiment(cookie, experiment)) {
        cookieContent.push(this.sortToGroupByTrafficShare(experiment));
      }
    });
    if (cookieContent.length) {
      Cookie.local.setItem(EXPERIMENTS_COOKIE_PREFIX, [...(cookie || []), ...cookieContent]);
    }
  },
};

export const getExperimentGroupId = (currentExperimentId) => {
  if (currentExperimentId) {
    const cookie = Experiments.readCookie();
    return cookie.find((experiment) => experiment.includes(currentExperimentId))?.split('_')[3];
  }

  return null;
};

export const getCurrentExperiments = () => {
  const cookie = Experiments.readCookie();

  const currentExperiments = [];

  if (cookie && cookie.length) {
    cookie.forEach((experimentCookie) => {
      const parts = experimentCookie.split('_');

      currentExperiments.push({
        experimentId: parts[1],
        groupId: parts[3],
      });
    });
  }

  return JSON.stringify(currentExperiments);
};
