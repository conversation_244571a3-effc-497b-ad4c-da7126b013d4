import axios from 'axios';
import { DiscountType, IDiscountExpiration } from '../types/interfaces';
import { pino } from './logger';

const logger = pino.child({ helper: 'shopifyAdapter' });

const { SHOPIFY_API_VERSION } = process.env;

export const ShopifyAdapter = (shopName: string, token: string) => {
  const getBaseUrl = (shopName: string) => `https://${shopName}/admin/api/${SHOPIFY_API_VERSION}`;

  const client = axios.create({
    baseURL: `${getBaseUrl(shopName)}`,
    headers: { 'X-Shopify-Access-Token': token },
  });

  const getPriceRule = ({ title, type, value, generatedAt, validUntil }) => {
    const priceRule = {
      price_rule: {
        title,
        target_type: 'line_item',
        target_selection: 'all',
        allocation_method: 'across',
        value_type: type === 'percentage' ? 'percentage' : 'fixed_amount',
        value: `-${value}`,
        customer_selection: 'all',
        starts_at: generatedAt,
        usage_limit: 1,
        ...(validUntil && {
          ends_at: validUntil,
        }),
      },
    };

    return priceRule;
  };

  const getValidUntil = (expiration: IDiscountExpiration) => {
    if (expiration) {
      if (expiration.type === 'absolute') {
        if (Date.now() > expiration.value * 1000) {
          throw Error('Discount codes cannot be created with expiry date in the past.');
        }
        return new Date(expiration.value * 1000).toISOString();
      }
      return new Date(Date.now() + 1000 * expiration.value).toISOString();
    }
    return null;
  };

  return {
    createPriceRule: async (
      title: string,
      value: number,
      expiration: IDiscountExpiration,
      type: DiscountType,
    ): Promise<{ priceRuleID: string; generatedAt: string; validUntil: string | null }> => {
      const generatedAt = new Date().toISOString();
      const validUntil = expiration ? getValidUntil(expiration) : null;
      const priceRule = getPriceRule({ title, value, type, generatedAt, validUntil });

      const { data } = await client.post('price_rules.json', priceRule);
      return { priceRuleID: data.price_rule.id, generatedAt, validUntil };
    },
    createCouponCode: async (code: string, priceRuleID: string) => {
      const { data } = await client.post(`price_rules/${priceRuleID}/discount_codes.json`, {
        discount_code: {
          code,
        },
      });

      return data.discount_code.id;
    },
    getResourceTitle: async (resource: string, id: number) => {
      const { data: attributes } = await client.post(`graphql.json`, {
        query: `query {
            ${resource}
            (id: "gid://shopify/${resource.charAt(0).toUpperCase()}${resource.slice(1)}/${id}") {
              title
            }
          }`,
      });

      return attributes.data[resource].title;
    },

    runQuery: async (query) => {
      const { data } = await client.post(`graphql.json`, {
        query,
      });

      if (data?.errors || !data?.data) {
        logger.warn('RunQuery response from Shopify is empty or error occurred', {
          query,
          errors: data?.errors,
          baseURL: `${getBaseUrl(shopName)}`,
        });
      }

      return data.data;
    },
  };
};
