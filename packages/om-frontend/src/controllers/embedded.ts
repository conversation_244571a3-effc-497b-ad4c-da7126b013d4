import { Request } from 'express';
import { CampaignManager } from '../services/CampaignManager';
import {
  updateV3LastRequestDate,
  getRunningExperiments,
  getTrackParams,
  getAccountSettings,
  getLogin,
} from '../helpers/mongoHelper';
import { LZString } from '../client/shared/lz-string';
import { LimitManager } from '../services/LimitManager';
import { isCustomBot } from '../helpers/robotHelper';
import { loadFeatureFlags } from '../helpers/cacheHelper';
import { pino as log } from '../helpers/logger';
import { getWhiteLabelBrand } from '../helpers/misc';
import ScraperForSPPO from '../services/ScraperForSPPO';
import { getAccountSPADomains } from '../helpers/isAccountDomainSPA';
import { omSalesLanding } from '../config';
import { getExperimentalSettings } from '../helpers/mongodb/experimental';
import { getActiveSplitURLABTests } from '../helpers/mongodb/splitURLABTest';

const isInProduction = process.env.ENVIRONMENT !== 'dev';
const shouldCacheResponse = isInProduction;
const CACHE_MAX_AGE = 1800;

const getCacheDuration = () => {
  if (shouldCacheResponse) {
    return `public, max-age=${CACHE_MAX_AGE}`;
  }
  return `no-cache`;
};

const getPoweredByData = async (databaseId) => {
  const accountSettings = await getAccountSettings(databaseId);

  if (!accountSettings) {
    log.warn({ message: 'No account settings found', databaseId });
    return {
      visible: false,
      linkBaseUrl: '',
    };
  }

  const { locale } = await getLogin(accountSettings.ownerLoginId);
  return {
    visible: accountSettings.isPoweredByEnabled,
    linkBaseUrl: omSalesLanding[locale],
  };
};

const generateAccountInfo = async (databaseId) => {
  const features = await loadFeatureFlags(databaseId);
  const embeddedCampaigns = await CampaignManager.loadEmbedded(databaseId, Object.keys(features));
  const dynamicContentCampaigns = await CampaignManager.loadDynamicContent(
    databaseId,
    Object.keys(features),
  );

  const [brand, trackParams, experimentalSettings, accountSettings] = await Promise.all([
    getWhiteLabelBrand(databaseId),
    getTrackParams(databaseId),
    getExperimentalSettings(databaseId),
    getAccountSettings(databaseId),
  ]);

  const settings = {
    features,
    experiments: await getRunningExperiments({ accountId: databaseId }),
    poweredBy: await getPoweredByData(databaseId),
    brand,
    trackParams,
    experimentalSettings,
    spaDomains: await getAccountSPADomains(databaseId),
    crossDomainTrackingRoles: accountSettings?.crossDomainTrackingRoles,
    providerServiceIdOverrides: accountSettings?.providerServiceIdOverrides,
  };

  const splitUrlABTests = await getActiveSplitURLABTests(databaseId);

  return {
    embeddedCampaigns: embeddedCampaigns.map((campaign) => ({ ...campaign, type: 'embedded' })),
    dynamicContentCampaigns: dynamicContentCampaigns.map((campaign) => ({
      ...campaign,
      type: 'dynamic_content',
    })),
    settings,
    splitUrlABTests,
  };
};

// For local accountInfo.json request handling
const handleAccountInfo = async (req: Request, res: any) => {
  res.setHeader('Cache-Control', 'no-cache');

  res.setHeader(process.env.COUNTRY_HEADER_NAME, 'HU');
  res.setHeader(process.env.IP_HEADER_NAME, '127.0.0.1');
  res.setHeader(
    'Access-Control-Expose-Headers',
    `${process.env.COUNTRY_HEADER_NAME}, ${process.env.IP_HEADER_NAME}`,
  );

  const databaseId = parseInt(req.params.omId, 10);
  const accountInfo = await generateAccountInfo(databaseId);

  return res.json(accountInfo);
};

const handleVariant = async (req: Request, res: any) => {
  res.setHeader('Cache-Control', getCacheDuration());
  res.type('text/html');

  const databaseId = parseInt(req.params.omId, 10);
  const variantId = req.params.variantId;
  const embeddedCampaigns = await CampaignManager.loadVariantHtml(databaseId, variantId);

  return res.send(embeddedCampaigns);
};

const afterPreInit = async (req: Request, res: any) => {
  res.setHeader('Cache-Control', 'no-cache');
  res.type('text/html');
  const logger = log.child({ service: 'after-pre-init' });

  if (!req.body.data) {
    logger.warn({
      message: 'No data in request',
      body: req.body,
      params: req.params,
      userAgent: req.useragent,
    });
    return res.sendStatus(400);
  }

  const result = {
    isBot: false,
    hasLimitReached: false,
    hasUsageCountersUpdated: false,
    hasV3DomainLastRequestDateUpdated: false,
    isNewVisitor: false,
  };

  const accountId = parseInt(req.params.omId, 10);
  const data = req.body.data;
  const decoded = LZString.decompressFromBase64(data) || '';
  if (!decoded) {
    log.error({
      message: 'Cannot decode cookie',
      databaseId: accountId,
      data,
    });

    return result;
  }
  const parsedData = JSON.parse(decoded);
  const ip = (req.headers['x-real-ip'] as string) || (req.connection.remoteAddress as string);

  const isBot = (req.useragent && req.useragent.isBot) || isCustomBot(req.useragent);
  result.isBot = isBot;
  const limitManager = new LimitManager(accountId, parsedData.accountCookie, parsedData.clientId);

  if (parsedData.increaseUsageCounters && isBot === false) {
    const { isNewVisitor } = await limitManager.increaseCounters({
      userAgent: req.useragent,
      referer: req.headers.referer,
    });
    result.isNewVisitor = isNewVisitor;
    result.hasUsageCountersUpdated = true;

    if (isNewVisitor) {
      log.info({
        message: 'New visitor count',
        databaseId: accountId,
        ip,
        clientId: parsedData.clientId,
        cookie: parsedData.accountCookie,
      });
    }
  }

  result.hasLimitReached = await limitManager.hasLimitReached();

  const referrer = parsedData.referrer || req.get('Referrer') || req.headers?.origin;
  try {
    const domain = new URL(referrer as string)?.hostname || '';
    const updateLastRequestDateResult = await updateV3LastRequestDate(accountId, domain);
    const modifiedCount = updateLastRequestDateResult?.modifiedCount ?? 0;
    result.hasV3DomainLastRequestDateUpdated = modifiedCount > 0;
  } catch (e) {
    log.warn({
      message: 'Error during v3 last request date update',
      errorMessage: e.message,
      databaseId: accountId,
      referrer,
      location: parsedData.referrer,
    });
  }
  logger.info({ message: 'Limit info', ...result, ...parsedData, ip, databaseId: accountId });

  return res.send(LZString.compressToBase64(JSON.stringify(result)));
};

const scraperInfoForSPPO = async (req: Request, res: any) => {
  const logger = log.child({ service: 'scraper-for-sppo' });
  const defaultResponse = [];
  res.setHeader('Cache-Control', 'no-cache');

  try {
    const accountId = parseInt(req.params.omId, 10);
    const url = req.query.url;
    if (!accountId || !url) {
      return res.send(defaultResponse);
    }

    const service = new ScraperForSPPO(accountId, url);
    if (!service.hasConfigForAccount()) {
      return res.send(defaultResponse);
    }
    if (await service.hasLockAndSetLockIfNot()) {
      return res.send(defaultResponse);
    }
    const config = await service.getScraperConfig();
    return res.send(config);
  } catch (err: any) {
    logger.error({
      message: 'Error during collect scraper config for SPPO',
      errorMessage: err?.message,
      errorStack: err?.stack,
      accountId: req.params?.omId,
      url: req.query?.url,
      isMobile: req.query?.isMobile,
    });
    return res.send(defaultResponse);
  }
};

export { handleAccountInfo, handleVariant, afterPreInit, generateAccountInfo, scraperInfoForSPPO };
