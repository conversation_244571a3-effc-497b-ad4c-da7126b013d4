import { ISubscriber } from '../../AbstractConversionHandler';
import AbstractIntegration from './AbstractIntegration';
import HandlingResult from '../HandlingResult';
import IIntegrationAdapter from './IIntegrationAdapter';
import IntegrationResponse from '../IntegrationResponse';
import { IntegrationError } from '../IntegrationError';

export default class Attentive extends AbstractIntegration implements IIntegrationAdapter {
  getType() {
    return 'attentiveV2';
  }

  async handle(campaignId, subscriber): Promise<HandlingResult> {
    const hasPhone = this.getPhoneNumberFromBindings(subscriber);
    const hasEmail = this.getEmailFromBinding(subscriber);

    if (!hasPhone && !hasEmail) {
      return new HandlingResult(
        this.settings,
        new IntegrationResponse(200, 'Fake ok', ''),
        subscriber,
      );
    }

    this.campaignData = await this.getCampaignData(campaignId, subscriber.variantId);
    const response = await this.subscribe(subscriber);
    return new HandlingResult(this.settings, response, subscriber, this.buildError(response));
  }

  async subscribe(subscriber: ISubscriber): Promise<IntegrationResponse> {
    const config = this.getSubscribeConfig(subscriber);
    let response;
    try {
      response = await this.axiosInstance.request(config);
    } catch (e) {
      this.logWarn('Subscribe exception!', e);
      return new IntegrationResponse(
        e.response.status,
        e.response.statusText,
        config.data,
        e.response,
      );
    }

    return new IntegrationResponse(response.status, response.statusText, config.data, response);
  }

  protected buildError(response: IntegrationResponse): IntegrationError | null {
    const integrationType = this.getType().toLowerCase();
    if (response.status < 200 || (response.status > 299 && response.status !== 409)) {
      let sentBack = response.message;
      if (response.rawResponse) {
        sentBack = `Status code: ${response.status}, status text: ${response.rawResponse.statusText}, data: ${response.rawResponse.data}`;
      }
      const sentBackStr = JSON.stringify(sentBack).replace('%', '%25');

      return new IntegrationError(
        `${integrationType} subscribe error`,
        decodeURIComponent(sentBackStr),
        response.sentData,
        response.status,
      );
    }

    return null;
  }

  buildPayload(subscriber: ISubscriber) {
    const payload: { user: { phone?: string; email?: string }; signUpSourceId?: string } = {
      user: {},
    };

    const phone = this.getPhoneNumberFromBindings(subscriber);
    if (phone) {
      payload.user.phone = phone;
    }

    const email = subscriber.email.trim();
    if (email) {
      payload.user.email = email;
    }

    const signUpSourceId = this.settings.getSpecific('signUpSourceId');

    if (signUpSourceId) {
      payload.signUpSourceId = signUpSourceId;
    }

    return payload;
  }

  protected getPhoneNumberFromBindings(subscriber: ISubscriber) {
    const bindings = this.settings.getSpecific('bindings');
    const phoneBinding = bindings.find((binding) => binding.externalId === 'phone');
    return phoneBinding && subscriber.customFields
      ? subscriber.customFields[phoneBinding.fieldId]
      : '';
  }

  protected getEmailFromBinding(subscriber: ISubscriber) {
    const bindings = this.settings.getSpecific('bindings');
    const binding = bindings.find((binding) => binding.externalId === 'email');
    return binding && subscriber.email.trim();
  }

  protected getSubscribeConfig(subscriber: any): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      method: 'post',
      url: `/subscriptions`,
      data: this.buildPayload(subscriber),
    };
  }

  protected getAxiosConfig(): { [key: string]: any; baseURL: string } {
    const authorizationToken = encodeURIComponent(
      this.settings.getGlobal('authorizationToken'),
    ).split('%')[0];

    const config = {
      baseURL: `https://api.attentivemobile.com/v1/`,
      headers: {
        Authorization: `Bearer ${authorizationToken}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    };
    return config;
  }
}
