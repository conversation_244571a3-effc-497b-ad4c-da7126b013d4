/* eslint-disable camelcase */
import { ObjectID } from 'bson';
import { stringify } from 'qs';
import * as _get from 'lodash.get';
import axios from 'axios';
import IIntegrationAdapter from './IIntegrationAdapter';
import { ISubscriber } from '../../AbstractConversionHandler';
import HandlingResult from '../HandlingResult';
import IntegrationResponse from '../IntegrationResponse';
import AbstractOAuthIntegration from './AbstractOAuthIntegration';
import BindingResolver from '../binding/Salesforce';
import { IntegrationError } from '../IntegrationError';

const client_id =
  '3MVG98_Psg5cppybqUoiIXVuHeC0CUZU8q7Egav3bmnrucSFQLgL_DsvG1Hmrbg_8QK4tlt2yRmLqwdBTTKjV'; // consumer_key
const client_secret = 'BCF2ABBD9DD25B10B4D1C736B7869F0CA6BD3F4110BDD4D541D70D3E4FEC463C'; // consumer_secret

export default class Salesforce extends AbstractOAuthIntegration implements IIntegrationAdapter {
  private accessToken: string = '';
  private bindingResolver;

  getType() {
    return 'salesforce';
  }

  protected createBindingResolver() {
    this.bindingResolver = new BindingResolver(this.settings.getSpecific('bindings'));
  }

  protected async getAccessToken() {
    let token;
    const isTokenExpired = await this.isTokenExpired();
    if (isTokenExpired) {
      try {
        token = await this.refreshToken();
      } catch (e) {
        const errorMessage = e.response && e.response.data ? e.response.data : e.message;
        this.logWarn('tokenRefreshError', {
          errorMessage,
        });
      }
    } else {
      token = this.settings.getGlobal('access_token');
    }

    return token;
  }

  async handle(campaignId: ObjectID, subscriber: ISubscriber): Promise<HandlingResult> {
    this.createBindingResolver();

    this.campaignData = await this.getCampaignData(campaignId, subscriber.variantId);

    this.accessToken = await this.getAccessToken();

    let response = await this.subscribe(subscriber);

    if (response.status >= 400 && response.status < 500) {
      this.accessToken = await this.refreshToken();

      response = await this.subscribe(subscriber);
    }

    return new HandlingResult(this.settings, response, subscriber, this.buildError(response));
  }

  async subscribe(subscriber: ISubscriber): Promise<IntegrationResponse> {
    let config = this.getSubscribeConfig(subscriber);
    config = { ...config, ...this.getRequestAuthHeader() };
    let response;
    try {
      this.axiosInstance = axios.create(this.getAxiosConfig());
      response = await this.axiosInstance.request(config);
    } catch (e) {
      return new IntegrationResponse(
        e.response.status,
        e.response.statusText,
        config.data,
        e.response,
      );
    }

    return new IntegrationResponse(response.status, response.statusText, config.data, response);
  }

  private static hasErrorInResponse(response) {
    const message = _get(response, 'rawResponse.data[0].errorCode', '');
    const realMessage = _get(response, 'rawResponse.data[0].message', '');
    const alreadySubscribed = message === 'DUPLICATES_DETECTED';
    const freeSalesforce = message === 'API_DISABLED_FOR_ORG';

    if (alreadySubscribed) {
      return {
        error: false,
        message,
      };
    }

    if (freeSalesforce) {
      console.log('API_DISABLED_FOR_ORG');
      return {
        error: true,
        message:
          "In your current Salesforce subscription you couldn't use this integration. Please contact with Salesforce account manager",
      };
    }

    return {
      error: !!message,
      message: realMessage,
    };
  }

  protected buildError(response: IntegrationResponse): IntegrationError | null {
    const integrationType = this.getType().toLowerCase();

    const error = Salesforce.hasErrorInResponse(response);
    if (error && error.error) {
      return new IntegrationError(
        `${integrationType} subscribe error`,
        decodeURIComponent(error.message),
        response.sentData,
        response.status,
      );
    }

    return null;
  }

  protected buildPayload(subscriber: ISubscriber) {
    const payload: any = {
      Email: subscriber.email,
      FirstName: '',
      LastName: '',
    };

    if (subscriber.firstName) {
      payload.FirstName = subscriber.firstName;
    }

    if (subscriber.lastName) {
      payload.LastName = subscriber.lastName;
    }

    const customFields = this.bindingResolver!.resolve(
      subscriber,
      this.campaignData,
      this.body.visitor.url,
    );

    Object.assign(payload, customFields);

    return payload;
  }

  protected getSubscribeConfig(subscriber: any): {
    [key: string]: any;
    url: string;
    method: string;
  } {
    return {
      method: 'post',
      url: `/sobjects/${this.settings.getSpecific('listType')}/`,
      data: this.buildPayload(subscriber),
    };
  }

  protected getRequestAuthHeader() {
    return {
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
    };
  }

  protected getAxiosConfig(): { [key: string]: any; baseURL: string } {
    return {
      baseURL: `${this.settings.getGlobal('instanceUrl')}/services/data/v52.0/`,
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }

  protected async isTokenExpired() {
    return true;
  }

  protected async refreshToken() {
    const instanceUrl = this.settings.getGlobal('instanceUrl');
    const accessToken = this.settings.getGlobal('accessToken');
    const refreshToken = this.settings.getGlobal('refreshToken');
    const response = await axios.post(
      `${instanceUrl}/services/oauth2/token`,
      stringify({
        client_id,
        client_secret,
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );

    const dataToWrite = {
      accessToken: response.data.access_token,
      signature: response.data.signature,
      instanceUrl: response.data.instance_url,
      id: response.data.id,
      issuedAt: response.data.issued_at,
    };

    await this.saveToken(
      {
        accessToken,
      },
      dataToWrite,
    );

    return dataToWrite.accessToken;
  }
}
