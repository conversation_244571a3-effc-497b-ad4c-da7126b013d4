(function (wnd) {
  const version = 'v2';
  const buildNum = 1;
  const JF_LS_LASTVIEW_KEY_PREFIX = `jfLastViewProduct.${version}.${buildNum}`;
  const JF_LS_MOSTVIEW_KEY_PREFIX = `jfMostViewProduct.${version}.${buildNum}`;

  const ctx = { addresses: [] };
  let events = [];
  let requests = [];

  const getTimestamp = () => Math.floor(new Date().getTime() / 1000);
  const queryRequestPromise = (req) => {
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject) => {
      resolve = _resolve;
      reject = _reject;
    });

    requests.push({ ...req, index: requests.length, promise, resolve, reject });

    return promise;
  };
  const makeKey = (...parts) => parts.join('.');
  const setLVPs = (lvps) => {
    if (typeof ctx.appId === 'undefined') {
      throw new Error('Unable to read LVPs since appId is not set.');
    }

    const key = makeKey(JF_LS_LASTVIEW_KEY_PREFIX, ctx.appId);
    localStorage.setItem(key, JSON.stringify(lvps));
    console.log('[JF:DEBUG] set', key, lvps);
  };
  const getLVPs = () => {
    if (typeof ctx.appId === 'undefined') {
      throw new Error('Unable to read LVPs since appId is not set.');
    }

    const raw = localStorage.getItem(makeKey(JF_LS_LASTVIEW_KEY_PREFIX, ctx.appId));
    let lvps = [];
    if (raw) {
      try {
        lvps = JSON.parse(raw);
      } catch (err) {
        console.warning('failed to parse Recently Viewed Products');
      }
    }

    return lvps;
  };

  const setMVPs = (mvps) => {
    if (typeof ctx.appId === 'undefined') {
      throw new Error('Unable to read MVPs since appId is not set.');
    }
    if (!Array.isArray(mvps)) {
      throw new Error('Trying to set invalid MVP value');
    }

    const key = makeKey(JF_LS_MOSTVIEW_KEY_PREFIX, ctx.appId);
    localStorage.setItem(key, JSON.stringify(mvps));
    console.log('[JF:DEBUG] set', key, mvps);
  };
  const getMVPs = () => {
    if (typeof ctx.appId === 'undefined') {
      throw new Error('Unable to read MVPs since appId is not set.');
    }

    const raw = localStorage.getItem(makeKey(JF_LS_MOSTVIEW_KEY_PREFIX, ctx.appId));
    let mvps = [];
    if (raw) {
      try {
        mvps = JSON.parse(raw);
      } catch (err) {
        console.warning('failed to parse Most Viewed Products');
      }
    }

    return mvps;
  };

  function JFResponse(body) {
    this._body = body;
  }

  JFResponse.prototype.previouslyViewedProducts = function () {
    if (this._body.rpvp && this._body.rpvp.productViews) {
      return this._body.rpvp.productViews;
    }

    return [];
  };
  JFResponse.prototype.mostPopularProducts = function () {
    if (this._body.rmpp && this._body.rmpp) {
      return this._body.rmpp;
    }

    return [];
  };
  JFResponse.prototype.customerHasTags = function () {
    if (this._body.rctags) {
      return this._body.rctags;
    }

    return false;
  };

  const JFClientSDKV2 = {
    setAppId(appId) {
      if (!appId || typeof appId !== 'string') {
        throw new Error('app id must be a string.');
      }

      console.log('[JF:DEBUG] appId', appId);

      ctx.appId = appId;

      return JFClientSDKV2;
    },
    setFeatureState(featureName, enabled) {
      console.log('[JF:DEBUG] setFeatureState ', featureName, enabled);
      return JFClientSDKV2;
    },
    registerCustomerAddress(addr) {
      if (typeof addr !== 'object' || Object.keys(addr).length === 0) {
        throw new Error('Trying to register an invalid customer address');
      }
      console.log('[JF:DEBUG] register address', addr);

      const registeredAddress = ctx.addresses.find(
        (a) =>
          `${a.provider}.${a.providerServiceId}.${a.customerAddress}.${a.addressType}` ===
          `${addr.provider}.${addr.providerServiceId}.${addr.customerAddress}.${addr.addressType}`,
      );
      if (registeredAddress) {
        console.warn('[JF:DEBUG] address already registered', addr);
        return;
      }

      ctx.addresses.push(addr);

      return JFClientSDKV2;
    },
    registerProductView(product) {
      if (!product || typeof product !== 'object') {
        throw new Error('registerProductView must be called with one object parameter.');
      }

      const productId = product.productId;
      const variantId = product.variantId;

      if (
        !productId ||
        !variantId ||
        typeof productId !== 'string' ||
        typeof variantId !== 'string'
      ) {
        throw new Error('productId and variantId must be a string.');
      }

      events.push({ type: 'epv', productId, variantId, timestamp: getTimestamp() });

      return JFClientSDKV2;
    },
    registerCustomEvent(type, properties) {
      if (!type || typeof type !== 'string') {
        throw new Error('type must be a string');
      }

      events.push({ type, timestamp: getTimestamp(), ...properties });

      return JFClientSDKV2;
    },

    getPreviouslyViewedProducts(options) {
      return queryRequestPromise({ ...options, type: 'qpvp' });
    },
    getMostPopularProducts(options) {
      return queryRequestPromise({ ...options, type: 'qmpp' });
    },
    getCustomerHasTags(...tags) {
      return queryRequestPromise({ ...tags, type: 'qcht' });
    },

    evaluateConditionalExpression(options) {
      return queryRequestPromise({ type: 'qce', options });
    },

    setUserSessionSate(state) {
      return JFClientSDKV2;
    },

    log({messageType, data, severity}) {
      console.log('[JF:DEBUG] log', messageType, data, severity);
    },

    go() {
      return new Promise((resolve) => {
        const responses = [];
        const _events = [...events];
        const _requests = [...requests];

        events = [];
        requests = [];

        if (_events.length > 0) {
          _events.forEach((event) => {
            const type = event.type;
            if (type === 'epv') {
              let lvps = getLVPs();
              const mvps = getMVPs();
              const product = { productId: event.productId, variantId: event.variantId };

              lvps = lvps.filter(
                (lvp) =>
                  event.productId !== lvp.product.productId ||
                  event.variantId !== lvp.product.variantId,
              );

              lvps.unshift({
                product,
                timestamp: event.timestamp,
              });

              setLVPs(lvps);

              let mvpRecord = mvps.find(
                (mvp) =>
                  mvp.product.productId === event.productId &&
                  mvp.product.variantId === event.variantId,
              );

              if (!mvpRecord) {
                mvpRecord = { product, views: 0, timestamp: event.timestamp };
                mvps.push(mvpRecord);
              }

              mvpRecord.views += 1;
              mvps.sort((a, b) => b.views - a.views);

              setMVPs(mvps);
            } else {
              console.log('[JF:DEBUG] Custom event: ', event);
            }
          });
        }

        if (_requests.length > 0) {
          _requests.forEach((req) => {
            if (req.type === 'qpvp') {
              const limit = req.limit || 1;
              let lvps = getLVPs();

              if (req.filterCurrent) {
                lvps = lvps.filter(
                  (lvp) =>
                    !_events.some(
                      (event) =>
                        event.type === 'epv' &&
                        event.productId === lvp.product.productId &&
                        event.variantId === lvp.product.variantId,
                    ),
                );
              }

              if (req.mergeVariants) {
                lvps = Object.values(
                  lvps.reduce((lvpObj, lvp) => {
                    if (!lvpObj[lvp.product.productId]) {
                      lvpObj[lvp.product.productId] = lvp;
                    }
                    return lvpObj;
                  }, {}),
                );
              }

              responses[req.index] = {
                index: req.index,
                type: req.type,
                response: lvps.slice(0, limit),
              };
            } else if (req.type === 'qmpp') {
              const limit = req.limit || 1;
              let mvps = getMVPs();

              if (req.filterCurrent) {
                mvps = mvps.filter(
                  (mvp) =>
                    !_events.some(
                      (event) =>
                        event.type === 'epv' &&
                        event.productId === mvp.product.productId &&
                        event.variantId === mvp.product.variantId,
                    ),
                );
              }

              if (req.mergeVariants) {
                mvps = Object.values(
                  mvps.reduce((mvpObj, mvp) => {
                    if (!mvpObj[mvp.product.productId]) {
                      mvpObj[mvp.product.productId] = mvp;
                    }
                    return mvpObj;
                  }, {}),
                );
              }

              responses[req.index] = {
                index: req.index,
                type: req.type,
                response: mvps.slice(0, limit),
              };
            } else if (req.type === 'qcht') {
              responses[req.index] = {
                index: req.index,
                type: req.type,
                response: ctx.addresses.length > 1,
              };
            } else if (req.type === 'qce') {
              responses[req.index] = Math.round(Math.random()) === 1;
            }
          });
        }

        setTimeout(() => {
          _requests.forEach((req) => req.resolve(responses[req.index].response));

          resolve();
        }, 300);
      });
    },
  };
  wnd.JFClientSDK = wnd.JFClientSDK || {};
  wnd.JFClientSDK.v2 = JFClientSDKV2;
})(window);
