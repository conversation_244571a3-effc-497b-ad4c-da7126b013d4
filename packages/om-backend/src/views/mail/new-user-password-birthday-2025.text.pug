block mail-content
  | #{translator.translate('new-user-password.mail.invocation', {firstName: login.firstName})}
  |
  | #{translator.translate('new-user-password.mail.lead')}
  | #{translator.translate('new-user-password.mail.almost-ready')}
  | #{url}
  | #{translator.translate('new-user-password.mail.already-set-password')}
  | #{translator.translate('new-user-password.mail.wait-for-working-together')}
  | !{translator.translate('new-user-password.mail.goodbye')}
  | #{translator.translate('new-user-password.mail.not-subscribed')}
