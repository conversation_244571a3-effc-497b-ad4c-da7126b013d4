const { DataSource, DATA_SOURCE_TYPES } = require('../../resources/dataSource/dataSource.model');

const { triggerProductFeedSyncFlow } = require('../../resources/dataSource/util/sync');

const getProductFeedSyncList = async (databaseId = null) => {
  const filter = {
    type: {
      $in: [DATA_SOURCE_TYPES.GOOGLE_PRODUCT_FEED, DATA_SOURCE_TYPES.ARUKERESO_PRODUCT_FEED],
    },
    'properties.autoSync': true,
    $or: [{ 'properties.status.error': { $exists: false } }, { 'properties.status.error': null }],
    'properties.syncFinishedAt': { $exists: true },
  };

  if (databaseId) {
    filter.databaseId = databaseId;
  }

  const list = await DataSource.aggregate([
    { $match: filter },
    { $sort: { 'properties.syncFinishedAt': -1 } },
    {
      $group: {
        _id: '$properties.domainId',
        feed: { $first: '$$ROOT' },
      },
    },
    { $replaceRoot: { newRoot: '$feed' } },
  ]);

  return list.map((feed) => ({
    _id: feed._id,
    databaseId: feed.databaseId,
  }));
};
const triggerProductSyncFlow = async (feed, logger) => {
  try {
    const databaseId = feed.databaseId;

    const productFeed = await triggerProductFeedSyncFlow(feed._id, databaseId);

    logger.info({ type: 'product-feed-sync', action: 'trigger', productFeed });
    return true;
  } catch (error) {
    logger.error({ type: 'product-feed-sync', action: 'trigger', error, feed });
  }
};

module.exports = {
  getProductFeedSyncList,
  triggerProductSyncFlow,
};
