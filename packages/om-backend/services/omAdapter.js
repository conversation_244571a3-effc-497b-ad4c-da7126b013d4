const axios = require('axios');
const https = require('https');
const AdmZip = require('adm-zip');
const { signToken } = require('../util/jwt');
const { uploadToS3Raw } = require('./s3');

const CHROME_URL = process.env.chrome_url || 'http://om-fakeclient:9100';
const OM_ADMIN_URL = process.env.om_admin_url;
const OM_CLIENT_URL = process.env.om_client_url;
const CDN_URL = process.env.om_userupload_cdn;
const OM_BASICAUTH_USER = process.env.om_basicauth_user;
const OM_BASICAUTH_PASS = process.env.om_basicauth_pass;
const S3_BUCKET = process.env.S3_BUCKET;
const logger = require('../logger');

logger.info(
  'OM_ADMIN_URL',
  OM_ADMIN_URL,
  'OM_CLIENT_URL',
  OM_CLIENT_URL,
  'CHROME_URL',
  CHROME_URL,
  'CDN_URL',
  CDN_URL,
  'OM_BASICAUTH_USER',
  OM_BASICAUTH_USER,
);

const uploadHtmlAndPreviewsForVariant = async ({ model, variantId, campaignId, token, userId }) => {
  const entries = await fetchScreenshootsFromVariant({
    variant: { _id: variantId },
    campaignId,
    token,
  });
  const promises = [];
  for (const e of entries) {
    const key = `public/${userId}/${variantId}/${e.name}`;
    logger.info('uploading', key);
    promises.push(uploadToS3Raw({ key, buffer: e.buffer }));
  }

  const uploadResults = await Promise.all(promises);
  logger.info('uploadResults', uploadResults);
  const previewURLs = uploadResults.filter((s) => s.indexOf(variantId) > -1 && s.endsWith('.png'));
  logger.info('previewURLs', previewURLs);
  await model.update(
    { 'variants._id': variantId },
    {
      $set: {
        'variants.$.previewURLs': previewURLs,
        'variants.$.previewGeneratedAt': new Date(),
        'variants.$.previewGenerated': true,
      },
    },
  );
  logger.info('preview urls saved for', userId, variantId);
};

const fetchScreenshootsFromTemplate = async ({ template, token }) => {
  const response = await axios.get(
    `${CHROME_URL}/screenshoot?template=${template}&token=${token}`,
    { responseType: 'arraybuffer' },
  );
  logger.info(template, 'screenshoot fetch completed', response.data.length);
  try {
    const zip = new AdmZip(response.data);
    const entries = zip.getEntries().map((e) => {
      return {
        name: e.name,
        buffer: e.getData(),
      };
    });
    return entries;
  } catch (e) {
    logger.info(e);
    return [];
  }
};

const fetchScreenshootsFromVariant = async ({ variant, token, campaignId }) => {
  logger.info('fetchScreenshootsFromVariant start', variant._id);
  const response = await axios.get(
    `${CHROME_URL}/screenshoot?variant=${variant._id}&token=${token}`,
    { responseType: 'arraybuffer' },
  );
  // logger.info(variant, 'screenshoot fetch completed', response.data.length)
  const zip = new AdmZip(response.data);
  const entries = zip.getEntries().map((e) => {
    let buffer = e.getData();
    if (e.name.endsWith('.html')) {
      let content = buffer.toString('utf8');
      content = content.replace(/#om-campaign-0/g, `#om-campaign-${campaignId}`);
      content = content.replace(/\[\[CAMPAIGN_ID\]\]/g, campaignId);
      if (CDN_URL) {
        logger.info('replacing urls with cdn', CDN_URL);
        content = replaceS3UrlWithCdn(content);
      }
      // const startStr = 'id="om_popup_css">'
      // const endStr = 'body.om-body-visible'
      // const startInd = content.indexOf(startStr) + startStr.length
      // const endInd = content.indexOf(endStr)
      // logger.info('startInd', startInd, endInd)
      // logger.info('length1', content.length)
      // content = content.substring(0, startInd) + content.substring(endInd)
      buffer = Buffer.from(content);
    }
    return {
      name: e.name,
      buffer,
    };
  });
  logger.info('fetchScreenshootsFromVariant finished', variant._id);
  return entries;
};

const fetchAndSendVariantHtmlToOm = async ({ userId, variant, token }) => {
  logger.info('fetchAndSendVariantHtmlToOm, token', token);
  const response = await axios.get(`${CHROME_URL}/html?variant=${variant._id}&token=${token}`);
  let content = response.data.replace(/#om-campaign-0/g, `#om-campaign-${variant.caId}`);
  content = content.replace(/\[\[CAMPAIGN_ID\]\]/g, variant.caId);
  if (CDN_URL) {
    logger.info('replacing urls with cdn', CDN_URL);
    content = replaceS3UrlWithCdn(content);
  }
  // const startStr = 'id="om_popup_css">'
  // const endStr = 'body.om-body-visible'
  // const startInd = content.indexOf(startStr) + startStr.length
  // const endInd = content.indexOf(endStr)
  // logger.info('length1', content.length)
  // content = content.substring(0, startInd) + content.substring(endInd)
  // logger.info('length2', content.length)
  const agent = new https.Agent({ rejectUnauthorized: false });
  try {
    const postOptions = {
      httpsAgent: agent,
    };
    if (OM_BASICAUTH_USER && OM_BASICAUTH_PASS) {
      logger.info('###using basic auth for om');
      postOptions.auth = {
        username: OM_BASICAUTH_USER,
        OM_BASICAUTH_PASS,
      };
    }
    const res = await axios.post(
      await optimonkUrl({ userId, caId: variant.caId, crId: variant.crId }),
      {
        content,
        settings: getVariantSettings(variant.template),
        name: variant.crName,
      },
      postOptions,
    );
    logger.info('success: optimonk update', res.data);
    return true;
  } catch (e) {
    logger.info('error: optimonk update failed', e.message);
    return false;
  }
};

const optimonkUrl = async ({ userId, caId, crId }) => {
  const token = await signToken(
    {
      iss: OM_CLIENT_URL,
      aud: OM_ADMIN_URL,
      jti: userId,
      caId,
      crId,
    },
    { sharedKey: true },
  );
  const url = `${OM_ADMIN_URL}/integrations/creative/update/${crId}?token=${token}`;
  logger.info('optimonk url', url);
  return url;
};

const replaceS3UrlWithCdn = (content) => {
  let s3 = `${S3_BUCKET}.s3.amazonaws.com`;
  content = content.replace(new RegExp(s3, 'g'), CDN_URL);
  s3 = `${S3_BUCKET}.s3.eu-west-1.amazonaws.com`;
  content = content.replace(new RegExp(s3, 'g'), CDN_URL);
  return content;
};

const getVariantSettings = (template) => {
  let frontendType = 'popup';

  if (template.style.mode === 'nano') {
    frontendType = 'nanobar';
  } else if (template.style.overlay.position !== 5) {
    frontendType = 'sidebar';
  }

  return {
    frontendType,
  };
};

module.exports = {
  fetchAndSendVariantHtmlToOm,
  fetchScreenshootsFromTemplate,
  fetchScreenshootsFromVariant,
  uploadHtmlAndPreviewsForVariant,
};
