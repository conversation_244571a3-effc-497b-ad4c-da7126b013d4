const crypto = require('crypto');
const MailChimpAdapter = require('./integrations/mailChimp');
const log = require('../logger');

class OmMailChimpAdapter extends MailChimpAdapter {
  constructor() {
    const apiKey = process.env.mailchimp_api_key;

    if (!apiKey) {
      console.warn('MailChimp API key is not provided in environment variables.');
    }

    super({ apiKey });
  }

  generateHash(email) {
    return crypto.createHash('md5').update(email.toLowerCase()).digest('hex');
  }

  async getMemberListId(email) {
    const encodedEmail = encodeURIComponent(email);
    const memberInfo = await this.api.get(`/search-members?query=${encodedEmail}`);

    const listId = memberInfo?.exact_matches?.members[0]?.list_id || null;

    return listId;
  }

  // the tag MUST exist in mailchimp
  async addTagToMember(email, tags) {
    if (!this.isInitialized()) return;

    try {
      const listId = await this.getMemberListId(email);
      const hash = this.generateHash(email);

      await this.api.post({
        path: `lists/${listId}/members/${hash}/tags`,
        body: { tags },
      });
    } catch (e) {
      log.error('Error while adding tags to member', { errorMessage: e.message, stack: e.stack });
    }
  }

  async setFields(email, tuples = {}) {
    if (!this.isInitialized()) return;

    let listId;
    let hash;
    try {
      listId = await this.getMemberListId(email);
      log.info(`[mailchimp setfield] email: ${email}, listId: ${listId},  ${typeof listId}`);
      if (!listId) {
        // member in cleaned (?) state
        return;
      }
      hash = this.generateHash(email);

      await this.api.put({
        path: `lists/${listId}/members/${hash}`,
        body: {
          email_address: email,
          status_if_new: 'subscribed',
          merge_fields: { ...tuples },
        },
      });
    } catch (e) {
      throw new Error(
        `Error while adding field with value to member: ${email} listId: "${listId}" hash: "${hash}"`,
      );
    }
  }
}

module.exports = new OmMailChimpAdapter();
