const { performance } = require('perf_hooks');
const { convert } = require('html-to-text');
const { chatGPTModelVersions } = require('@om/workflow-sppo');
const OpenAIAdapter = require('../AIAdapters/OpenAI');
const { model: ConfigModel } = require('../../resources/config/config.model');
const fakeClientAdapter = require('../fakeClientAdapter');
const logger = require('../../logger').child({ service: 'domain-context-generator' });

const getPageContent = async (domain) => {
  const siteContent = await fakeClientAdapter.getSiteContent(domain);
  return convert(siteContent, { selectors: [{ selector: 'img', format: 'skip' }] });
};

const generate = async (domain) => {
  const promptConfig = await ConfigModel.findOne({ key: 'DOMAIN_CONTEXT_PROMPT' });
  const prompt = promptConfig?.value;

  if (!prompt) throw new Error('prompt_not_set');

  let siteContent = null;
  try {
    const getSiteContentGenerationStart = performance.now();
    siteContent = await getPageContent(domain);
    const getSiteContentGenerationEnd = performance.now();

    logger.info({
      domain,
      took: (getSiteContentGenerationEnd - getSiteContentGenerationStart).toFixed(2),
      message: `site fetching with fake-client for domain: ${domain}`,
    });
  } catch (e) {
    logger.error({ message: 'Cannot get page content', errorMessage: e.message, domain });
    throw e;
  }

  const prompts = [{ text: prompt.replace('{siteContent}', siteContent) }];
  try {
    const completions = await OpenAIAdapter.getCompletions(prompts, {
      model: chatGPTModelVersions.getDefaultVersion(),
    });
    return { content: completions[0].choices.pop().message.content, siteContent };
  } catch (e) {
    logger.error({ message: 'Error during domain context OpenAI generation', domain, prompts });
    throw e;
  }
};

module.exports = {
  generate,
};
