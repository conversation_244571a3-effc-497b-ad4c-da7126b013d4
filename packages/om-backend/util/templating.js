const pug = require('pug');

const environment = process.env.NODE_ENV || process.env.environment || 'dev';
const omFrontendUrl = process.env.om_frontend_url;
const path = require('path');
const Translator = require('./translator');

const urlParams = collectUrls(process.env);
const region = require('./region');
const logger = require('../logger').child({ service: 'util-templating' });

const config = {
  basedir: path.join(__dirname, '../src/views/'),
  cache: environment !== 'dev',
};

function collectUrls(params) {
  const result = {};
  Object.keys(params).forEach((param) => {
    if (/om_.*_url/.test(param)) {
      result[param] = params[param];
    }
  });

  return result;
}

class Templating {
  constructor(engine, translator) {
    this.engine = engine;
    this.translator = translator;
  }

  render(fileName, options = {}) {
    const app = {};
    app.t = this.translator;
    app.param = urlParams;
    app.region = region;

    const opt = Object.assign(config, options, {
      translator: this.translator,
      app,
      omFrontendUrl,
    });
    const file = fileName.indexOf('.pug$') === -1 ? `${fileName}.pug` : fileName;
    const filePath = path.join(opt.basedir, file);
    try {
      return this.engine.renderFile(filePath, opt);
    } catch (e) {
      logger.error(e.message);
      throw e;
    }
  }
}

module.exports = new Templating(pug, Translator);
