const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const querystring = require('querystring');
const { model: AccountModel } = require('../../resources/account/account.model');
const { tokenHeader } = require('../../util/jwt');
const { saveToDB, decodeData } = require('../../util/oauth');
const redis = require('../../services/ioRedisAdapter');
const {
  CLIENT_ID,
  DEFAULT_SCOPES_STRING,
  TOKEN_ENDPOINT,
  AUTH_ENDPOINT,
  REDIS_CODE,
  REDIS_STATE,
  getBasicAuthorizationHeader,
} = require('../../services/integrations/klaviyo/constants');
const KlaviyoError = require('../../services/integrations/klaviyo/KlaviyoError');
const { executeApiRequest } = require('../../services/integrations/klaviyo/apiUtils');
const { KLAVIYO_COOKIE_KEY } = require('../klaviyoApp');

const router = express.Router();
const logger = require('../../logger').child({ service: 'integration-klaviyo-oauth' });

const adminDomain = process.env.om_new_admin_url;
const backendDomain = process.env.om_backend_url;

const generateCodes = () => {
  const base64URLEncode = (str) => {
    return str.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  };
  const verifier = base64URLEncode(crypto.randomBytes(32));

  const sha256 = (buffer) => {
    return crypto.createHash('sha256').update(buffer).digest();
  };
  const challenge = base64URLEncode(sha256(verifier));

  return {
    codeVerifier: verifier,
    codeChallenge: challenge,
  };
};

// Using the shared getBasicAuthorizationHeader function from constants

const getParameters = async (req) => {
  const { userId } = await tokenHeader(req);
  const { code, state } = req.query;
  const missingParameters = [];

  if (!code) missingParameters.push('code');
  if (!state) missingParameters.push('state');

  const savedCode = await redis.get(REDIS_CODE(userId));
  if (!savedCode) {
    missingParameters.push('savedCode');
  }

  const data = decodeData(state);
  let [globalIntegrationName, campaignId] = data;
  redis.del(REDIS_STATE(userId));

  return {
    userId,
    authCode: code,
    globalIntegrationName,
    campaignId,
    savedCode,
    missingParameters,
  };
};

const getAuth = async (authCode, redirectUri, userId) => {
  try {
    return await executeApiRequest(
      async () => {
        const codeVerifier = await redis.get(REDIS_CODE(userId));
        if (!codeVerifier) {
          throw new KlaviyoError('Code verifier not found', {
            context: 'klaviyo-oauth-get-auth',
            code: 'KLAVIYO_CODE_VERIFIER_MISSING',
          });
        }

        const url = process.env.om_backend_url + redirectUri;
        return axios.post(
          TOKEN_ENDPOINT,
          querystring.stringify({
            grant_type: 'authorization_code',
            code: authCode,
            code_verifier: codeVerifier,
            redirect_uri: url,
          }),
          {
            headers: {
              Authorization: getBasicAuthorizationHeader(),
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );
      },
      {
        context: 'klaviyo-oauth-get-auth',
        logger,
      },
    );
  } catch (error) {
    // Format the error for the controller response
    logger.error(
      error instanceof KlaviyoError
        ? error.toLogFormat()
        : {
            message: 'Error in Klaviyo OAuth',
            errorMessage: error.message,
            response: error.response?.data,
          },
    );

    // Return a response object that the controller can handle
    return error.response || { status: 500, data: error.message };
  }
};

const getAuthURL = (state, codeChallenge) => {
  const redirectUri = '/api/integration-oauth/klaviyoOAuth';

  return `${AUTH_ENDPOINT}?response_type=code&client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(
    backendDomain + redirectUri,
  )}&scope=${encodeURIComponent(
    DEFAULT_SCOPES_STRING,
  )}&state=${state}&code_challenge_method=S256&code_challenge=${codeChallenge}`;
};

const getRedirectUrl = (userId, parameters, integrationId) => {
  let url = `${adminDomain}/${userId}/campaign/${parameters.campaignId}/settings?newIntegrationFlow=true&integrationId=${integrationId}&activeBox=integration`;
  if (!parameters.campaignId) {
    url = `${adminDomain}/${userId}/dashboard?message=klaviyoConnected`;
  }
  return url;
};

const getNewIntegrationFlowErrorRedirectURL = (parameters) => {
  let url = `${adminDomain}/${parameters.userId}/campaign/${parameters.campaignId}/settings?newIntegrationFlow=true&oauthError=true`;
  if (!parameters.campaignId) {
    url = `${adminDomain}/${parameters.userId}/dashboard?message=klaviyoConnectDenied`;
  }
  return url;
};

router.get('/', async (req, res) => {
  const parameters = await getParameters(req);

  if (parameters.missingParameters.length > 0) {
    res.clearCookie(KLAVIYO_COOKIE_KEY);
    res.redirect(getNewIntegrationFlowErrorRedirectURL(parameters));
    logger.error(`Missing parameters: ${parameters.missingParameters.join('; ')}`);
    return;
  }
  const { userId } = await tokenHeader(req);
  const response = await getAuth(parameters.authCode, req.baseUrl, userId);

  if (response.status !== 200) {
    res.redirect(getNewIntegrationFlowErrorRedirectURL(parameters));
    logger.error(`getAuth response code: ${response.status}`);
    return;
  }

  const expiresAt = new Date();
  expiresAt.setSeconds(expiresAt.getSeconds() + response.data.expires_in);

  const dataToWrite = {
    access_token: response.data.access_token,
    refresh_token: response.data.refresh_token,
    expires_at: expiresAt,
    scope: response.data.scope,
    name: parameters.globalIntegrationName,
  };

  redis.del(REDIS_CODE(userId));

  const integrationId = await saveToDB(userId, 'klaviyoOAuth', dataToWrite);

  let url = getRedirectUrl(userId, parameters, integrationId);

  res.redirect(url);
});

router.get('/auth', async (req, res) => {
  const { state, integrationId } = req.query;
  const { userId } = await tokenHeader(req);

  const codes = generateCodes();

  redis.setex(REDIS_STATE(userId), integrationId, 900);
  redis.setex(REDIS_CODE(userId), codes.codeVerifier, 900);

  const authUrl = getAuthURL(state, codes.codeChallenge);

  return res.redirect(authUrl);
});

router.get('/install', async (req, res) => {
  const { userId } = await tokenHeader(req);

  if (!userId) {
    res.json({ success: false, error: 'unauthorized' });
    return;
  }

  if (!req.cookies[KLAVIYO_COOKIE_KEY]) {
    res.json({ success: true });
    return;
  }

  res.clearCookie(KLAVIYO_COOKIE_KEY);

  const codes = generateCodes();

  redis.setex(REDIS_CODE(userId), codes.codeVerifier, 900);

  const authUrl = getAuthURL('install', codes.codeChallenge);

  return res.json({ success: true, authUrl });
});

router.post('/disconnect', async (req, res) => {
  const { userId } = await tokenHeader(req);
  const { integrationId } = req.body;

  try {
    await AccountModel.updateOne(
      { databaseId: userId, 'settings.integrations._id': integrationId },
      { $set: { 'settings.integrations.$[elem].data.access_token': '' } },
      { arrayFilters: [{ 'elem._id': integrationId }] },
    );
    res.json({ success: true });
  } catch (error) {
    logger.error({
      error: error.message,
      userId,
      integrationId,
    });
    res.json({ success: false });
  }
});

module.exports = router;
