const mongoose = require('mongoose');

const Schema = mongoose.Schema;

const ShopRenterInstallSchema = new Schema(
  {
    userHash: { type: String },
    shopName: { type: String },
    email: { type: String },
    firstName: { type: String },
    lastName: { type: String },
    username: { type: String },
    password: { type: String },
  },
  { timestamps: true },
);

module.exports.model = mongoose.model(
  'ShopRenterInstall',
  ShopRenterInstallSchema,
  'master_shoprenter_install',
);
module.exports.ObjectId = mongoose.Types.ObjectId;
