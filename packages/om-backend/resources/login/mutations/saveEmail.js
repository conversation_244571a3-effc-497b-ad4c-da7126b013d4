const { verifyEmail, changeEmailAddress } = require('../../../helpers/email');
const { model: AccountModel } = require('../../account/account.model');

const storeOriginalShopifyEmail = async ({ originalShopifyEmail, userId }) => {
  await AccountModel.updateOne(
    { databaseId: userId },
    { $set: { 'profile.shopifyEmail': originalShopifyEmail } },
  );
};

const saveEmail = async (_, { newEmail, originalShopifyEmail }, { log, loginId, userId }) => {
  await verifyEmail(newEmail, log);
  
  try {
    await changeEmailAddress(loginId, newEmail);
    await storeOriginalShopifyEmail({ originalShopifyEmail, userId });

    log.info(
      {
        data: {
          userId,
          loginId,
          originalShopifyEmail,
          newEmail,
        },
      },
      'Saved email address during onboarding',
    );

    return true;
  } catch (err) {
    log.error({
      err,
      data: { loginId, userId },
      message: 'An error occurred while setting email address during onboarding',
    });
    return false;
  }
};

module.exports = {
  saveEmail,
};
