type Account {
  _id: ID
  billing: Billing
  campaignInfos: JSON
  databaseId: Int
  isSubAccount: Boolean
  limits: Limits
  name: String
  settings: JSON
  subAccounts: [SubAccount]
  type: String
  users: [User]
}

type AccountResponse {
  _id: ID
  databaseId: Int
  businessName: String
  name: String
  billing: Billing
  limits: Limits
  login: Login
  settings: JSON
  totalAssigned: Int
  type: String
  partnerType: String
  userHash: String
  createdAt: Date
  isSuperAdmin: Boolean
  features: [String]
  campaignInfos: JSON
  profile: JSON
  userRole: String
  agencyUserRole: String
}

type SubAccount {
  _id: ID
  databaseId: Int
  name: String
  billing: Billing
  limits: Limits
  users: [User]
  invitations: [Invitation]
  campaignInfos: CampaignInfo
  features: [String]
}

type SubAccountResponse {
  _id: ID
  databaseId: Int
  name: String
  limits: Limits
  users: Int
  campaignInfos: CampaignInfo
}

type CampaignInfo {
  campaignCount: Int
  totalConversions: Int
}

type SubAccountsResponse {
  subAccounts: [SubAccountResponse]
  count: Int
}

type GracePeriod {
  startDate: Date
  reasonType: String
}

type Billing {
  dateExpires: DateTime
  datePaid: DateTime
  package: String
  futurePackage: String
  period: Int
  cancelledAt: DateTime
  downgradedAt: DateTime
  overrunPrice: Int
  overrunMaximumTotal: Int
  gracePeriod: GracePeriod
}

type Limits {
  maxPageViews: Float
  pageViews: Int
  maxVisitor: Int
  usedVisitor: Int
  domains: Int
  campaigns: Int
  isOverrun: Boolean
}

type User {
  loginId: ID
  role: String
}

type UserResponse {
  login: Login
  role: String
}

type Invitation {
  email: String
  expiration: DateTime
  hash: String
}

type UserSuggestions {
  users: [Login]
  agencyOwner: String
}

type InvitationInfo {
  success: Boolean
  message: String
  inviter: Login
}

type Analytics {
  enabled: Boolean
}

type Domain {
  _id: ID
  domain: String!
  inactive: Boolean
  lastRequestDate: Date
  v3LastRequestDate: Date
  analytics: Analytics
  platform: String
  shopId: String
  isKlaviyoDetected: Boolean
  crossDomainTrackingRole: String
  providerServiceIdOverride: String
}

type DetailedDomain {
  _id: ID
  domain: String!
  inactive: Boolean!
  lastRequestDate: Date
  v3LastRequestDate: Date
  crossDomainTrackingRole: String
  providerServiceIdOverride: String
  isShopifyAppExtensionActive: Boolean
  analytics: Analytics
  platform: String
  activeCampaigns: [Campaign!]!
}

type IntegrationResponse {
  success: Boolean
  id: ID
}

type WhiteLabelSettingsResponse {
  customLogo: String
  customSquared: String
  brandName: String
  domainBase: String
}

input LimitsInput {
  maxVisitor: Int
  usedVisitor: Int
}

input UpdateSubAccountInput {
  _id: ID
  name: String
  limits: LimitsInput
}

input UserInput {
  loginId: ID
  role: String
}

input BillingInput {
  package: String
}

input SubAccountInput {
  databaseId: Int
  name: String
  billing: BillingInput
  limits: LimitsInput
  users: [UserInput]
  invitations: [String]
}

input WhiteLabelInput {
  customLogo: String
  customSquared: String
  brandName: String
  domainBase: String
}

input SettingsInput {
  codeInserted: Boolean
  spamProtection: Boolean
}

input InsertCodeInput {
  email: String
  name: String
  note: String
  domain: String
}

input CredentialsInput {
  url: String
  username: String
  password: String
}

input IntegrationInput {
  type: String
  data: JSON
}

input PackageInput {
  packageName: String
}

input WizardPreferencesInput {
  color: String
  customThemeId: String
  style: String
  useCase: String
  templateId: String
  goal: String
  goals: [String]
  list: String
  coupon: String
  useCases: [String]
  type: String
  skip: Boolean
}

type PaginatedDetailedDomains {
  domains: [DetailedDomain!]!
  count: Int!
}

type ShopUserHashResponse {
  firstName: String
  userHash: String
}

input MasterContactUsInput {
  firstName: String!
  lastName: String!
  email: String!
  phoneNumber: String!
  websiteUrl: String!
  monthlyPageViews: Int!
}

input BrandColorInput {
  name: String!
  themeColor: String!
}

input BrandFontInput {
  name: String!
  value: String!
}

type LostFeatures {
  oldColor: String!
  newColor: String!
  lostPageViews: Int!
  newPageViews: Int!
  lostDomainsCount: Int!
  newDomainsCount: Int!
  lostFeatures: [String!]!
}

type ToggleShopifyAppExtensionStatusResponse {
  success: Boolean
  message: String
}

type CreateAccountForShopResponse {
  success: Boolean
  isNewRegistration: Boolean
  myshopifyDomain: String
  isAgency: Boolean
  agencyDatabaseId: Int
}
type ShopifyAppEmbedStatus {
  disabled: Boolean
  domain: String
}

enum UserProfileKeys {
  personalizedExperiences
  firstExperience
  onboardingSectionShown
  onboardingSectionDone
  onboardingSectionActivated
  aiOnboardingSectionDone
  betterEmailCheck
  interestedFeature
  wizardLastPage202404
  wizardAnimationShown
}

input FilterInput {
  search: String
}

extend type Query {
  getAccount: AccountResponse
  getInvitation(hash: String!): InvitationInfo
  getInvitations(accountId: ID): [Invitation]
  getSubAccounts(
    filter: FilterInput
    pagination: PaginationInput
    sorting: SortingInput
  ): SubAccountsResponse
  getUsers(accountId: ID): [UserResponse]
  getUsersWithSuggestions(accountId: ID): [UserResponse]
  getUserSuggestions: UserSuggestions
  getAccountMembers: JSON @accessControl(roles: [OWNER])
  getGlobalIntegrations: JSON
  getDomains: [Domain]
  getDomainsWithSiteData: JSON
  getDetailedDomainsWithPagination(
    pagination: PaginationInput
    filter: String
  ): PaginatedDetailedDomains
  codeInsertedInAllDomains: Boolean
  getDomainUsageCount(domainId: ID!): Int
  getDomainsCount: Int
  getFonts: JSON
  getDomain(domainId: ID!): Domain
  getShopUserHash(type: String!): ShopUserHashResponse
  getAgencyWhiteLabelSettings: WhiteLabelSettingsResponse
  getAgencyWhiteLabelSettingsForDomain: WhiteLabelSettingsResponse
  getAllSubAccounts: JSON
  getDowngradeLostFeatures(toPlan: String!, fromPlan: String): LostFeatures
  getAgencyManagedAccounts(filter: FilterInput): JSON
  isAgencyAffiliate(partnerId: String!): JSON
  getApiKey: String
  getExperimentalSettings: JSON!
  requestToAccessFeature(email: String!, locale: String!, feature: String!): Boolean
  getAffiliateSubscribers: [JSON]
  getAffiliateData: JSON!
  getAffiliatePayoutItems: JSON!
  preferredTemplateLanguage: String
  getSiteInfo(url: String!): JSON!
}

extend type Mutation {
  addInvitation(accountId: ID, email: String): Response! @accessControl(roles: [OWNER, WRITE])
  addSubAccount(input: SubAccountInput): Boolean!
  removeInvitation(accountId: ID, email: String): Boolean
  removeSubAccount(accountId: ID): Boolean!
  removeUser(accountId: ID, loginId: ID): Boolean!
  addUser(accountId: ID, loginId: ID): UserResponse
  updateSubAccount(input: UpdateSubAccountInput): SubAccount!
  updateUser(accountId: ID, input: UserInput): User
  updateSettings(input: SettingsInput!): Boolean!
  updateWhiteLabelSettings(input: WhiteLabelInput!): Boolean!
  sendInsertCode(input: InsertCodeInput!): Response
  sendCredentials(input: CredentialsInput!): Response
  addIntegration(input: IntegrationInput!): IntegrationResponse
  addOAuthIntegration(type: String!, name: String, params: JSON): IntegrationResponse
  editIntegration(integrationId: ID!, input: IntegrationInput!): Response
  removeDomain(domainId: ID!): Boolean
  addDomain(domain: String!, scrape: Boolean): JSON
  removeIntegration(integrationId: ID!): Boolean
  changeIntegrationName(integrationId: ID!, name: String!): Boolean
  removeMember(email: String!): Boolean
  resendInvitation(id: String!): Boolean
  renameDomain(domainId: ID!, domain: String!): Boolean
  sendUserQualifications(
    business: String!
    otherBusiness: String
    stage: String!
    whyUs: String!
    onboardingVersion: String!
  ): Boolean
  sendAdditionalOnboardingInfo(
    website: String
    phoneNumber: String
    agencyIndustry: String
    clientsNumber: Int
  ): Boolean
  hideWelcomeMessage: Boolean
  sendMasterContactUs(input: MasterContactUsInput): Boolean
  updateFonts(key: String!, subsets: [String]!, weights: [String]!): Boolean
  deleteFont(key: String!, type: String!): Boolean
  toggleAnalyticsStatus(domainId: ID!, enabled: Boolean!): Boolean
  toggleDomainActiveStatus(domainId: ID!, active: Boolean!): Response!
  toggleShopifyAppExtensionStatus(
    myshopifyDomain: String!
    isActive: Boolean!
  ): ToggleShopifyAppExtensionStatusResponse!
  sendCampaignRebuildTicket(campaignId: ID!, variantId: ID!): Boolean
  createAccountForShop(type: String!): CreateAccountForShopResponse
  finishOnboarding: Boolean
  saveOnboardingStage(stage: String): Boolean
  downgradeSubscription(
    plan: String!
    period: Int
    activeDomainIds: [String!]!
    hasFunctionalDowngrade: Boolean
    needShopifyApprove: Boolean
    skipDisableLostFeatures: Boolean
  ): Boolean
  sendMagicLinkEmail(email: String!, template: String): Boolean
  trackPageview(url: String!): Boolean!
  generateApiKey: String
  deleteApiKey: String
  updateAffiliatePartnerInfo(data: JSON!): JSON
  updateBusinessNameAndPhoneNumber(businessName: String, phoneNumber: String): Boolean
  storeProductTourStatus(status: String!, tour: String!): Response
  storeInfoBoxVisibility(box: String!, hide: Boolean!): Response
  addExperimentalSetting(input: JSON!): JSON
  updateWizardPreferences(input: WizardPreferencesInput): Boolean
  updateExperimentalSetting(input: JSON!): JSON
  deleteCookie(customId: String!): JSON
  setWizardPreference(key: String, value: Boolean): Boolean
  setWizardPreferencesByToken(token: String): Boolean
  saveOnboardingRole(role: String): Boolean
  saveOnboardingWhoBuildPopup(whoBuildPopup: String): Boolean
  checkShopifyAppEmbedStatuses(myshopifyDomains: [String!]!): [ShopifyAppEmbedStatus]
  setProfileKey(key: UserProfileKeys!, value: JSON): Boolean
  setOnboardingProperty(key: String!, value: JSON): Boolean
  updatePreferredTemplateLanguage(languageCode: String!): Boolean
  savePreferredLangByDomain(domain: String!): Boolean
  fetchSiteInfo(domain: String!, improved: Boolean): JSON
  saveAiFeatureRequest(feature: String!): Response
  saveVIPOnboardingRequest: Boolean
  saveAgencyPotential(
    firstName: String
    lastName: String
    email: String
    agencyName: String
  ): Boolean
  upsertGeneralSetting(key: String!, value: String!): Boolean
  updateAgencyContactNeeded(contactNeeded: Boolean!): Boolean
  flagAccountAsAgency: Boolean
  saveReferralSource(source: String!): Boolean
}
