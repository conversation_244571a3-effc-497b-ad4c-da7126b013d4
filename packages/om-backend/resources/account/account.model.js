const mongoose = require('mongoose');

const Schema = mongoose.Schema;
const moment = require('moment');

const { PARTNER_TYPES } = require('@om/payment/src/partnerType');
const logger = require('../../logger');
const { secondaryConnection } = require('../../services/db/connection');

const AccountSchema = new Schema(
  {
    databaseId: { type: Number, index: true },
    campaignInfos: {
      campaignCount: { type: Number, default: 0 },
      totalConversions: { type: Number, default: 0 },
    },
    businessName: { type: String },
    name: { type: String },
    billing: {
      package: { type: String },
      futurePackage: { type: String },
      period: { type: Number },
      datePaid: { type: Date },
      dateExpires: { type: Date },
      cancelledAt: { type: Date },
      downgradedAt: { type: Date },
      overrunPrice: { type: Number },
      overrunMaximumTotal: { type: Number },
      gracePeriod: {
        startDate: { type: Date },
        reasonType: { type: String },
      },
    },
    limits: {
      maxVisitor: { type: Number, default: 0 },
      usedVisitor: { type: Number, default: 0 },
      maxPageViews: { type: Number, default: 0 },
      pageViews: { type: Number, default: 0 },
      domains: { type: Number, default: 10 },
      campaigns: { type: Number, default: 10 },
      limitReachedAt: { type: Date, default: null },
      isOverrun: { type: Boolean, default: false },
      limit80EmailSent: { type: Boolean, default: false },
      limitReachedEmailSent: { type: Boolean, default: false },
      salesPinged: { type: Boolean, default: false },
      limit80SalesPing: { type: Boolean, default: false },
    },
    users: [
      {
        loginId: { type: Schema.Types.ObjectId },
        role: { type: String, enum: ['write', 'owner'] },
      },
    ],
    invitations: [
      {
        email: { type: String },
        expiration: { type: Date },
        hash: { type: String },
      },
    ],
    subAccounts: [],
    settings: {
      wizardPreferences: {
        color: { type: String },
        customThemeId: { type: Schema.Types.ObjectId },
        style: { type: String },
        useCase: { type: Schema.Types.ObjectId },
        templateId: { type: Schema.Types.ObjectId },
        coupon: { type: String },
        goal: { type: String },
        goals: { type: [String] },
        list: { type: String },
        recommendationDismissed: { type: Boolean },
        useCases: { type: [String] },
        type: { type: String },
        skip: { type: Boolean },
      },
      apiKeys: { type: Array, default: [], sparse: true },
      slack: {
        agency: { type: Boolean, default: false },
        midmarket: { type: Boolean, default: false },
        midmarketUpgrade: { type: Boolean, default: false },
        affiliate: { type: Object },
        freemiumHotLead: { type: Boolean, default: false },
      },
      fonts: { type: Array, default: [] },
      customFonts: [
        {
          _id: false,
          name: { type: String },
          key: { type: String },
        },
      ],
      downgrade: {
        from: { type: String },
        to: { type: String },
        toPeriod: { type: Number },
        pending: { type: Boolean, default: false },
        activeDomainIds: [{ type: Schema.Types.ObjectId }],
        hasFunctionalDowngrade: { type: Boolean, default: true },
        needShopifyApprove: { type: Boolean, default: false },
        skipDisableLostFeatures: { type: Boolean, default: false },
      },
      onboarding: {
        showWelcomeMessage: { type: Boolean, default: null },
        campaignCreated: { type: Boolean, default: false },
        finished: { type: Boolean, default: false },
        stage: { type: String },
        role: { type: String },
        whoBuildPopup: { type: String },
        additional: {
          website: { type: String },
          phone: { type: String },
          agency_industry: { type: String },
          clients_number: { type: String },
        },
        'accountSetup-interestedFeature': { type: String },
        hear: { type: String },
      },
      userQualificationFilled: { type: Boolean, default: false },
      qualifications: {
        business: { type: String },
        otherBusiness: { type: String },
        stage: { type: String },
        whyUs: { type: String },
      },
      shopifyQualifications: {
        plan: { type: String },
        revenue: { type: Number },
        orderCount: { type: Number },
        lastRequestAt: { type: Date, sparse: true },
      },
      review: {
        rating: { type: Number },
        feedback: { type: String },
      },
      hasPoweredByLinkDisabled: { type: Boolean, default: false },
      preventSubscribe: {
        emails: { type: Array, default: [] },
        storage: { type: Boolean, default: false }, // previously - settings.preventSubscriberDataStorage
      },
      codeInserted: { type: Boolean, default: false },
      messages: {
        actionRequired: {
          lastSentAt: { type: Date, default: null },
          sentCount: { type: Number, default: 0 },
        },
        dailyMailsUnsubscribedAt: { type: Date, default: null }, // DEPRECATED, but stil contain values for accounts that unsubscribed before new weeklyReportUnsubscribedAt field
        weeklyReportUnsubscribedAt: { type: Date, default: null }, // FYI: used for freemium subscriber report mails
        midmarketNoActiveCampaignPing: { type: Number, default: null }, // used for inactive-midmarket-ping cron job
      },
      general: [
        {
          _id: false,
          key: { type: String },
          value: { type: String }, // __QUESTION__ legyen-e csak szám
          dateCreated: { type: Date },
          dateUpdated: { type: Date },
        },
      ],
      shops: { type: Array },
      whiteLabel: { type: Object },
      domains: [
        {
          domain: { type: String, required: true },
          lastRequestDate: { type: Date },
          v3LastRequestDate: { type: Date, default: undefined },
          inactive: { type: Boolean, default: false },
          analytics: {
            enabled: { type: Boolean, default: true },
          },
          platform: { type: String, default: '' },
          shopId: { type: String, default: null },
          isKlaviyoDetected: { type: Boolean, default: false },
          useNonstandardOrders: { type: Boolean, default: false },
          crossDomainTrackingRole: { type: String, default: null },
          providerServiceIdOverride: { type: String, default: null },
        },
      ],
      zapier: {
        auth: {
          codes: [{ type: String }],
          tokens: [{ type: String }],
        },
        hooks: [
          {
            _id: false,
            url: { type: String, default: null },
            campaigns: [{ type: Number }],
          },
        ],
      },
      integrations: [
        {
          // _id: true, // __TODO__ összekötni campaign-ban lévő settings.integrations.id-val
          type: { type: String },
          migrated: { type: Boolean, default: false },
          data: {},
        },
      ],
      spamProtection: { type: Boolean, default: false },
      affiliate: {
        partnerInfo: {
          displayName: { type: String, default: '' },
          slug: { type: String, default: '', index: true },
          headline: { type: String, default: '' },
          buttonText: { type: String, default: '' },
          buttonUrl: { type: String, default: '' },
          about: { type: String, default: '' },
          image: { type: String, default: '' },
        },
        canBeManagedByAgency: { type: Boolean, default: false },
      },
      experiments: { type: Object, default: {} },
    },
    deleted: { type: Boolean, default: false },
    type: { type: String, enum: ['agency', 'sub', 'normal'], index: true },
    partnerType: { type: String, enum: Object.values(PARTNER_TYPES), index: true },
    features: { type: Array, default: [] },
    profile: { type: Object },
  },
  { timestamps: true },
);

AccountSchema.index({ 'settings.shops.type': 1 }, { sparse: true });
AccountSchema.index({ 'settings.shops.myshopify_domain': 1 }, { sparse: true });
AccountSchema.index({ 'settings.whiteLabel.domainBase': 1 }, { sparse: true });
AccountSchema.index({ 'users.loginId': 1 }, { sparse: true });
// Hooks
AccountSchema.pre('find', softDeleteMiddleware);
AccountSchema.pre('findOne', softDeleteMiddleware);
AccountSchema.pre('count', softDeleteMiddleware);

function softDeleteMiddleware(next) {
  const filter = this.getQuery();
  if (filter.deleted == null) {
    filter.deleted = false;
  }
  next();
}

function logAccountUpdate() {
  const query = this.getQuery();
  const update = this.getUpdate();

  logger.info({ query, update }, 'updateAccount');
}
AccountSchema.pre('updateOne', logAccountUpdate);
AccountSchema.pre('findOneAndUpdate', logAccountUpdate);
AccountSchema.pre('updateMany', logAccountUpdate);
AccountSchema.pre('update', logAccountUpdate);

AccountSchema.statics.isPoweredByLinkDisabledInOwnerAccount = async function (subAccountId) {
  const { settings } = await this.findOne(
    { subAccounts: { $in: [subAccountId] } },
    { 'settings.hasPoweredByLinkDisabled': 1 },
  );
  return settings.hasPoweredByLinkDisabled === true;
};

AccountSchema.statics.getPastActiveAccounts = async function () {
  const accs = await this.find(
    {
      'billing.package': { $nin: ['TRIAL', 'BELSO'] },
      type: { $ne: 'sub' },
    },
    { databaseId: 1 },
  );
  const ids = accs.map((a) => a.databaseId);
  return this.getAccountsWithRegion(ids);
};

AccountSchema.statics.getAccountsWithRegion = async function (ids) {
  const accounts = await this.aggregate([
    { $match: { databaseId: { $in: ids } } },
    {
      $lookup: {
        from: 'master_logins',
        foreignField: '_id',
        localField: 'users.0.loginId',
        as: 'logins',
      },
    },
    {
      $project: {
        databaseId: 1,
        name: 1,
        features: 1,
        createdAt: 1,
        billing: 1,
        users: 1,
        regions: '$logins.region',
      },
    },
  ]);
  const result = accounts.map((a) => {
    return {
      id: a.databaseId,
      email: a.name,
      package: a.billing.package,
      period: a.billing.period,
      datePaid: a.billing.datePaid,
      dateExpires: a.billing.dateExpires,
      createdAt: a.createdAt,
      region: a.regions[0],
      features: a.features,
    };
  });
  return result;
};

AccountSchema.statics.getActiveAccounts = async function (expiredDays = 0) {
  const dateExpiresDate = new Date(moment().subtract(expiredDays, 'days').format('YYYY-MM-DD'));
  const accounts = await this.aggregate([
    {
      $match: {
        'billing.package': { $nin: ['TRIAL', 'BELSO', 'FREE', 'DEMO'] },
        'billing.dateExpires': { $gte: dateExpiresDate },
        type: { $ne: 'sub' },
      },
    },
    {
      $lookup: {
        from: 'master_logins',
        foreignField: '_id',
        localField: 'users.0.loginId',
        as: 'logins',
      },
    },
    { $addFields: { login: { $arrayElemAt: ['$logins', 0] } } },
    {
      $project: {
        users: 1,
        databaseId: 1,
        expires: '$billing.dateExpires',
        paid: '$billing.datePaid',
        package: '$billing.package',
        period: '$billing.period',
        email: '$login.email',
        region: '$login.region',
        shopifyPay: '$settings.shops.pay',
      },
    },
  ]);
  // set shopifyPay
  accounts.forEach((e) => {
    const isShopifyPay =
      e.shopifyPay && e.shopifyPay.find((p) => p === '1' || p === 1) !== undefined;
    e.isShopifyPay = isShopifyPay;
  });
  return accounts;
};
const accountModelSecondary = secondaryConnection.model(
  'Account',
  AccountSchema,
  'master_accounts',
);

module.exports.model = mongoose.model('Account', AccountSchema, 'master_accounts');
module.exports.secondary = accountModelSecondary;
module.exports.ObjectId = mongoose.Types.ObjectId;
