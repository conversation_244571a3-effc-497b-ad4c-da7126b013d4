const { addAlertsToCampaigns } = require('../../../helpers/userAlertV2');
const { model } = require('../campaign.model');

const getCampaignAlerts = async (_, { campaignId }, ctx) => {
  const { userId, log } = ctx;
  let result = null;
  try {
    const CampaignModel = model(userId);
    const campaign = await CampaignModel.findOne({ _id: campaignId }, { domainId: 1 }).lean();
    await addAlertsToCampaigns(userId, [campaign], []);
    return campaign.alerts;
  } catch (e) {
    log.error(e, 'Cannot load user alerts', ctx);
  }

  return result;
};

module.exports = {
  getCampaignAlerts,
};
