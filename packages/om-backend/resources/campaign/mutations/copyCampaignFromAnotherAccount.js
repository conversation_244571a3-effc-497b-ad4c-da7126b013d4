const _get = require('lodash.get');
const { copyInBucket } = require('../../../services/s3');
const { CampaignShareModel } = require('../../shared/shared.model');
const { model, ObjectId, DYNAMIC_CONTENT_TYPE } = require('../campaign.model');
const { model: UserFieldsModel } = require('../../field/field.model');
const { model: CustomThemeModel } = require('../../custom_theme/custom_theme.model');
const { model: AccountModel } = require('../../account/account.model');
const { model: VariantTemplateModel } = require('../variantTemplate.model');
const { signToken } = require('../../../util/jwt');
const { FEATURES, isFeatureEnabled } = require('../../../util/feature');
const { getIncreasedCampaignCount } = require('../helper');
const { regenerateTemplateIds } = require('../../../services/campaignUtil');
const { updateAccountCampaignInfo } = require('../../../helpers/account/campaigns');
const omAdapter = require('../../../services/omAdapter');

const _copyInputs = async ({ inputs, sourceDatabaseId, databaseId }) => {
  const customIds = (inputs || []).map((input) => input && input?.customId).filter((v) => !!v);
  if (!customIds.length) return;
  const sourceUserFields = await UserFieldsModel.find({
    customId: { $in: customIds },
    databaseId: sourceDatabaseId,
  }).lean();
  const targetUserFields = await UserFieldsModel.find({
    customId: { $in: customIds },
    databaseId,
  }).lean();

  if (sourceUserFields.length === targetUserFields.length) return;

  const missing = [];
  for (const sourceInput of sourceUserFields) {
    const { customId } = sourceInput;
    const found = targetUserFields.some(
      ({ customId: existingCustomId }) => existingCustomId === customId,
    );

    if (found) continue;

    missing.push({
      ...sourceInput,
      _id: ObjectId(),
      databaseId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  await UserFieldsModel.create(missing);
};

const _copyCustomTheme = async (themeKit, databaseId, themeKitIdMap) => {
  if (!themeKit?.id) return;
  const sourceId = ObjectId(themeKit.id);
  if (themeKitIdMap[`${sourceId}`]) return themeKitIdMap[`${sourceId}`];
  const sourceTheme = await CustomThemeModel.findOne({ _id: sourceId }).lean();
  const newId = ObjectId();
  const userThemesCount = await CustomThemeModel.count({
    databaseId,
    sourceTheme: { $exists: true },
  });
  const name = `Shared theme ${userThemesCount + 1}`;
  sourceTheme.themeKit.id = newId;
  sourceTheme.themeKit.name = name;

  await CustomThemeModel.create({
    ...sourceTheme,
    _id: newId,
    name,
    databaseId,
  });

  const result = { id: newId, name };

  themeKitIdMap[`${sourceId}`] = result;

  return result;
};

const PATH_FONTS = 'settings.fonts';
const PATH_CUSTOM_FONTS = 'settings.customFonts';

const __copyFonts = async ({ sourceAccount, targetAccount }, copiedFonts) => {
  const sourceFonts = _get(sourceAccount, PATH_FONTS, []);
  const targetFonts = _get(targetAccount, PATH_FONTS, []);

  for (const sourceFont of sourceFonts) {
    const { key, subsets, weights } = sourceFont;
    if (copiedFonts.has(key)) continue;
    copiedFonts.add(key);
    const found = targetFonts.find(({ key: targetKey }) => key === targetKey);
    if (found) {
      const $addToSet = {};
      if (Array.isArray(subsets)) {
        $addToSet['settings.fonts.$.subsets'] = { $each: subsets };
      }
      if (Array.isArray(weights)) {
        $addToSet['settings.fonts.$.weights'] = { $each: weights };
      }
      if (Object.keys($addToSet).length) {
        await AccountModel.updateOne(
          { _id: targetAccount._id, 'settings.fonts.key': key },
          { $addToSet },
        );
      }
    } else {
      await AccountModel.updateOne(
        { _id: targetAccount._id },
        { $push: { [PATH_FONTS]: sourceFont } },
      );
    }
  }
};

const __copyCustomFonts = async ({ sourceAccount, targetAccount }, copiedFonts) => {
  const sourceFonts = _get(sourceAccount, PATH_CUSTOM_FONTS, []);
  const targetFonts = _get(targetAccount, PATH_CUSTOM_FONTS, []);

  targetFonts.forEach(({ key }) => copiedFonts.add(key));

  for (const sourceFont of sourceFonts) {
    const { key, name } = sourceFont;
    if (copiedFonts.has(key)) continue;
    copiedFonts.add(key);
    const found = targetFonts.find(({ key: targetKey }) => key === targetKey);

    if (!found) {
      const fileCopies = ['woff', 'woff2', 'css'].map((ext) => {
        const path = `${name}/${name}.${ext}`;
        return copyInBucket({
          from: `customFonts/${sourceAccount.databaseId}/${path}`,
          to: `customFonts/${targetAccount.databaseId}/${path}`,
        });
      });
      await Promise.allSettled([
        ...fileCopies,
        AccountModel.updateOne(
          { _id: targetAccount._id },
          {
            $push: { [PATH_CUSTOM_FONTS]: sourceFont },
          },
        ),
      ]);
    }
  }
};

const _copyAccountFonts = async ({ sourceId, targetId }, copiedFonts) => {
  const projection = { [PATH_FONTS]: 1, [PATH_CUSTOM_FONTS]: 1, databaseId: 1 };
  const sourceAccount = await AccountModel.findOne({ databaseId: sourceId }, projection);
  const targetAccount = await AccountModel.findOne({ databaseId: targetId }, projection);
  const accounts = { sourceAccount, targetAccount };

  await Promise.all([__copyFonts(accounts, copiedFonts), __copyCustomFonts(accounts, copiedFonts)]);
};

const copyCampaignFromAnotherAccount = async (_, { token }, { userId, log }) => {
  const shareData = await CampaignShareModel.findOne({ token });

  if (!shareData) return false;

  const {
    databaseId: sourceAccount,
    data: { campaign: sourceCampaignId },
  } = shareData;

  const [fromAccount, toAccount] = await Promise.all([
    AccountModel.findOne({ databaseId: sourceAccount }, { features: 1 }),
    AccountModel.findOne({ databaseId: userId }, { settings: 1, features: 1 }),
  ]);

  const fromAccountMigrated = isFeatureEnabled(fromAccount, FEATURES.VARIANT_TEMPLATE_MIGRATED);
  const toAccountMigrated = isFeatureEnabled(toAccount, FEATURES.VARIANT_TEMPLATE_MIGRATED);
  const bothMigrated = fromAccountMigrated && toAccountMigrated;

  if (fromAccountMigrated !== toAccountMigrated) {
    log.error('copyCampaignFromAnotherAccount failed, Migration status of accounts are different', {
      fromAccount: sourceAccount,
      fromAccountMigrated: !!fromAccountMigrated,
      toAccount: userId,
      toAccountMigrated,
    });
    return false;
  }

  const SourceCampaignModel = model(sourceAccount);
  let sourceCampaign;
  let sourceCampaignTemplates;

  if (bothMigrated) {
    sourceCampaign = await SourceCampaignModel.findOne({ id: sourceCampaignId });
    sourceCampaignTemplates = await VariantTemplateModel.find({
      databaseId: sourceAccount,
      campaignId: sourceCampaignId,
    });
  } else {
    sourceCampaign = await SourceCampaignModel.findOne({ id: sourceCampaignId }).select(
      '+variants.template',
    );
  }

  const account = await AccountModel.findOne({ databaseId: userId }, { 'settings.domains': 1 });

  // currently dc share not supported
  if (
    !sourceCampaign ||
    sourceCampaign?.type === DYNAMIC_CONTENT_TYPE ||
    sourceCampaign.version < 2
  ) {
    return false;
  }

  const TargetCampaignModel = model(userId);

  const campaign = new TargetCampaignModel(sourceCampaign.toObject());
  const fontsCopied = new Set();
  const [campaignCount] = await Promise.all([
    getIncreasedCampaignCount(userId),
    _copyAccountFonts({ sourceId: sourceAccount, targetId: userId }, fontsCopied),
  ]);

  const newCampaignId = ObjectId();
  campaign._id = newCampaignId;
  campaign.id = campaignCount;
  campaign.name =
    campaign.locale === 'hu' ? `Kampány #${campaignCount}` : `Campaign #${campaignCount}`;
  campaign.isNew = true;
  campaign.impressions = 0;
  campaign.conversions = 0;
  campaign.conversionRate = 0;
  campaign.status = 'inactive';
  campaign.currentExperimentId = null;
  campaign.settings.positions = null;
  campaign.settings.manuallyEmbedded = false;
  campaign.settings.integrations = [];
  if (account?.settings?.domains?.length) {
    campaign.domainId = account.settings.domains[0]?._id;
    campaign.domain = account?.settings.domains[0]?.domain;
  }

  const themeKitIdMap = {};
  const newVariantIds = [];
  const notDeletedVariants = [];
  let targetVariantTemplates = [];
  for (const caVariant of campaign.variants) {
    if (caVariant.status === 'deleted') continue;
    let newVariantId = ObjectId();
    const oldVariantId = caVariant._id;
    caVariant._id = newVariantId;
    caVariant.id = undefined;

    if (bothMigrated) {
      const variantTemplate = sourceCampaignTemplates.find(
        (v) => `${v.variantId}` === `${oldVariantId}`,
      );
      if (variantTemplate) {
        targetVariantTemplates.push({
          databaseId: userId,
          campaignId: campaign.id,
          variantId: newVariantId,
          template: regenerateTemplateIds(variantTemplate.template),
        });
      }
    } else if (caVariant.template) {
      delete caVariant.template['style.data.tabText'];
      caVariant.template = regenerateTemplateIds(caVariant.template);
    }

    // await _copyImages(caVariant?.template?.images ?? []);
    await _copyInputs({
      inputs: caVariant?.template?.inputs ?? [],
      sourceDatabaseId: sourceAccount,
      databaseId: userId,
    });
    const customThemeResult = await _copyCustomTheme(
      caVariant?.template?.themeKit,
      userId,
      themeKitIdMap,
    );
    if (customThemeResult) {
      const { id: newCustomThemeId, name: newCustomThemeName } = customThemeResult;
      caVariant.template.themeKit.name = newCustomThemeName;
      caVariant.template.themeKit.id = newCustomThemeId;
    }

    caVariant.impressions = 0;
    caVariant.conversions = 0;
    caVariant.confidence = null;
    caVariant.winner = false;
    newVariantIds.push(newVariantId);
    caVariant.previewGenerated = false;
    caVariant.createdAt = new Date();
    caVariant.updatedAt = new Date();
    notDeletedVariants.push(caVariant);
  }

  campaign.variants = notDeletedVariants;
  campaign.createdAt = new Date();
  campaign.updatedAt = new Date();
  await campaign.save();

  if (bothMigrated) {
    await VariantTemplateModel.insertMany(targetVariantTemplates);
  }

  await updateAccountCampaignInfo(userId);
  const dockerToken = await signToken({ userId }, { expiresIn: 300 });

  log.info('copyCampaignFromAnotherAccount, html generation started for variants', {
    newCampaignId,
    notDeletedVariants: notDeletedVariants.map((v) => v._id),
  });

  for (const v of notDeletedVariants) {
    omAdapter.uploadHtmlAndPreviewsForVariant({
      model: TargetCampaignModel,
      campaignId: campaignCount,
      variantId: v._id,
      token: dockerToken,
      userId,
    });
  }

  return { success: true, campaignId: campaign.id, variantId: newVariantIds[0] };
};

module.exports = { copyCampaignFromAnotherAccount };
