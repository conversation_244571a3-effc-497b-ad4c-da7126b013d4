const mongoose = require('mongoose');
const { campaignChangeTypes } = require('./common');

const Schema = mongoose.Schema;

const changeLogSchema = {
  databaseId: { type: Number, required: true },
  context: { type: Object, required: false },
  changeType: { type: String, enum: Object.keys(campaignChangeTypes) },
  login: {
    loginId: { type: Schema.Types.ObjectId, required: true },
    email: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    role: { type: String, required: true },
    superadmin: { type: <PERSON><PERSON><PERSON>, required: true },
    superAdminName: { type: String },
    superAdminEmail: { type: String },
  },
};

const ChangeLogSchema = new Schema(changeLogSchema, { timestamps: true });

ChangeLogSchema.index({ databaseId: 1 });
ChangeLogSchema.index({ 'context.campaignId': 1 });

module.exports.model = mongoose.model('ChangeLog', ChangeLogSchema, 'user_change_logs');
module.exports.ObjectId = mongoose.Types.ObjectId;
