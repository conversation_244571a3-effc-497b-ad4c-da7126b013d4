const mockRunQuery = jest.fn();
const mockRecreateOrDropMaterializedView = jest.fn();
const TestDb = require('../../../test/database/testDb');
const { ObjectId } = require('../goal.model');
const {
  createGoal,
  createAccount,
  createCampaign,
} = require('../../../test/database/seed/seedGoal');
const { getGoal } = require('./getGoal');

jest.mock('../helpers/getGoalQuery', () => {
  return {
    getQuery: jest.fn().mockReturnValue({ query: 'test query', params: [] }),
  };
});

jest.mock('../../../services/bigQueryAdapter', () => {
  return {
    BigQueryClient: jest.fn().mockImplementation(() => {
      return {
        runQuery: mockRunQuery,
      };
    }),
  };
});

jest.mock('../helpers/materialized-view-generation', () => {
  return {
    recreateOrDropMaterializedView: mockRecreateOrDropMaterializedView,
  };
});

jest.setTimeout(60000);

const SEED_GOAL_ID = '644143a600f5cb5f69695320';
const SEED_USER_ID = 44;

describe('getGoal', () => {
  let logStub;
  const testDatabase = new TestDb();

  beforeAll(async () => {
    await testDatabase.start();
  });

  beforeEach(() => {
    logStub = {
      error: jest.fn(),
      info: jest.fn(),
    };
    mockRunQuery.mockResolvedValue([
      {
        variantId: '644143a600f5cb5f69695323',
        goalCount: {
          mobile: 1,
          desktop: 1,
          allType: 2,
        },
        uniqueVisitorCount: {
          mobile: 1,
          desktop: 1,
          allType: 4,
        },
      },
      {
        variantId: '644143a600f5cb5f69695324',
        goalCount: {
          mobile: 2,
          desktop: 3,
          allType: 5,
        },
        uniqueVisitorCount: {
          mobile: 2,
          desktop: 3,
          allType: 5,
        },
      },
      {
        variantId: '644143a600f5cb5f69695325',
        goalCount: {
          mobile: 1,
          desktop: 2,
          allType: 3,
        },
        uniqueVisitorCount: {
          mobile: 1,
          desktop: 2,
          allType: 3,
        },
      },
      {
        variantId: '644143a600f5cb5f69695326',
        goalCount: {
          mobile: 0,
          desktop: 1,
          allType: 1,
        },
        uniqueVisitorCount: {
          mobile: 1,
          desktop: 1,
          allType: 2,
        },
      },
    ]);
  });

  afterAll(async () => {
    await testDatabase.stop();
  });

  afterEach(async () => {
    await testDatabase.cleanup();
  });

  test('should get goal from db, run query and aggregate the results', async () => {
    await createGoal(testDatabase.db, { domainId: ObjectId('6441376901d35ce92a2af430') });
    await createAccount(testDatabase.db, {
      settings: {
        domains: [
          { _id: ObjectId('6441376901d35ce92a2af430'), shopId: 'shop id', domain: 'domain' },
        ],
      },
    });
    await createCampaign(testDatabase.db, {});

    const goalConversions = await getGoal(
      null,
      {
        goalId: SEED_GOAL_ID,
        campaignId: 1,
        domainId: ObjectId('6441376901d35ce92a2af430'),
      },
      { userId: SEED_USER_ID, log: logStub },
    );

    expect(goalConversions).toEqual({
      campaignId: 1,
      goalId: '644143a600f5cb5f69695320',
      goalCount: 10,
      isDefault: false,
      name: 'This is a goal',
      variants: [
        {
          variantId: '644143a600f5cb5f69695323',
          goalCount: 2,
          uniqueVisitorCount: 4,
        },
        {
          variantId: '644143a600f5cb5f69695324',
          goalCount: 5,
          uniqueVisitorCount: 5,
        },
        {
          variantId: '644143a600f5cb5f69695325',
          goalCount: 3,
          uniqueVisitorCount: 3,
        },
      ],
    });
  });
  test('should handle default goal', async () => {
    await createAccount(testDatabase.db, {
      settings: {
        domains: [
          { _id: ObjectId('6441376901d35ce92a2af430'), shopId: 'shop id', domain: 'domain' },
        ],
        shops: [],
      },
    });
    await createCampaign(testDatabase.db, {});
    const goalConversions = await getGoal(
      null,
      {
        goalId: 'default_purchase',
        campaignId: 1,
        domainId: ObjectId('6441376901d35ce92a2af430'),
      },
      { userId: SEED_USER_ID, log: logStub },
    );
    expect(goalConversions).toEqual({
      campaignId: 1,
      goalId: 'default_purchase',
      goalCount: 10,
      isDefault: true,
      name: 'purchase',
      variants: [
        {
          variantId: '644143a600f5cb5f69695323',
          goalCount: 2,
          uniqueVisitorCount: 4,
        },
        {
          variantId: '644143a600f5cb5f69695324',
          goalCount: 5,
          uniqueVisitorCount: 5,
        },
        {
          variantId: '644143a600f5cb5f69695325',
          goalCount: 3,
          uniqueVisitorCount: 3,
        },
      ],
    });
  });
  test('should throw error if there is only start or end date', async () => {
    await createGoal(testDatabase.db, { domainId: ObjectId('6441376901d35ce92a2af430') });
    await createAccount(testDatabase.db, {
      settings: {
        domains: [
          { _id: ObjectId('6441376901d35ce92a2af430'), shopId: 'shop id', domain: 'domain' },
        ],
      },
    });
    await createCampaign(testDatabase.db, {});
    await expect(() =>
      getGoal(
        null,
        {
          goalId: SEED_GOAL_ID,
          campaignId: 1,
          startDate: '2023-12-12',
        },
        { userId: SEED_USER_ID, log: logStub },
      ),
    ).rejects.toThrow();
    expect(logStub.error).toHaveBeenCalledWith(
      { err: new Error(), goalId: '644143a600f5cb5f69695320', userId: 44 },
      'Failed to get goal by variantId, userId: 44, goalId: 644143a600f5cb5f69695320',
    );
  });
  test('should throw error if cannot find domain', async () => {
    await createGoal(testDatabase.db, { domainId: ObjectId('6441376901d35ce92a2af431') });
    await createAccount(testDatabase.db, {
      settings: {
        domains: [
          { _id: ObjectId('6441376901d35ce92a2af430'), shopId: 'shop id', domain: 'domain' },
        ],
      },
    });
    await createCampaign(testDatabase.db, {});
    await expect(() =>
      getGoal(
        null,
        {
          goalId: SEED_GOAL_ID,
          campaignId: 1,
          domainId: ObjectId('6441376901d35ce92a2af430'),
        },
        { userId: SEED_USER_ID, log: logStub },
      ),
    ).rejects.toThrow();
    expect(logStub.error).toHaveBeenCalledWith(
      {
        err: new Error('Unable to resolve domain.'),
        goalId: '644143a600f5cb5f69695320',
        userId: 44,
      },
      'Failed to get goal by variantId, userId: 44, goalId: 644143a600f5cb5f69695320',
    );
  });
  test('should create materialized view if it is missing', async () => {
    await createGoal(testDatabase.db, { domainId: ObjectId('6441376901d35ce92a2af430') });
    await createAccount(testDatabase.db, {
      settings: {
        domains: [
          { _id: ObjectId('6441376901d35ce92a2af430'), shopId: 'shop id', domain: 'domain' },
        ],
      },
    });
    await createCampaign(testDatabase.db, {});
    mockRunQuery.mockRejectedValue({ errors: [{ reason: 'notFound' }] });

    await expect(() =>
      getGoal(
        null,
        {
          goalId: SEED_GOAL_ID,
          campaignId: 1,
          domainId: ObjectId('6441376901d35ce92a2af430'),
        },
        { userId: SEED_USER_ID, log: logStub },
      ),
    ).rejects.toThrow();

    expect(mockRecreateOrDropMaterializedView).toHaveBeenCalledWith({
      databaseId: 44,
      domainId: ObjectId('6441376901d35ce92a2af430'),
      force: true,
      projectId: 'optimonk-secure-staging-5c52',
    });
  });
});
