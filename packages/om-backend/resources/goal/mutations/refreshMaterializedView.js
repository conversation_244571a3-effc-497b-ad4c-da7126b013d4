const { refreshMatView } = require('../helpers/refreshMaterializedView');

const refreshMaterializedView = async (_, { domainId }, { userId, log }) => {
  try {
    await refreshMatView({ domainId, userId });
  } catch (e) {
    const error = new Error();
    const message = 'Unable to refresh view, a concurrent update may be in progress';
    error.extensions = {
      code: 500,
      message,
    };
    log.warn({ err: e, domainId, databaseId: userId }, message);
    throw error;
  }
  return {
    success: true,
  };
};

module.exports = { refreshMaterializedView };
