input newFieldInput {
  customId: String!
  name: String!
  type: String!
}

input newMasterFieldInput {
  customId: String!
  name: String!
  type: String!
  template: String!
}

input updateFieldInput {
  _id: String!
  name: String!
  type: String!
}

input updateMasterFieldInput {
  _id: String!
  template: String!
  name: String!
  type: String!
}

type Field {
  _id: String
  customId: String!
  name: String!
  type: String!
}

extend type Query {
  allFields(fieldIds: [String],excludeBuiltIn: Boolean): [Field]!
  allTemplateFields(template: String): [Field]!
  allFieldsNew(campaignId: Int!): [Field]!
}

extend type Mutation {
  newField(input: newFieldInput!): Boolean!
  newMasterField(input: newMasterFieldInput!): Boolean!
  updateField(input: updateFieldInput!): Boolean!
  updateMasterField(input: updateMasterFieldInput!): Boolean!
  deleteField(customId: String!, databaseId: Int!): JSO<PERSON>
}
