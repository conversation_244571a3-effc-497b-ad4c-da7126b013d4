const { generateNextCharId } = require('@om/common/src/identifier');

const { model: ExperienceModel, ObjectId } = require('../experience.model');
const { queueCampaignChange } = require('../../../services/queue/changeLogQueue');
const { campaignChangeTypes } = require('../../changeLog/common');

const createExperience = async (_, { campaign }, userContext) => {
  const {
    userId: databaseId,
    superadmin,
    superAdminName,
    superAdminEmail,
    role,
    loginId,
  } = userContext;
  const count = await ExperienceModel.countDocuments({
    campaign,
    databaseId,
  });

  const experience = new ExperienceModel({
    name: `Experience ${generateNextCharId(count)}`,
    campaign,
    databaseId,
    priority: count,
  });

  const savedExperience = await experience.save();
  queueCampaignChange({
    changeContext: {
      campaignId: ObjectId(campaign),
      experienceId: savedExperience._id,
    },
    changeType: campaignChangeTypes.ADDED_EXPERIENCE,
    userContext: { userId: databaseId, superadmin, superAdminName, superAdminEmail, role, loginId },
  });

  return savedExperience;
};

module.exports = {
  createExperience,
};
