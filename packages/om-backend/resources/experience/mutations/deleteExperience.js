const { model: CampaignModel } = require('../../campaign/campaign.model');
const { ObjectId } = require('../experience.model');
const { handleNotFoundExperience } = require('../helpers/error');
const { revertToSimpleCampaign } = require('../helpers/handleConversions');
const { logicallyDeleteExperience } = require('../helpers/removes');
const { updateUserCaches } = require('../../campaign/helper');
const { queueCampaignChange } = require('../../../services/queue/changeLogQueue');
const { campaignChangeTypes } = require('../../changeLog/common');

const deleteExperience = async (_, { experienceId }, userContext) => {
  const {
    userId: databaseId,
    superadmin,
    superAdminName,
    superAdminEmail,
    role,
    loginId,
    log,
  } = userContext;
  const userCampaignModel = CampaignModel(databaseId);
  const experience = await logicallyDeleteExperience(experienceId, databaseId);

  handleNotFoundExperience(experience);

  queueCampaignChange({
    changeContext: {
      campaignId: ObjectId(experience.campaign),
      experienceId: ObjectId(experienceId),
    },
    changeType: campaignChangeTypes.REMOVED_EXPERIENCE,
    userContext: { userId: databaseId, superadmin, superAdminName, superAdminEmail, role, loginId },
  });

  try {
    await revertToSimpleCampaign({
      campaign: experience?.campaign,
      databaseId,
      userCampaignModel,
      userContext: {
        userId: databaseId,
        superadmin,
        superAdminName,
        superAdminEmail,
        role,
        loginId,
      },
    });

    await userCampaignModel.updateOne(
      {
        _id: experience.campaign,
      },
      {
        $set: {
          'variants.$[variant].status': 'deleted',
        },
      },
      { arrayFilters: [{ 'variant._id': { $in: experience.variants } }] },
    );

    updateUserCaches(databaseId);
  } catch (e) {
    log.error(e, 'deleteExperience operation failed');

    return { success: false };
  }

  return { success: true };
};

module.exports = {
  deleteExperience,
};
