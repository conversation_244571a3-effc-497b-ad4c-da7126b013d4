const faker = require('faker');
const mongoose = require('mongoose');
const moment = require('moment');
const { model: AccountModel } = require('../../resources/account/account.model');
const { model: campaignModel } = require('../../resources/campaign/campaign.model');
const { model: StatisticsModel } = require('../../resources/statistics/statistics.model');
const { model: TemplateModel } = require('../../resources/template/template.model');
const { getRandomIntInclusive } = require('../../util/random');
const { getDatabaseIds } = require('./helper');
const log = require('../../logger').child({ script: __filename });

module.exports = async (config) => {
  const databaseIds = await getDatabaseIds();
  const templates = await TemplateModel.find({}, { _id: 1, name: 1 });
  let campaignCounter = 0;

  for (let i = 0; i < databaseIds.length; i++) {
    const CampaignModel = campaignModel(databaseIds[i]);

    const domains = [];

    for (let i = 0; i < config.campaignCount; i++) {
      domains.push({
        _id: mongoose.Types.ObjectId(),
        domain: faker.internet.url(),
        lastRequestDate: new Date(),
        v3LastRequestDate: new Date(),
      });
    }

    await AccountModel.findOneAndUpdate(
      { databaseId: databaseIds[i] },
      { 'settings.domains': domains },
    );

    const campaigns = [];
    let variantId = 0;

    for (let i = 0; i < config.campaignCount; i++) {
      // console.log('Generating variants')
      const variants = [];
      for (
        let j = 0;
        j < getRandomIntInclusive(config.variantsInterval.min, config.variantsInterval.max);
        j++
      ) {
        variants.push({
          id: variantId,
          name: faker.random.word(),
          template: {},
        });

        variantId++;
      }
      // console.log('variants', variants)

      // console.log('Generating events')
      // let events = []
      // for (let j = 0; j < getRandomIntInclusive(config.eventsInterval.min, config.eventsInterval.max); j++) {
      //   events.push({
      //     type: faker.random.arrayElement(CampaignModel.schema.path('settings.events.0.type').enumValues),
      //     device: faker.random.arrayElement(CampaignModel.schema.path('settings.events.0.device').enumValues),
      //     options: { status: true }
      //   })
      // }
      // console.log('events', events)

      // console.log('Generating frontend rules')
      // let frontendRules = []
      // for (let j = 0; j < getRandomIntInclusive(config.frontendRulesInterval.min, config.frontendRulesInterval.max); j++) {
      //   frontendRules.push({
      //     type: faker.random.arrayElement(CampaignModel.schema.path('settings.frontendRules.0.type').enumValues),
      //     options: {}
      //   })
      // }
      // console.log('frontendRules', frontendRules)

      const template = faker.random.arrayElement(templates);

      campaigns.push({
        templateName: template.name,
        templateId: template._id,
        name: faker.random.word(),
        domain: 'asdf.com',
        domainId: domains[i]._id,
        id: campaignCounter,
        version: faker.random.number({ min: 1, max: 2 }),
        device: faker.random.arrayElement(CampaignModel.schema.path('device').enumValues),
        status: faker.random.arrayElement(['active', 'inactive']),
        variants,
        // settings: {
        //   events,
        //   frontendRules
        // },
        // events: events,
        // frontendRules: frontendRules,
        schedule: '',
      });

      log.info(`Campaign with id ${campaignCounter} is created.`);

      campaignCounter++;
      // console.log('campaign', campaign)
    }

    const insertedCampaigns = await CampaignModel.insertMany(campaigns);
    const camCount = insertedCampaigns.reduce((acc, campaign) => {
      return campaign.status !== 'deleted' && campaign.status !== 'archived' ? acc + 1 : acc;
    }, 0);
    await AccountModel.findOneAndUpdate(
      { databaseId: databaseIds[i] },
      { 'campaignInfos.campaignCount': camCount },
    );

    for (let i = 0; i < insertedCampaigns.length; i++) {
      let sumOfAppearances = 0;
      let sumOfConversions = 0;
      const statistics = [];

      for (let j = 0; j < insertedCampaigns[i].variants.length; j++) {
        let variantAppearances = 0;
        let variantConversions = 0;

        for (
          let day = moment('2018-01-01 00:00:00');
          day.isBefore(moment().add(1, 'days'));
          day.add(1, 'days')
        ) {
          const impressions = faker.random.number(1000);
          const conversions = faker.random.number(impressions / 10);
          variantAppearances += impressions;
          variantConversions += conversions;
          sumOfAppearances += impressions;
          sumOfConversions += conversions;

          statistics.push({
            databaseId: databaseIds[i],
            period: day.toISOString(),
            campaign: insertedCampaigns[i]._id,
            variant: insertedCampaigns[i].variants[j]._id,
            impressions,
            conversions,
          });
        }
        await CampaignModel.findOneAndUpdate(
          { 'variants._id': insertedCampaigns[i].variants[j]._id },
          {
            $set: {
              'variants.$.impressions': variantAppearances,
              'variants.$.conversions': variantConversions,
            },
          },
        );
      }

      await StatisticsModel.insertMany(statistics);
      await CampaignModel.findOneAndUpdate(
        { domain: insertedCampaigns[i].domain },
        { $set: { impressions: sumOfAppearances, conversions: sumOfConversions } },
      );
    }
  }
  log.info('----- All Campaigns & Statistics generated -----');

  // process.exit(0)
};
