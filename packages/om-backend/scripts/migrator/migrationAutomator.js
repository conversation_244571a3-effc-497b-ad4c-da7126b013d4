const axios = require('axios');
const fs = require('fs');
const util = require('util');

const { featureFlags } = require('../../helpers/migration/util');
require('dotenv').config({ path: '../../.env' });

const environments = {
  production: 'https://backend.optimonk.com',
  qa1: 'https://qa1-backend.optimonk.com',
  stage1: 'https://staging-backend.optimonk.com',
  stage2: 'https://staging2-backend.optimonk.com',
  stage3: 'https://staging3-backend.optimonk.com',
  stage4: 'https://staging4-backend.optimonk.com',
  stage5: 'https://staging5-backend.optimonk.com',
  stage6: 'https://staging6-backend.optimonk.com',
};

const config = {
  baseUrl: 'https://backend.optimonk.dev',
  mode: 'migrate',
  migrationType: '',
  accountType: null,
  delayBetweenMigration: 0,
  batchLimit: 200,
  nextBatchDelay: 2000,
  numberOfInstances: 1,
};

function getArgs() {
  const args = {};
  process.argv.slice(2, process.argv.length).forEach((arg) => {
    if (arg.slice(0, 2) === '--') {
      const longArg = arg.split('=');
      const longArgFlag = longArg[0].slice(2, longArg[0].length);
      const longArgValue = longArg.length > 1 ? longArg[1] : true;
      args[longArgFlag] = longArgValue;
    } else if (arg[0] === '-') {
      const flags = arg.slice(1, arg.length).split('');
      flags.forEach((flag) => {
        args[flag] = true;
      });
    }
  });
  return args;
}

const args = getArgs();
if (!args.type || !featureFlags[args.type]) {
  console.error(
    `Migration type is not defined or not available! Your choice: ${
      args.type
    }. Available: ${Object.keys(featureFlags)}`,
  );
  process.exit(1);
}
config.migrationType = args.type;
config.accountType = args.accountType;
config.numberOfInstances = args.instances || 1;

if (args.limit && !Number.isNaN(args.limit)) {
  config.batchLimit = Number.parseInt(args.limit, 10);
}

if (args.cleanup) {
  config.mode = 'cleanup';
}

if (args.env) {
  if (!environments[args.env]) {
    console.error(
      `Environment is not available! Your choice: ${args.env}. Available: ${Object.keys(
        environments,
      )}`,
    );
    process.exit(1);
  }

  config.baseUrl = environments[args.env];
}
const log_file = fs.createWriteStream(
  `${__dirname}/logs/migration_${config.migrationType}_${new Date().toISOString()}.log`,
  { flags: 'w' },
);

function logInfo(message) {
  console.log(message);
  log_file.write(`INFO: ${util.format(message)}\n`);
}

function logError(message) {
  console.error(message);
  log_file.write(`ERROR: ${util.format(message)}\n`);
}

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'; // Little hack for self generated cert
const cron_token = process.env.cron_token;
const apiClient = axios.create({
  baseURL: `${config.baseUrl}/migrator/${config.migrationType}`,
});

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function getUsersForMigration() {
  return apiClient.get(
    `/?cron_token=${cron_token}&limit=${config.batchLimit}&method=${config.mode}${
      config.accountType ? `&accountType=${config.accountType}` : ''
    }`,
  );
}

async function cleanupUser(userId) {
  return apiClient.delete(`/?cron_token=${cron_token}`, {
    data: {
      userId,
    },
  });
}

async function migrateUser(userId) {
  return apiClient.post(`/?cron_token=${cron_token}`, {
    userId,
  });
}

const sharedState = {
  users: [],
  isFetchingUsers: false,
};

async function fetchUsers() {
  if (sharedState.isFetchingUsers || sharedState.users.length > 0) return;

  logInfo(`Request users for ${config.mode}`);
  sharedState.isFetchingUsers = true;
  try {
    await sleep(config.nextBatchDelay);

    const usersRequest = await getUsersForMigration();
    if (!Array.isArray(usersRequest.data)) {
      logError('Getting users failed because bad data received');
      return;
    }
    sharedState.users.push(...usersRequest.data);
    logInfo(`${sharedState.users.length} users found for ${config.mode}`);
  } catch (err) {
    if (err.response) {
      logError(err.response.data);
    }
    logError(err.message);
  } finally {
    sharedState.isFetchingUsers = false;
  }
}

async function waitForUsers(instance) {
  while (sharedState.isFetchingUsers) {
    logInfo(`Instance ${instance} waiting for users...`);
    await sleep(300);
  }
}

async function migrationManager(instance) {
  logInfo(`Starting instance ${instance}`);
  await fetchUsers();

  while (true) {
    if (sharedState.users.length === 0) {
      await waitForUsers(instance);
      if (sharedState.users.length === 0) {
        await fetchUsers();
      }
      if (sharedState.users.length === 0) {
        logInfo(`No more users to process. Instance ${instance} completed`);
        break;
      }
    }

    const user = sharedState.users.shift();
    if (!user) continue;

    logInfo(`Instance ${instance} processing userID: ${user}`);
    try {
      let response;
      if (config.mode === 'migrate') {
        response = await migrateUser(user);
      } else {
        response = await cleanupUser(user);
      }
      const result = response.data && response.data.result;
      if (result.error) {
        logError(
          `Instance ${instance} failed to ${config.mode} userID: ${user} message: ${result.message}`,
        );
      } else {
        logInfo(
          `Instance ${instance} successfully ${
            config.mode
          } userID: ${user}. Result: ${JSON.stringify(result)}`,
        );
      }
    } catch (err) {
      logError(`Instance ${instance} error: ${err.message}`);
      sharedState.users.push(user);
    }

    await sleep(config.delayBetweenMigration);
  }

  logInfo(`Instance ${instance} stopped`);
}

for (let i = 1; i <= config.numberOfInstances; i++) {
  migrationManager(i);
}
