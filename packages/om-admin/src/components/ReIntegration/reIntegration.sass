@import '@/sass/variables/_colors.sass'
.dashboard-alert
  .alert-block
    &-header
      display: flex
      flex-wrap: nowrap
      font-size: 14px
      color: $om-gray-700
      font-weight: 500
    &-title
      color: $om-gray-600
      font-size: 12px
    &-link
      font-size: 12px
      display: inline-block
      text-decoration: underline
.campaign
  &-alert, &-error
    .alert-block
      &-header
        display: flex
        flex-wrap: nowrap
        font-size: 14px
        color: $om-gray-700
        font-weight: 500
      &-title
        color: $om-gray-700
        font-size: 12px
        font-weight: 500
      &-description
        font-size: 12px
        color: $om-gray-600
        font-weight: 400
      &-link
        font-size: 12px
        display: inline-block
        text-decoration: underline
.re-integration
  padding: 3rem 0
  .container-fluid
    padding-bottom: 2.0625rem
  .alert-block-re-integration
    max-width: 53.5rem
    padding-left: 2.5rem
    .alert-block
      &-wrapper
        ul
          li
            font-size: 85%
      &-header
        display: flex
        flex-wrap: nowrap
        font-size: 14px
        color: $om-gray-700
        font-weight: 500
      &-title
        color: $om-gray-700
        font-size: 14px
      &-description
        color: $om-gray-600
        font-size: 12px
  .deprecated-integrations
    padding: 0.1875rem 0 0 2.5rem
    .title
      .heading-3
        padding: 0.75rem 0 0 1rem
        font-weight: 400
    .integrations
      padding: 0 0 0 1rem
      .deprecated
        height: 4rem
        width: 605px
        border-bottom: 1px solid #E3E5E8
        .name
          font-size: 20px
          font-weight: 500
          color: #23262A
          min-width: 14rem
          margin-right: 10px
          margin-top: auto
          margin-bottom: auto
        .marker
          font-size: 20px
          color: #B9BEC6
          line-height: 32px
        .reconnect
          margin-left: auto
          align-items: center
          display: flex
