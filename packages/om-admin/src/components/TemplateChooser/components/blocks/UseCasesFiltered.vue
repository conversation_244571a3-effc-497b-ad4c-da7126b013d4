<template lang="pug">
.use-cases-filtered
  .use-cases-filtered__block(v-for="useCase in useCases" v-if="getName(useCase)")
    CollectionPreviews.mb-10(
      forcedShowLink
      :showTitle="false"
      :name="getName(useCase)"
      :to="getRoute(useCase.slug)"
      :useCaseMap="useCaseMap"
      :templates="useCase.detailedTemplates"
      :subtitle="getDescription(useCase)"
    )
</template>

<script>
  import { mapState, mapGetters } from 'vuex';

  export default {
    name: 'UseCasesFiltered',
    components: {
      CollectionPreviews: () =>
        import('@/components/TemplateChooser/components/CollectionPreviews.vue'),
    },
    computed: {
      ...mapGetters('templateChooser', ['useCasesWithTemplates']),
      ...mapState('useCase', ['useCaseMap']),
      ...mapGetters('useCase', ['localizedUseCase', 'useCaseDescriptionByIdAndLocale']),
      useCases() {
        return this.useCasesWithTemplates;
      },
    },

    methods: {
      getName(useCase) {
        return this.localizedUseCase(useCase?._id)?.name;
      },
      getDescription(useCase) {
        return this.localizedUseCase(useCase._id)?.description;
      },
      getRoute(slug) {
        return {
          name: 'chooser-use-case',
          params: { slug },
        };
      },
    },
  };
</script>

<style lang="sass">
  .use-cases-filtered-title
    margin-bottom: 2.5rem
</style>
