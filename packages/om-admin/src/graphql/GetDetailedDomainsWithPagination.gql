query GetDetailedDomainsWithPagination($pagination: PaginationInput, $filter: String) {
  getDetailedDomainsWithPagination(pagination: $pagination, filter: $filter) {
    domains {
      _id
      domain
      inactive
      lastRequestDate
      v3LastRequestDate
      analytics {
        enabled
      }
      isShopifyAppExtensionActive
      crossDomainTrackingRole
      providerServiceIdOverride
      activeCampaigns {
        _id
        name
        conversions
        impressions
        conversionRate
        templateName
        version
        variants {
          previewURLs
        }
      }
    },
    count
  }
}
