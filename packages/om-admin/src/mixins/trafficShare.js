export default {
  methods: {
    roundToNDecimals({ number, decimalPlaces }) {
      return Math.round(number * Math.pow(10, decimalPlaces)) / Math.pow(10, decimalPlaces);
    },
    convertDecimalsUp({ number, decimalPlaces }) {
      return number * Math.pow(10, decimalPlaces);
    },
    reduceToNthDecimal({ number, decimalPlaces, reduceDecimalCount }) {
      return this.roundToNDecimals({
        number: number / Math.pow(10, reduceDecimalCount),
        decimalPlaces,
      });
    },
    getTrafficShareInPercentage(trafficShare, groupCount) {
      return this.roundToNDecimals({
        number: this.convertDecimalsUp({ number: trafficShare, decimalPlaces: 2 }) / groupCount,
        decimalPlaces: 1,
      });
    },
    isValidTrafficShare(urlVariants) {
      const trafficShareSum = urlVariants.reduce((sum, variant) => {
        sum += this.convertDecimalsUp({ number: variant.trafficShare, decimalPlaces: 4 });
        return sum;
      }, 0);

      const wholeTraffic = this.convertDecimalsUp({
        number: urlVariants.length,
        decimalPlaces: 4,
      });

      return trafficShareSum === wholeTraffic;
    },
  },
};
