<template lang="pug">
mixin backyard-near-column-resizer-top
  .backyard-near-column-resizer-top(
    @mouseenter="isMouseOnBackyard = true"
    @mouseleave="isMouseOnBackyard = false"
  )

mixin backyard-near-column-resizer-bottom
  .backyard-near-column-resizer-bottom(
    @mouseenter="isMouseOnBackyard = true"
    @mouseleave="isMouseOnBackyard = false"
  )

div
  .om-element-col-size-calculation-helper(:id="sizeCalculationHelperId")

  template(v-if="isHeightResizerEnabled && !isMouseNearTheResizer")
    template(v-if="isCenterPosition")
      +backyard-near-column-resizer-top
      +backyard-near-column-resizer-bottom
    template(v-else-if="isBottomPosition")
      +backyard-near-column-resizer-top
    template(v-else)
      +backyard-near-column-resizer-bottom

  template(
    v-if="isActiveResize || isMouseOnBackyard || (isHeightResizerEnabled && show && !isResizerBlockerInteraction && !isFullscreen)"
  )
    .om-column-height-resizer-border(v-if="isActiveResize || isMouseNearTheResizer")

    template(v-if="isCenterPosition")
      .om-column-height-resizer-top(
        @mouseenter="mouseNearTheResizer(true), changeDirection('top')"
        @mouseleave="mouseNearTheResizer(false)"
      )
        .om-column-height-resizer-svg-wrapper(
          v-if="isActiveResize || isMouseNearTheResizer"
          :data-track-property="elementDataTrackProperty"
          @mousedown.stop.prevent="handleMousedown"
        )
          resizer-v2(:type="replaceArrowDirectionByPosition(symbolDirectionType, 'top')")

      .om-column-height-resizer-bottom(
        @mouseenter="mouseNearTheResizer(true), changeDirection('bottom')"
        @mouseleave="mouseNearTheResizer(false)"
      )
        .om-column-height-resizer-svg-wrapper(
          v-if="isActiveResize || isMouseNearTheResizer"
          :data-track-property="elementDataTrackProperty"
          @mousedown.stop.prevent="handleMousedown"
        )
          resizer-v2(:type="replaceArrowDirectionByPosition(symbolDirectionType, 'bottom')")

    template(v-else)
      div(
        :class="isBottomPosition ? 'om-column-height-resizer-top' : 'om-column-height-resizer-bottom'"
        @mouseenter="mouseNearTheResizer(true), changeDirection('bottom')"
        @mouseleave="mouseNearTheResizer(false)"
      )
        .om-column-height-resizer-svg-wrapper(
          v-if="isActiveResize || isMouseNearTheResizer"
          :data-track-property="elementDataTrackProperty"
          @mousedown.stop.prevent="handleMousedown"
        )
          resizer-v2(
            :type="replaceArrowDirectionByPosition(symbolDirectionType, isBottomPosition ? 'top' : 'bottom')"
          )
</template>

<script>
  import ResizerV2 from '@/editor/components/svg/ResizerV2';
  import { debounce } from 'lodash-es';
  import { resizeTracker } from '@/services/userInteractionTracker/tracker';
  import { mapState, mapGetters } from 'vuex';
  import teaserMixin from '@/editor/mixins/teaser';
  import heightResizerMixin from './mixins/heightResizer';
  import teaserResizeMixin from './mixins/teaser';

  const MIN_HEIGHT = 25;

  export default {
    components: { ResizerV2 },

    mixins: [heightResizerMixin, teaserMixin, teaserResizeMixin],

    props: {
      show: {
        type: Boolean,
        default: false,
      },
      structuralElement: {
        type: Object,
        default: () => {},
      },
      onlyMobile: {
        type: Boolean,
        default: false,
      },
      isResizerBlockerInteraction: {
        type: Boolean,
        default: false,
      },
      parentRefs: {
        type: Object,
      },
    },

    data() {
      return {
        isMouseNearTheResizer: false,
        isMouseOnBackyard: false,
        heightResizer: null,
        sizeMultiplier: null,
        minHeight: null,
        hasStuckCanvasOnTopByOmLarge: false,
      };
    },

    computed: {
      ...mapState(['selectedPage']),
      ...mapGetters(['isFullscreen']),
      elementDataTrackProperty() {
        return `component:OmCol|change:x.elements.OmCol.minHeight|device:${this.device}`;
      },
    },

    mounted() {
      this.createHeightResizeInstance();

      window.addEventListener('mousemove', this.handleMousemove, true);
      window.addEventListener('mouseup', this.handleMouseup, true);
      this.$bus?.$on?.('workspaceLeaving', this.handleMouseup, true);

      this.setTeaserComputedHeight();
    },

    beforeDestroy() {
      this.heightResizer = null;

      window.removeEventListener('mousemove', debounce(this.handleMousemove, 50), true);
      window.removeEventListener('mouseup', this.handleMouseup, true);
      this.$bus?.$off?.('workspaceLeaving', this.handleMouseup, true);
    },

    methods: {
      pageCoordinate(e) {
        return this.calculatePageCoordinate(e, this.selectedPage.data.mobilePosition);
      },
      handleMousedown(e) {
        if (!this.heightResizer) return;

        this.sizeMultiplier = this.calculateSizeMultiplier();
        this.minHeight = this.minHeightCalculation(this.sizeMultiplier);

        this.heightResizer.startResize(this.pageCoordinate(e));
        this.$bus?.$emit?.('selectElement', this.structuralElement.uid);
        this.$bus?.$emit?.('resizerInteraction', true);
        resizeTracker.track(e);
      },

      handleMousemove(e) {
        if (!this.isActiveResize) return;

        if (this.isMaxHeightReached(this.sizeMultiplier))
          this.heightResizer.updateMouseYLimit(this.pageCoordinate(e));

        this.updateResizerData(this.sizeMultiplier, this.minHeight);
        this.heightResizer.change(this.pageCoordinate(e));
        this.structuralDomElement.style['min-height'] = `${this.heightResizer
          .getData()
          .getMinHeight()}em`;

        this.setTeaserComputedHeight();
      },

      handleMouseup() {
        if (!this.isActiveResize) return;

        this.heightResizer.stopResize();

        this.$bus?.$emit?.('updateSettings', this.heightResizer.getValues());

        this.structuralDomElement.style['min-height'] = null;
        this.$bus?.$emit?.('resizerInteraction', false);
        this.sizeMultiplier = null;
        this.minHeight = null;
      },

      minHeightCalculation(multiplier) {
        if (this.isNano) return 0;
        const minHeight = this.sumColumnHeight(this.structuralElement.uid, multiplier);
        return minHeight > MIN_HEIGHT ? minHeight : MIN_HEIGHT;
      },
    },
  };
</script>

<style lang="sass">
  .backyard-near-column-resizer
    &-top,
    &-bottom
      position: absolute
      z-index: 99
      height: 5px
      width: 36px
      margin-left: -18px
      left: 50%
    &-top
      top: 3px
    &-bottom
      bottom: 0

  .om-element-col-size-calculation-helper
    height: 0
    width: 0
    position: absolute
    z-index: -1
    opacity: 0

  .om-column-height-resizer

    &-border
      z-index: 100
      position: absolute
      padding: 0
      margin: 0
      top: 0
      left: 0
      height: 100%
      width: 100%
      border: 1px solid #ed5a29

    &-bottom,
    &-top
      z-index: 101
      position: absolute
      width: 100%
      height: 20px
      left: 0
      display: flex
      align-items: center
      justify-content: center
      padding: 0
      margin: 0

    &-bottom
      bottom: -10px
      .om-mobile-swipe &
        bottom: 0

    &-top
      top: -10px

    &-svg-wrapper
      cursor: move
      cursor: grab
      cursor: -moz-grab
      cursor: -webkit-grab

      svg
        &:active
          cursor: grabbing
          cursor: -moz-grabbing
          cursor: -webkit-grabbing
</style>
