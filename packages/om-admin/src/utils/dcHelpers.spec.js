import { getRedirectDomain } from '@/utils/dcHelpers';

describe('New DC campaign redirect domain getter test', () => {
  const inputs = [
    { input: 'www.test.com', expected: 'www.test.com' },
    { input: 'https://www.test.com', expected: 'www.test.com' },
    { input: 'http://www.test.com', expected: 'www.test.com' },
    { input: 'http://www.test.com/collections/all', expected: 'www.test.com' },
  ];
  test.each(inputs)('Test different inputs $input', ({ input, expected }) => {
    expect(getRedirectDomain(input)).toEqual(expected);
  });

  test('Test non-input case', () => {
    const campaignDomain = 'www.test.com';
    expect(getRedirectDomain(null, campaignDomain)).toEqual(campaignDomain);
  });
});
