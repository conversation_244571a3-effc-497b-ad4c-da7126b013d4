.feedback
  &-variant-title
    color: #AAB1C1
    font-size: 0.875rem
    display: flex
    justify-content: flex-end
    + span > span >.brand-select-popover
      margin-left: -1px
      .brand-btn-select-popover-disable
        background: rgb(114, 174, 255)
  &-card-body
    padding-left: 1.5rem
    padding-right: 1.5rem
    width: 100%
  &-progress
    height: 2rem
    + div 
      word-break: break-word
  &-icon-style
    height: 2rem
    color: #72AEFF
    font-size: 1rem
    display: flex
    align-items: center
    &.smiley 
      font-size: 2rem
    &.yesno
      font-size: 2rem
.stars
  &-1:after
    content: '\f005\f006\f006\f006\f006'
    font-family: FontAwesome
    letter-spacing: 2px
  &-2:after
    content: '\f005\f005\f006\f006\f006'
    font-family: FontAwesome
    letter-spacing: 2px

  &-3:after
    content: '\f005\f005\f005\f006\f006'
    font-family: FontAwesome
    letter-spacing: 2px

  &-4:after
    content: '\f005\f005\f005\f005\f006'
    font-family: FontAwesome
    letter-spacing: 2px

  &-5:after
    content: '\f005\f005\f005\f005\f005'
    font-family: FontAwesome
    letter-spacing: 2px

.smiley
  &-good:after
    content: '\f118'
    font-family: FontAwesome

  &-neutral:after
    content: '\f11a'
    font-family: FontAwesome

  &-bad:after
    content: '\f119'
    font-family: FontAwesome

.yesno
  &-yes:after
    content: '\f087'
    font-family: FontAwesome

  &-no:after
    content: '\f088'
    font-family: FontAwesome