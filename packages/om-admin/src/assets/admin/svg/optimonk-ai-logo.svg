<svg width="368" height="138" viewBox="0 0 368 138" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_1_2)">
<rect x="249.503" y="41.0019" width="67.0233" height="67.0233" rx="10.8148" fill="white"/>
<rect x="249.503" y="41.0019" width="67.0233" height="67.0233" rx="10.8148" fill="url(#paint0_linear_1_2)"/>
<rect x="250.335" y="41.8338" width="65.3594" height="65.3594" rx="9.98294" stroke="white" stroke-width="1.66382"/>
<rect x="250.335" y="41.8338" width="65.3594" height="65.3594" rx="9.98294" stroke="url(#paint1_linear_1_2)" stroke-width="1.66382"/>
</g>
<g filter="url(#filter1_d_1_2)">
<path d="M284.708 88.4308L283.197 83.8398H271.973L270.462 88.4308H262.907L273.182 59.2155H281.988L292.263 88.4308H284.708ZM274.045 77.5794H281.125L277.585 66.8532L274.045 77.5794Z" fill="url(#paint2_linear_1_2)"/>
<path d="M294.839 59.2155H301.746V88.4308H294.839V59.2155Z" fill="url(#paint3_linear_1_2)"/>
<path d="M284.313 88.5608L284.407 88.8468H284.708H292.263H292.85L292.655 88.2928L282.381 59.0775L282.283 58.7996H281.988H273.182H272.887L272.789 59.0775L262.515 88.2928L262.32 88.8468H262.907H270.462H270.763L270.857 88.5608L272.274 84.2558H282.896L284.313 88.5608ZM294.839 58.7996H294.423V59.2155V88.4308V88.8468H294.839H301.746H302.162V88.4308V59.2155V58.7996H301.746H294.839ZM274.62 77.1635L277.585 68.1805L280.55 77.1635H274.62Z" stroke="white" stroke-width="0.831911"/>
</g>
<g filter="url(#filter2_dd_1_2)">
<path d="M330.084 44.9323C330.084 44.6581 329.862 44.4358 329.587 44.4358C329.313 44.4358 329.091 44.6581 329.091 44.9323C329.091 46.8516 327.535 48.4076 325.616 48.4076C325.342 48.4076 325.119 48.6298 325.119 48.904C325.119 49.1782 325.342 49.4005 325.616 49.4005C327.535 49.4005 329.091 50.9564 329.091 52.8758C329.091 53.15 329.313 53.3722 329.587 53.3722C329.862 53.3722 330.084 53.15 330.084 52.8758C330.084 50.9564 331.64 49.4005 333.559 49.4005C333.833 49.4005 334.056 49.1782 334.056 48.904C334.056 48.6298 333.833 48.4076 333.559 48.4076C331.64 48.4076 330.084 46.8516 330.084 44.9323Z" fill="url(#paint4_linear_1_2)"/>
</g>
<g filter="url(#filter3_dd_1_2)">
<path d="M335.774 31.7185C336.027 31.7185 336.232 31.9237 336.232 32.1768C336.232 33.1892 337.053 34.0099 338.065 34.0099C338.319 34.0099 338.524 34.215 338.524 34.4681C338.524 34.7212 338.319 34.9264 338.065 34.9264C337.053 34.9264 336.232 35.7471 336.232 36.7595C336.232 37.0126 336.027 37.2178 335.774 37.2178C335.521 37.2178 335.316 37.0126 335.316 36.7595C335.316 35.7471 334.495 34.9264 333.483 34.9264C333.23 34.9264 333.024 34.7212 333.024 34.4681C333.024 34.215 333.23 34.0099 333.483 34.0099C334.495 34.0099 335.316 33.1892 335.316 32.1768C335.316 31.9237 335.521 31.7185 335.774 31.7185Z" fill="url(#paint5_linear_1_2)"/>
</g>
<g filter="url(#filter4_dd_1_2)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M322.85 30.4812C322.85 30.2154 322.635 30 322.369 30C322.103 30 321.888 30.2154 321.888 30.4812C321.888 33.936 319.087 36.7367 315.632 36.7367C315.4 36.7367 315.206 36.9016 315.161 37.1209C315.154 37.1522 315.151 37.1847 315.151 37.2179C315.151 37.467 315.34 37.672 315.583 37.6966C315.599 37.6982 315.616 37.6991 315.632 37.6991C319.087 37.6991 321.888 40.4998 321.888 43.9546C321.888 44.2203 322.103 44.4358 322.369 44.4358C322.635 44.4358 322.85 44.2203 322.85 43.9546C322.85 40.4998 325.651 37.6991 329.106 37.6991C329.371 37.6991 329.587 37.4836 329.587 37.2179C329.587 36.9521 329.371 36.7367 329.106 36.7367C325.651 36.7367 322.85 33.936 322.85 30.4812Z" fill="url(#paint6_linear_1_2)"/>
</g>
<g filter="url(#filter5_dd_1_2)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M99.5498 54.5362C99.5498 52.0329 101.671 49.9351 104.201 49.9351C106.732 49.9351 108.853 52.0329 108.853 54.5362C108.853 57.0395 106.732 59.1373 104.201 59.1373C101.671 59.1373 99.5498 57.0395 99.5498 54.5362ZM221.228 77.5628L229.498 88.8557H238.285L228.622 76.0804L238.024 63.298H228.982L221.228 74.4931V51.159L213.48 53.4875V88.8557H221.228V77.5628ZM26.8105 52.3616C16.4262 52.3616 8 60.438 8 70.9687C8 81.4995 16.4262 89.5758 26.8105 89.5758C37.1948 89.5758 45.621 81.4995 45.621 70.9687C45.621 60.438 37.1948 52.3616 26.8105 52.3616ZM26.8034 60.3331C32.6989 60.3331 37.3503 64.5775 37.3503 70.9687C37.3503 77.3599 32.6918 81.6043 26.8034 81.6043C20.915 81.6043 16.2636 77.3599 16.2636 70.9687C16.2636 64.5775 20.9079 60.3331 26.8034 60.3331ZM64.5515 89.5757C71.3236 89.5757 76.901 83.695 76.901 76.0801V76.0731C76.901 68.4582 71.3236 62.5775 64.5515 62.5775C61.0877 62.5775 58.557 63.7523 56.8534 65.6962V61.3818L49.0988 63.7173V99.0855H56.8534V86.457C58.557 88.4009 61.0877 89.5757 64.5515 89.5757ZM62.9963 69.8358C66.5591 69.8358 69.1463 72.2412 69.1463 76.0731C69.1463 79.905 66.5662 82.3104 62.9963 82.3104C59.4265 82.3104 56.8463 79.905 56.8463 76.0731C56.8463 72.2412 59.4336 69.8358 62.9963 69.8358ZM90.6006 70.6612V79.7095C90.6006 81.9052 92.5163 82.115 95.9235 81.9052V88.8558C85.7937 89.8767 82.8459 86.8629 82.8459 79.7026V70.6542H78.7106V63.2911H82.8459V56.5223L90.6006 54.2218V63.2911H95.9235V70.6542H90.6006V70.6612ZM100.327 63.298H108.075V88.8557H100.327V63.298ZM150.043 88.8557H141.773V68.053L132.421 83.2338H131.487L122.135 68.053V88.8557H113.865V53.075H122.135L131.954 68.969L141.773 53.075H150.043V88.8557ZM167.263 62.5847C159.671 62.5847 153.571 68.4654 153.571 76.0802C153.571 83.6951 159.664 89.5758 167.263 89.5758C174.863 89.5758 180.956 83.6951 180.956 76.0802C180.956 68.4654 174.856 62.5847 167.263 62.5847ZM167.263 70.0527C170.621 70.0527 173.208 72.4511 173.208 76.0802C173.208 79.7094 170.628 82.1148 167.263 82.1148C163.899 82.1148 161.319 79.7094 161.319 76.0802C161.319 72.4511 163.906 70.0527 167.263 70.0527ZM209.076 73.1647V88.8559H201.321V74.2905C201.321 71.1229 199.307 69.6404 196.826 69.6404C193.984 69.6404 192.019 71.2767 192.019 74.9058V88.8629H184.271V63.6968L192.019 61.3892V65.7036C193.418 63.8157 195.998 62.585 199.413 62.585C204.63 62.585 209.076 66.263 209.076 73.1647Z" fill="url(#paint7_linear_1_2)"/>
</g>
<defs>
<filter id="filter0_dd_1_2" x="220.386" y="11.885" width="125.257" height="125.257" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.5584"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.4872"/>
<feGaussianBlur stdDeviation="9.15102"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1_2" result="effect2_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1_2" result="shape"/>
</filter>
<filter id="filter1_d_1_2" x="260.069" y="57.5517" width="44.1731" height="34.2067" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.831911"/>
<feGaussianBlur stdDeviation="0.831911"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.137255 0 0 0 0 0.121569 0 0 0 0 0.12549 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_2" result="shape"/>
</filter>
<filter id="filter2_dd_1_2" x="296.002" y="15.3189" width="67.1702" height="67.1702" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.5584"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.4872"/>
<feGaussianBlur stdDeviation="9.15102"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1_2" result="effect2_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1_2" result="shape"/>
</filter>
<filter id="filter3_dd_1_2" x="303.907" y="2.60158" width="63.7331" height="63.7331" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.5584"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.4872"/>
<feGaussianBlur stdDeviation="9.15102"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1_2" result="effect2_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1_2" result="shape"/>
</filter>
<filter id="filter4_dd_1_2" x="286.034" y="0.883102" width="72.6696" height="72.6696" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="14.5584"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7.4872"/>
<feGaussianBlur stdDeviation="9.15102"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1_2" result="effect2_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1_2" result="shape"/>
</filter>
<filter id="filter5_dd_1_2" x="0.512797" y="44.2764" width="245.259" height="64.1248" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.82843"/>
<feGaussianBlur stdDeviation="3.7436"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.07978"/>
<feGaussianBlur stdDeviation="2.28776"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1_2" result="effect2_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1_2" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_2" x1="283.014" y1="41.0019" x2="283.014" y2="108.025" gradientUnits="userSpaceOnUse">
<stop stop-color="#ED5A29" stop-opacity="0.5"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint1_linear_1_2" x1="283.014" y1="41.0019" x2="283.014" y2="119.272" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#ED5A29" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint2_linear_1_2" x1="262.718" y1="59.4144" x2="301.683" y2="88.3267" gradientUnits="userSpaceOnUse">
<stop stop-color="#7C10EE"/>
<stop offset="1" stop-color="#ED5A29"/>
</linearGradient>
<linearGradient id="paint3_linear_1_2" x1="262.718" y1="59.4144" x2="301.683" y2="88.3267" gradientUnits="userSpaceOnUse">
<stop stop-color="#7C10EE"/>
<stop offset="1" stop-color="#ED5A29"/>
</linearGradient>
<linearGradient id="paint4_linear_1_2" x1="329.561" y1="44.4358" x2="329.304" y2="53.3649" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F6AD95"/>
</linearGradient>
<linearGradient id="paint5_linear_1_2" x1="335.774" y1="31.8362" x2="335.616" y2="37.331" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F6AD95"/>
</linearGradient>
<linearGradient id="paint6_linear_1_2" x1="322.369" y1="30" x2="321.955" y2="44.4239" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F6AD95"/>
</linearGradient>
<linearGradient id="paint7_linear_1_2" x1="123.143" y1="49.9351" x2="123.143" y2="99.0672" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CFCFCF"/>
</linearGradient>
</defs>
</svg>
